@extends('backend.layouts.app')

@section('title')
    Messages personnalisés - {{ $module_action }}
@endsection

@push('after-styles')
    <link rel="stylesheet" href="{{ asset('vendor/datatable/datatables.min.css') }}">
@endpush

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h4 class="card-title mb-0">
            <i class="fas fa-history me-2"></i>
            {{ $module_action }}
        </h4>
        <a href="{{ route('backend.custom-notifications.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Nouveau message
        </a>
    </div>
    
    <div class="card-body">
        <div class="table-responsive">
            <table id="notificationsTable" class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Titre</th>
                        <th>Message</th>
                        <th>Destinataire</th>
                        <th>Canaux</th>
                        <th>Options</th>
                        <th>Date d'envoi</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
@endsection

@push('after-scripts')
<script src="{{ asset('vendor/datatable/datatables.min.js') }}"></script>
<script>
$(document).ready(function() {
    $('#notificationsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: '{{ route("backend.custom-notifications.index_data") }}',
        columns: [
            {
                data: 'title',
                name: 'title',
                render: function(data, type, row) {
                    return '<strong>' + data + '</strong>';
                }
            },
            {
                data: 'message',
                name: 'message',
                render: function(data, type, row) {
                    return '<span class="text-muted">' + data + '</span>';
                }
            },
            {
                data: 'recipient',
                name: 'recipient'
            },
            {
                data: 'channels',
                name: 'channels',
                render: function(data, type, row) {
                    let badges = '';
                    const channels = data.split(', ');
                    channels.forEach(function(channel) {
                        if (channel === 'Push') {
                            badges += '<span class="badge bg-primary me-1"><i class="fas fa-mobile-alt me-1"></i>' + channel + '</span>';
                        } else if (channel === 'Email') {
                            badges += '<span class="badge bg-success me-1"><i class="fas fa-envelope me-1"></i>' + channel + '</span>';
                        }
                    });
                    return badges;
                }
            },
            {
                data: 'extras',
                name: 'extras',
                render: function(data, type, row) {
                    if (data === '-') return '<span class="text-muted">-</span>';
                    let badges = '';
                    const extras = data.split(', ');
                    extras.forEach(function(extra) {
                        if (extra === 'Icône') {
                            badges += '<span class="badge bg-info me-1"><i class="fas fa-image me-1"></i>' + extra + '</span>';
                        } else if (extra === 'Image') {
                            badges += '<span class="badge bg-warning me-1"><i class="fas fa-images me-1"></i>' + extra + '</span>';
                        } else if (extra === 'Lien') {
                            badges += '<span class="badge bg-secondary me-1"><i class="fas fa-link me-1"></i>' + extra + '</span>';
                        }
                    });
                    return badges;
                }
            },
            {
                data: 'created_at',
                name: 'created_at'
            }
        ],
        order: [[5, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/fr-FR.json'
        },
        pageLength: 25,
        responsive: true
    });
});
</script>
@endpush 