@extends('backend.layouts.app')

@section('title')
    Messages personnalisés - {{ $module_action }}
@endsection

@push('after-styles')
    <link rel="stylesheet" href="{{ asset('vendor/select2/css/select2.min.css') }}">
    <style>
        .recipient-options {
            display: none;
        }
        .recipient-options.active {
            display: block;
        }
        .message-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
        }
        .notification-channels {
            background: #e3f2fd;
            border-radius: 0.375rem;
            padding: 1rem;
        }
    </style>
@endpush

@section('content')
<div class="card">
    <div class="card-header">
        <h4 class="card-title mb-0">
            <i class="fas fa-paper-plane me-2"></i>
            {{ $module_action }}
        </h4>
    </div>
    
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        @endif

        <form action="{{ route('backend.custom-notifications.store') }}" method="POST" id="customNotificationForm">
            @csrf
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Titre du message -->
                    <div class="mb-3">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading me-1"></i>
                            Titre du message <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control @error('title') is-invalid @enderror" 
                               id="title" 
                               name="title" 
                               value="{{ old('title') }}" 
                               placeholder="Entrez le titre de votre message"
                               maxlength="255"
                               required>
                        @error('title')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Maximum 255 caractères</div>
                    </div>

                    <!-- Message -->
                    <div class="mb-3">
                        <label for="message" class="form-label">
                            <i class="fas fa-comment-alt me-1"></i>
                            Message <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('message') is-invalid @enderror" 
                                  id="message" 
                                  name="message" 
                                  rows="5" 
                                  placeholder="Rédigez votre message ici..."
                                  required>{{ old('message') }}</textarea>
                        @error('message')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            <span id="messageCount">0</span> caractères
                        </div>
                    </div>

                    <!-- Options avancées de notification -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cogs me-1"></i>
                                Options avancées de notification
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="large_icon" class="form-label">
                                            <i class="fas fa-image me-1"></i>
                                            Icône large (URL)
                                        </label>
                                        <input type="url" 
                                               class="form-control @error('large_icon') is-invalid @enderror" 
                                               id="large_icon" 
                                               name="large_icon" 
                                               value="{{ old('large_icon') }}" 
                                               placeholder="https://exemple.com/icon.png">
                                        @error('large_icon')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">URL de l'icône à afficher dans la notification</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="big_image" class="form-label">
                                            <i class="fas fa-image me-1"></i>
                                            Grande image (URL)
                                        </label>
                                        <input type="url" 
                                               class="form-control @error('big_image') is-invalid @enderror" 
                                               id="big_image" 
                                               name="big_image" 
                                               value="{{ old('big_image') }}" 
                                               placeholder="https://exemple.com/image.jpg">
                                        @error('big_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-text">URL de l'image à afficher dans la notification</div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="action_url" class="form-label">
                                    <i class="fas fa-link me-1"></i>
                                    Lien d'action
                                </label>
                                <input type="url" 
                                       class="form-control @error('action_url') is-invalid @enderror" 
                                       id="action_url" 
                                       name="action_url" 
                                       value="{{ old('action_url') }}" 
                                       placeholder="https://exemple.com/page">
                                @error('action_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">URL à ouvrir quand l'utilisateur clique sur la notification</div>
                            </div>
                        </div>
                    </div>

                    <!-- Destinataires -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-users me-1"></i>
                            Destinataires <span class="text-danger">*</span>
                        </label>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="radio" 
                                       name="recipient_type" 
                                       id="all_users" 
                                       value="all" 
                                       {{ old('recipient_type') === 'all' ? 'checked' : '' }}>
                                <label class="form-check-label" for="all_users">
                                    <strong>Tous les utilisateurs (Topic: voir_films_hd)</strong>
                                    <small class="text-muted d-block">Envoyer à tous les utilisateurs de l'application via le topic voir_films_hd</small>
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="radio" 
                                       name="recipient_type" 
                                       id="test_token" 
                                       value="token"
                                       {{ old('recipient_type') === 'token' ? 'checked' : '' }}>
                                <label class="form-check-label" for="test_token">
                                    <strong>Token de test</strong>
                                    <small class="text-muted d-block">Pour tester uniquement - Entrez un token FCM spécifique</small>
                                </label>
                            </div>
                        </div>

                        <!-- Champ token de test (visible uniquement si l'option token est sélectionnée) -->
                        <div class="recipient-options" id="token_input" style="display: none;">
                            <div class="mb-3">
                            <input type="text" 
                                   class="form-control @error('test_token') is-invalid @enderror" 
                                   name="test_token" 
                                       placeholder="Entrez le token FCM de test"
                                       value="{{ old('test_token') }}">
                            @error('test_token')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                                <div class="form-text">Token FCM pour tester la notification sur un appareil spécifique</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Canaux de notification -->
                    <div class="notification-channels mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-broadcast-tower me-1"></i>
                            Canaux d'envoi
                        </h6>
                        
                        <div class="form-check mb-2">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="send_push" 
                                   name="send_push" 
                                   value="1"
                                   {{ old('send_push') ? 'checked' : 'checked' }}>
                            <label class="form-check-label" for="send_push">
                                <i class="fas fa-mobile-alt me-1 text-primary"></i>
                                <strong>Notification Push</strong>
                                <small class="text-muted d-block">Notification sur l'application mobile</small>
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="send_email" 
                                   name="send_email" 
                                   value="1"
                                   {{ old('send_email') ? 'checked' : '' }}>
                            <label class="form-check-label" for="send_email">
                                <i class="fas fa-envelope me-1 text-success"></i>
                                <strong>Email</strong>
                                <small class="text-muted d-block">Notification par email</small>
                            </label>
                        </div>
                    </div>

                    <!-- Aperçu -->
                    <div class="message-preview">
                        <h6 class="mb-2">
                            <i class="fas fa-eye me-1"></i>
                            Aperçu de la notification
                        </h6>
                        <div class="border rounded p-3 bg-white">
                            <div class="d-flex align-items-start">
                                <div class="me-2">
                                    <img id="preview-icon" 
                                         src="https://via.placeholder.com/40x40/007bff/ffffff?text=📱" 
                                         alt="Icon" 
                                         class="rounded" 
                                         style="width: 40px; height: 40px; object-fit: cover;">
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold text-primary" id="preview-title">Titre du message</div>
                                    <div class="text-muted small mb-2" id="preview-message">Votre message apparaîtra ici...</div>
                                    <div id="preview-big-image" class="d-none">
                                        <img class="img-fluid rounded" style="max-width: 200px; max-height: 100px; object-fit: cover;">
                                    </div>
                                    <div id="preview-action-url" class="d-none">
                                        <small class="text-info">
                                            <i class="fas fa-link me-1"></i>
                                            <span>Lien d'action configuré</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <hr>

            <div class="d-flex justify-content-between align-items-center">
                <a href="{{ route('backend.custom-notifications.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    Historique
                </a>
                
                <button type="submit" class="btn btn-primary btn-lg" id="sendButton">
                    <i class="fas fa-paper-plane me-1"></i>
                    Envoyer le message
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('after-styles')
<style>
.recipient-options {
    display: none;
    margin-top: 15px;
}

.recipient-options.active {
    display: block;
}
</style>
@endpush

@push('after-scripts')
<script src="{{ asset('vendor/select2/js/select2.min.js') }}"></script>
<script>
$(document).ready(function() {
    // Initialiser Select2 pour la sélection d'utilisateurs
    $('#users_select').select2({
        placeholder: 'Sélectionnez les utilisateurs...',
        allowClear: true,
        width: '100%'
    });

    // Gestion des options de destinataires
    $('input[name="recipient_type"]').change(function() {
        const value = $(this).val();
        
        // Masquer toutes les options
        $('.recipient-options').removeClass('active');
        $('#users_select').prop('required', false).prop('disabled', true);
        $('#test_token_value').prop('required', false).prop('disabled', true).removeAttr('name');
        
        // Afficher l'option correspondante
        if (value === 'specific') {
            $('#specific_users_options').addClass('active');
            $('#users_select').prop('required', true).prop('disabled', false);
        } else if (value === 'token') {
            $('#test_token_options').addClass('active');
            $('#test_token_value').prop('required', true).prop('disabled', false).attr('name', 'test_token');
        }
    });

    // Déclencher le changement au chargement si une option est déjà sélectionnée
    $('input[name="recipient_type"]:checked').trigger('change');

    // Compteur de caractères pour le message
    $('#message').on('input', function() {
        const count = $(this).val().length;
        $('#messageCount').text(count);
        
        // Mettre à jour l'aperçu
        updatePreview();
    });

    // Mettre à jour l'aperçu du titre
    $('#title').on('input', function() {
        updatePreview();
    });

    // Mettre à jour l'aperçu pour les nouveaux champs
    $('#large_icon, #big_image, #action_url').on('input', function() {
        updatePreview();
    });

    // Fonction pour mettre à jour l'aperçu
    function updatePreview() {
        const title = $('#title').val() || 'Titre du message';
        const message = $('#message').val() || 'Votre message apparaîtra ici...';
        const largeIcon = $('#large_icon').val();
        const bigImage = $('#big_image').val();
        const actionUrl = $('#action_url').val();
        
        $('#preview-title').text(title);
        $('#preview-message').text(message.substring(0, 100) + (message.length > 100 ? '...' : ''));
        
        // Mettre à jour l'icône
        if (largeIcon) {
            $('#preview-icon').attr('src', largeIcon);
        } else {
            $('#preview-icon').attr('src', 'https://via.placeholder.com/40x40/007bff/ffffff?text=📱');
        }
        
        // Mettre à jour la grande image
        if (bigImage) {
            $('#preview-big-image img').attr('src', bigImage);
            $('#preview-big-image').removeClass('d-none');
        } else {
            $('#preview-big-image').addClass('d-none');
        }
        
        // Mettre à jour le lien d'action
        if (actionUrl) {
            $('#preview-action-url span').text('Lien: ' + actionUrl);
            $('#preview-action-url').removeClass('d-none');
        } else {
            $('#preview-action-url').addClass('d-none');
        }
    }

    // Validation du formulaire
    $('#customNotificationForm').on('submit', function(e) {
        const recipientType = $('input[name="recipient_type"]:checked').val();
        const sendPush = $('#send_push').is(':checked');
        const sendEmail = $('#send_email').is(':checked');

        if (!recipientType) {
            e.preventDefault();
            alert('Veuillez sélectionner le type de destinataires.');
            return false;
        }

        if (recipientType === 'specific' && $('#users_select').val().length === 0) {
            e.preventDefault();
            alert('Veuillez sélectionner au moins un utilisateur.');
            return false;
        }

        if (recipientType === 'token' && !$('#test_token_value').val().trim()) {
            e.preventDefault();
            alert('Veuillez saisir un token FCM de test.');
            return false;
        }

        if (!sendPush && !sendEmail) {
            e.preventDefault();
            alert('Veuillez sélectionner au moins un canal d\'envoi (Push ou Email).');
            return false;
        }

        // Confirmation avant envoi
        let confirmMessage;
        if (recipientType === 'all') {
            confirmMessage = 'Êtes-vous sûr de vouloir envoyer ce message à tous les utilisateurs ?';
        } else if (recipientType === 'token') {
            confirmMessage = 'Êtes-vous sûr de vouloir envoyer cette notification de test au token spécifié ?';
        } else {
            confirmMessage = 'Êtes-vous sûr de vouloir envoyer ce message aux utilisateurs sélectionnés ?';
        }

        if (!confirm(confirmMessage)) {
            e.preventDefault();
            return false;
        }

        // Désactiver le bouton pour éviter les doubles envois
        $('#sendButton').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Envoi en cours...');
    });

    // Initialiser l'aperçu
    updatePreview();

    // Gestion de l'affichage du champ token
    document.querySelectorAll('input[name="recipient_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            document.getElementById('token_input').style.display = 
                this.value === 'token' ? 'block' : 'none';
        });
    });

    // Afficher le champ token si l'option est déjà sélectionnée (en cas d'erreur de validation)
    if (document.querySelector('input[name="recipient_type"]:checked')?.value === 'token') {
        document.getElementById('token_input').style.display = 'block';
    }
});
</script>
@endpush 