<?php
// 1. Inclure le fichier de configuration dédié
// Ce fichier définit les constantes comme STREAMIT_DB_HOST, STREAMIT_TMDB_API_BEARER_TOKEN, etc.
require_once __DIR__ . '/../streamit_config.php';

// La ligne `use App\Config\Database;` n'est pas utilisée actuellement.
// Si vous avez une classe Database que vous souhaitez utiliser, assurez-vous qu'elle est chargée
// (par exemple, via un autoloader si vous utilisez Composer, ou un require_once explicite).
// Pour l'instant, la connexion PDO est faite directement.
// use App\Config\Database;

ini_set('error_log', __DIR__ . '/debug.log');
error_reporting(E_ALL);
ini_set('display_errors', 1); // À désactiver en production

header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *"); // À restreindre en production
header("Access-Control-Allow-Methods: POST");
// Ajouter X-API-Key si vous implémentez la protection par token
header("Access-Control-Allow-Headers: Content-Type, X-API-Key");

// --- Protection API (Exemple basique) ---
// Décommentez et adaptez si vous souhaitez utiliser STREAMIT_API_PROTECTION_TOKEN
/*
if (!isset($_SERVER['HTTP_X_API_KEY']) || $_SERVER['HTTP_X_API_KEY'] !== STREAMIT_API_PROTECTION_TOKEN) {
    http_response_code(403); // Forbidden
    echo json_encode(['error' => 'Accès non autorisé.']);
    error_log('Tentative d\'accès non autorisé à l\'API (clé API manquante ou incorrecte).');
    exit;
}
*/

// Récupérer les données JSON
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Logging initial (peut être réduit en production)
error_log('Début du script fetch_data_to_json.php');
// error_log('Input brut reçu : ' . $input);
// error_log('Données décodées : ' . print_r($data, true));

// Vérifier si le JSON est valide
if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['error' => 'JSON invalide : ' . json_last_error_msg()]);
    exit;
}

// Valider les données requises
if (!isset($data['tmdb_id'], $data['titre_fr'], $data['annee'], $data['duree'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Données manquantes (tmdb_id, titre_fr, annee, duree sont requis).']);
    exit;
}

// Construire la date de sortie au format YYYY-MM-DD
// Utiliser directement release_date si elle existe, sinon construire à partir de l'année
$releaseDate = isset($data['release_date']) && preg_match('/^\d{4}-\d{2}-\d{2}$/', $data['release_date'])
    ? $data['release_date']
    : $data['annee'] . '-01-01';

$genreMapping = [
    28 => 7, 12 => 8, 16 => 12, 35 => 10, 80 => 9, 99 => 11, 18 => 4, 10751 => 2, 14 => 13, 36 => 15, 27 => 16, 10402 => 21, 9648 => 28, 10749 => 5, 878 => 6, 10770 => 31, 53 => 1, 10752 => 3, 37 => 36,
    10759 => 51, 10765 => 52, 10766 => 47, 10767 => 48, 10768 => 49, 10763 => 50, 10764 => 38, 10762 => 41
];

function convertGenreIds($tmdbIds, $mapping) {
    $localIds = [];
    if (!is_array($tmdbIds)) return $localIds;
    foreach ($tmdbIds as $tmdbId) {
        if (isset($mapping[$tmdbId])) {
            $localIds[] = $mapping[$tmdbId];
        } else {
            error_log("Genre TMDB non mappé : " . $tmdbId);
        }
    }
    return array_unique($localIds);
}

// Fonction pour convertir la durée au format 00:00
function convertDuration($durationStr) {
    $duration = "00:00";

    // Patterns possibles: "2h 30min", "1h30m", "90min", "120 min", etc.
    if (preg_match('/(\d+)h\s*(\d+)(?:min|m)/i', $durationStr, $matches)) {
        // Format "2h 30min" ou "1h30m"
        $hours = intval($matches[1]);
        $minutes = intval($matches[2]);
        $duration = sprintf("%02d:%02d", $hours, $minutes);
    } elseif (preg_match('/(\d+)h/i', $durationStr, $matches)) {
        // Format "2h"
        $hours = intval($matches[1]);
        $duration = sprintf("%02d:00", $hours);
    } elseif (preg_match('/(\d+)\s*(?:min|m)/i', $durationStr, $matches)) {
        // Format "90min" ou "120 min"
        $totalMinutes = intval($matches[1]);
        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;
        $duration = sprintf("%02d:%02d", $hours, $minutes);
    } elseif (is_numeric($durationStr)) {
        // Format numérique simple (minutes)
        $totalMinutes = intval($durationStr);
        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;
        $duration = sprintf("%02d:%02d", $hours, $minutes);
    }

    return $duration;
}

// Fonction pour appeler des commandes Artisan
function artisan_call($command, $parameters = []) {
    $baseDir = dirname(__DIR__);
    
    // Charger l'autoloader de Laravel
    require_once $baseDir . '/vendor/autoload.php';
    
    // Charger l'application Laravel
    $app = require_once $baseDir . '/bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    
    // Exécuter la commande
    $status = $kernel->call($command, $parameters);
    
    // Terminer la requête
    $kernel->terminate(null, $status);
    
    return $status;
}

try {
    // Utiliser les constantes du fichier streamit_config.php
    $host = STREAMIT_DB_HOST;
    $dbname = STREAMIT_DB_NAME;
    $username = STREAMIT_DB_USER;
    $password = STREAMIT_DB_PASS;

    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, PDO::ATTR_EMULATE_PREPARES => false
    ]);

    $sqlCheckInitial = "SELECT id, tmdb_id, name, YEAR(release_date) as year FROM entertainments WHERE tmdb_id = :data_tmdb_id OR (name = :title AND YEAR(release_date) = :year)";
    $stmtCheckInitial = $pdo->prepare($sqlCheckInitial);
    $stmtCheckInitial->bindValue(':data_tmdb_id', $data['tmdb_id']);
    $stmtCheckInitial->bindValue(':title', $data['titre_fr']);
    $stmtCheckInitial->bindValue(':year', (int)$data['annee'], PDO::PARAM_INT);
    $stmtCheckInitial->execute();
    $potentialExistingMovie = $stmtCheckInitial->fetch(PDO::FETCH_ASSOC);

    if ($potentialExistingMovie) {
        $entertainment_id = $potentialExistingMovie['id'];
        $db_tmdb_id = $potentialExistingMovie['tmdb_id'];

        if ($db_tmdb_id == $data['tmdb_id']) {
            http_response_code(200);
            echo json_encode(['message' => 'Film ignoré (doublon direct basé sur tmdb_id).', 'status' => 'skipped_duplicate', 'existing_movie_id' => $entertainment_id]);
            error_log("Film ignoré (doublon direct tmdb_id: {$data['tmdb_id']}). ID existant: {$entertainment_id}");
            exit;
        }
        elseif (strtolower($potentialExistingMovie['name']) === strtolower($data['titre_fr']) && (int)$potentialExistingMovie['year'] === (int)$data['annee']) {
            // Si le titre et l'année correspondent mais pas l'ID
            if (strpos((string)$db_tmdb_id, 'tt') === 0) {
                // Si l'ID dans la base est un IMDB (commence par 'tt'), on vérifie via l'API TMDB
                error_log("Titre/Année correspondent, tmdb_id diffère. DB IMDB ID: {$db_tmdb_id}. Tentative de résolution via API TMDB.");

                $tmdb_api_bearer = STREAMIT_TMDB_API_BEARER_TOKEN; // Utilisation de la constante
                $tmdb_api_url = "https://api.themoviedb.org/3/movie/{$db_tmdb_id}/external_ids";
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $tmdb_api_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Ajout d'un timeout
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Bearer ' . $tmdb_api_bearer, 'accept: application/json']);
                $api_response_json = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curl_error = curl_error($ch);
                curl_close($ch);

                if ($curl_error) {
                    error_log("Erreur cURL appel API TMDB pour {$db_tmdb_id}: " . $curl_error);
                } elseif ($http_code == 200 && $api_response_json) {
                    $api_response_data = json_decode($api_response_json, true);
                    if (isset($api_response_data['id']) && $api_response_data['id'] == $data['tmdb_id']) {
                        error_log("API TMDB a confirmé la correspondance. DB IMDB ID {$db_tmdb_id} -> API TMDB ID {$api_response_data['id']}. Mise à jour du film ID {$entertainment_id}.");
                        $pdo->beginTransaction();
                        try {
                            $sqlUpdateTmdbId = "UPDATE entertainments SET tmdb_id = :new_tmdb_id WHERE id = :entertainment_id";
                            $stmtUpdateTmdbId = $pdo->prepare($sqlUpdateTmdbId);
                            $stmtUpdateTmdbId->bindValue(':new_tmdb_id', $data['tmdb_id']);
                            $stmtUpdateTmdbId->bindValue(':entertainment_id', $entertainment_id);
                            $stmtUpdateTmdbId->execute();

                            $sqlDeleteSources = "DELETE FROM sources WHERE sourceable_id = :entertainment_id AND sourceable_type = 'Modules\\\\Entertainment\\\\Models\\\\Entertainment'";
                            $stmtDeleteSources = $pdo->prepare($sqlDeleteSources);
                            $stmtDeleteSources->bindValue(':entertainment_id', $entertainment_id);
                            $stmtDeleteSources->execute();
                            $deletedSourcesCount = $stmtDeleteSources->rowCount();
                            error_log("{$deletedSourcesCount} anciennes sources supprimées pour l'entertainment ID {$entertainment_id}.");

                            $newSourcesAddedCount = 0;
                            if (isset($data['urls_alternatives']) && is_array($data['urls_alternatives'])) {
                                $sqlSource = "INSERT INTO sources (title, url, url_type, source_type, sourceable_id, sourceable_type, status, created_at, updated_at) VALUES (:title, :url, :url_type, :source_type, :sourceable_id, :sourceable_type, :status, NOW(), NOW())";
                                $stmtSource = $pdo->prepare($sqlSource);
                                
                                error_log("URLs alternatives trouvées: " . count($data['urls_alternatives']));
                                error_log("URLs alternatives: " . print_r($data['urls_alternatives'], true));
                                
                                foreach ($data['urls_alternatives'] as $url) {
                                    if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
                                        error_log("URL de source invalide ou vide pour entertainment {$entertainment_id}: " . $url);
                                        continue;
                                    }
                                    
                                    // Extraire le nom de domaine pour le titre
                                    $parsedUrl = parse_url($url);
                                    $domain = isset($parsedUrl['host']) ? $parsedUrl['host'] : 'embed';
                                    $domainParts = explode('.', $domain);
                                    $extractedTitle = $domainParts[0] ?? 'embed';
                                    
                                    try {
                                        $stmtSource->execute([
                                            ':title' => $extractedTitle,
                                            ':url' => $url,
                                            ':url_type' => 'embed',
                                            ':source_type' => 'both',
                                            ':sourceable_id' => $entertainment_id,
                                            ':sourceable_type' => 'Modules\\Entertainment\\Models\\Entertainment',
                                            ':status' => 'active'
                                        ]);
                                        $newSourcesAddedCount++;
                                        error_log("Source ajoutée avec succès: {$url} pour entertainment {$entertainment_id}");
                                    } catch (PDOException $e) {
                                        error_log("Erreur lors de l'ajout de la source {$url} pour entertainment {$entertainment_id}: " . $e->getMessage());
                                    }
                                }
                                error_log("{$newSourcesAddedCount} nouvelles sources ajoutées pour l'entertainment ID {$entertainment_id}.");
                                
                                // Vider le cache pour ce film
                                if (function_exists('artisan_call')) {
                                    artisan_call('cache:forget', ['key' => "movie_{$entertainment_id}"]);
                                    error_log("Cache vidé pour le film ID {$entertainment_id}");
                                }
                            }
                            $pdo->commit();
                            http_response_code(200);
                            echo json_encode(['message' => 'Film existant mis à jour avec nouveau tmdb_id et nouvelles sources.', 'status' => 'updated_existing', 'updated_movie_id' => $entertainment_id, 'new_sources_added' => $newSourcesAddedCount]);
                            exit;
                        } catch (Exception $e) {
                            $pdo->rollBack();
                            error_log("Erreur lors de la mise à jour du film après correspondance API: " . $e->getMessage());
                        }
                    } else {
                        error_log("API TMDB n'a pas confirmé la correspondance ou ID TMDB différent. API ID: " . ($api_response_data['id'] ?? 'N/A') . ", Data TMDB ID: " . $data['tmdb_id']);
                    }
                } else {
                    error_log("Erreur appel API TMDB pour {$db_tmdb_id}. Code: {$http_code}. Réponse: {$api_response_json}");
                }
            } else {
                // Si l'ID dans la base n'est pas un IMDB (numérique), on met à jour directement
                error_log("Titre/Année correspondent, tmdb_id diffère. DB ID: {$db_tmdb_id} est numérique. Mise à jour directe.");
                $pdo->beginTransaction();
                try {
                    $sqlUpdateTmdbId = "UPDATE entertainments SET tmdb_id = :new_tmdb_id WHERE id = :entertainment_id";
                    $stmtUpdateTmdbId = $pdo->prepare($sqlUpdateTmdbId);
                    $stmtUpdateTmdbId->bindValue(':new_tmdb_id', $data['tmdb_id']);
                    $stmtUpdateTmdbId->bindValue(':entertainment_id', $entertainment_id);
                    $stmtUpdateTmdbId->execute();

                    $sqlDeleteSources = "DELETE FROM sources WHERE sourceable_id = :entertainment_id AND sourceable_type = 'Modules\\\\Entertainment\\\\Models\\\\Entertainment'";
                    $stmtDeleteSources = $pdo->prepare($sqlDeleteSources);
                    $stmtDeleteSources->bindValue(':entertainment_id', $entertainment_id);
                    $stmtDeleteSources->execute();
                    $deletedSourcesCount = $stmtDeleteSources->rowCount();
                    error_log("{$deletedSourcesCount} anciennes sources supprimées pour l'entertainment ID {$entertainment_id}.");

                    $newSourcesAddedCount = 0;
                    if (isset($data['urls_alternatives']) && is_array($data['urls_alternatives'])) {
                        $sqlSource = "INSERT INTO sources (title, url, url_type, source_type, sourceable_id, sourceable_type, status, created_at, updated_at) VALUES (:title, :url, :url_type, :source_type, :sourceable_id, :sourceable_type, :status, NOW(), NOW())";
                        $stmtSource = $pdo->prepare($sqlSource);
                        
                        error_log("URLs alternatives trouvées: " . count($data['urls_alternatives']));
                        error_log("URLs alternatives: " . print_r($data['urls_alternatives'], true));
                        
                        foreach ($data['urls_alternatives'] as $url) {
                            if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
                                error_log("URL de source invalide ou vide pour entertainment {$entertainment_id}: " . $url);
                                continue;
                            }
                            
                            // Extraire le nom de domaine pour le titre
                            $parsedUrl = parse_url($url);
                            $domain = isset($parsedUrl['host']) ? $parsedUrl['host'] : 'embed';
                            $domainParts = explode('.', $domain);
                            $extractedTitle = $domainParts[0] ?? 'embed';
                            
                            try {
                                $stmtSource->execute([
                                    ':title' => $extractedTitle,
                                    ':url' => $url,
                                    ':url_type' => 'embed',
                                    ':source_type' => 'both',
                                    ':sourceable_id' => $entertainment_id,
                                    ':sourceable_type' => 'Modules\\Entertainment\\Models\\Entertainment',
                                    ':status' => 'active'
                                ]);
                                $newSourcesAddedCount++;
                                error_log("Source ajoutée avec succès: {$url} pour entertainment {$entertainment_id}");
                            } catch (PDOException $e) {
                                error_log("Erreur lors de l'ajout de la source {$url} pour entertainment {$entertainment_id}: " . $e->getMessage());
                            }
                        }
                        error_log("{$newSourcesAddedCount} nouvelles sources ajoutées pour l'entertainment ID {$entertainment_id}.");
                        
                        // Vider le cache pour ce film
                        if (function_exists('artisan_call')) {
                            artisan_call('cache:forget', ['key' => "movie_{$entertainment_id}"]);
                            error_log("Cache vidé pour le film ID {$entertainment_id}");
                        }
                    }
                    $pdo->commit();
                    http_response_code(200);
                    echo json_encode(['message' => 'Film existant mis à jour avec nouveau tmdb_id et nouvelles sources.', 'status' => 'updated_existing', 'updated_movie_id' => $entertainment_id, 'new_sources_added' => $newSourcesAddedCount]);
                    exit;
                } catch (Exception $e) {
                    $pdo->rollBack();
                    error_log("Erreur lors de la mise à jour du film après correspondance: " . $e->getMessage());
                }
            }
        }
    }

    $baseSlug = generateSlug($data['titre_fr']);
    $slug = $baseSlug;
    $yearForSlug = $data['annee'];

    $sqlCheckSlug = "SELECT id FROM entertainments WHERE name = :name AND YEAR(release_date) = :year";
    $stmtCheckSlug = $pdo->prepare($sqlCheckSlug);
    $stmtCheckSlug->bindValue(':name', $data['titre_fr']);
    $stmtCheckSlug->bindValue(':year', (int)$data['annee'], PDO::PARAM_INT);
    $stmtCheckSlug->execute();
    $slugExists = $stmtCheckSlug->fetch(PDO::FETCH_ASSOC);

    if ($slugExists) {
        $errorMessage = "Film avec même nom et année déjà existant. Film: " . $data['titre_fr'] . " (" . $yearForSlug . ")";
        error_log($errorMessage);
        http_response_code(409);
        echo json_encode(['error' => $errorMessage, 'status' => 'duplicate_film']);
        exit;
    }

    // Convertir la durée au format 00:00
    $formattedDuration = convertDuration($data['duree']);

    // On insère directement l'URL du poster sans téléchargement
    $posterUrl = isset($data['poster_path']) && filter_var($data['poster_path'], FILTER_VALIDATE_URL)
        ? $data['poster_path']
        : null;

    $sql = "INSERT INTO entertainments (name, tmdb_id, poster_url, thumbnail_url, description, type, IMDb_rating, content_rating, duration, release_date, status, created_at, updated_at, movie_access, language)
            VALUES (:name, :tmdb_id, :poster_url, :thumbnail_url, :description, :type, :imdb_rating, :content_rating, :duration, :release_date, :status, NOW(), NOW(), :movie_access, :language)";
    $stmt = $pdo->prepare($sql);
    $stmt->bindValue(':name', $data['titre_fr']);
    $stmt->bindValue(':tmdb_id', $data['tmdb_id']);
    $stmt->bindValue(':poster_url', $posterUrl);
    $stmt->bindValue(':thumbnail_url', $posterUrl);
    $stmt->bindValue(':description', $data['overview'] ?? '');
    $stmt->bindValue(':type', 'movie');
    $stmt->bindValue(':imdb_rating', $data['vote_average'] ?? '0');
    $stmt->bindValue(':content_rating', 'NA');
    $stmt->bindValue(':duration', $formattedDuration);
    $stmt->bindValue(':release_date', $releaseDate);
    $stmt->bindValue(':status', 1);
    $stmt->bindValue(':movie_access', 'free');
    $stmt->bindValue(':language', 'french');

    error_log('Données à insérer (après vérif doublons) : ' . print_r(['name' => $data['titre_fr'], 'tmdb_id' => $data['tmdb_id'], 'duration' => $formattedDuration], true));

    if ($stmt->execute()) {
        $entertainment_id = $pdo->lastInsertId();
        error_log("Insertion réussie dans entertainments. ID : {$entertainment_id}");

        if (isset($data['genre_ids']) && is_array($data['genre_ids'])) {
            $localGenreIds = convertGenreIds($data['genre_ids'], $genreMapping);
            if (!empty($localGenreIds)) {
                $sqlGenre = "INSERT INTO entertainment_gener_mapping (entertainment_id, genre_id, created_at, updated_at) VALUES (:entertainment_id, :genre_id, NOW(), NOW())";
                $stmtGenre = $pdo->prepare($sqlGenre);
                foreach ($localGenreIds as $genreId) {
                    try {
                        $stmtGenre->execute([':entertainment_id' => $entertainment_id, ':genre_id' => $genreId]);
                    } catch (PDOException $e) {
                        error_log("Erreur insertion genre {$genreId} pour entertainment {$entertainment_id}: " . $e->getMessage());
                    }
                }
            }
        }

        $sources_added_count = 0;
        if (isset($data['urls_alternatives']) && is_array($data['urls_alternatives'])) {
            $sqlSource = "INSERT INTO sources (title, url, url_type, source_type, sourceable_id, sourceable_type, status, created_at, updated_at) VALUES (:title, :url, :url_type, :source_type, :sourceable_id, :sourceable_type, :status, NOW(), NOW())";
            $stmtSource = $pdo->prepare($sqlSource);
            
            error_log("URLs alternatives trouvées: " . count($data['urls_alternatives']));
            error_log("URLs alternatives: " . print_r($data['urls_alternatives'], true));
            
            foreach ($data['urls_alternatives'] as $url) {
                if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
                    error_log("URL de source invalide ou vide pour entertainment {$entertainment_id}: " . $url);
                    continue;
                }
                
                // Extraire le nom de domaine pour le titre
                $parsedUrl = parse_url($url);
                $domain = isset($parsedUrl['host']) ? $parsedUrl['host'] : 'embed';
                $domainParts = explode('.', $domain);
                $extractedTitle = $domainParts[0] ?? 'embed';
                
                try {
                    $stmtSource->execute([
                        ':title' => $extractedTitle,
                        ':url' => $url,
                        ':url_type' => 'embed',
                        ':source_type' => 'both',
                        ':sourceable_id' => $entertainment_id,
                        ':sourceable_type' => 'Modules\\Entertainment\\Models\\Entertainment',
                        ':status' => 'active'
                    ]);
                    $sources_added_count++;
                    error_log("Source ajoutée avec succès: {$url} pour entertainment {$entertainment_id}");
                } catch (PDOException $e) {
                    error_log("Erreur lors de l'ajout de la source {$url} pour entertainment {$entertainment_id}: " . $e->getMessage());
                }
            }
        }

        header('Content-Type: application/json');
        echo json_encode(['message' => 'Film ajouté avec succès', 'id' => $entertainment_id, 'status' => 'success', 'sources_added' => $sources_added_count]);
        exit;
    } else {
        throw new PDOException('Erreur lors de l\'insertion principale du film. Info: ' . print_r($stmt->errorInfo(), true));
    }

} catch (PDOException $e) {
    error_log('Erreur PDO : ' . $e->getMessage() . ' Trace: ' . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de base de données : ' . $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    exit;
} catch (Exception $e) {
    error_log('Erreur générale : ' . $e->getMessage() . ' Trace: ' . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode(['error' => 'Erreur du serveur : ' . $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    exit;
}

function generateSlug($string) {
    $slug = preg_replace('/[^a-z0-9]+/i', '-', $string);
    $slug = trim($slug, '-');
    $slug = strtolower($slug);
    return $slug ?: 'film';
}
