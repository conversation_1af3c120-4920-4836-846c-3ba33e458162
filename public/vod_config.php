<?php
// Configuration pour le système de recherche VOD IPTV
// Ce fichier permet de tester et configurer les connexions aux bases de données

// Configuration par défaut - Modifiez selon vos paramètres
$config = [
    'main_db' => [
        'host' => 'localhost',
        'dbname' => 'voirfilms',
        'username' => 'root',
        'password' => ''
    ],
    'iptv_db' => [
        'host' => 'localhost',
        'dbname' => 'iptv_db',
        'username' => 'root',
        'password' => ''
    ],
    'iptv_db2' => [
        'host' => 'localhost',
        'dbname' => 'iptv_db2',
        'username' => 'root',
        'password' => ''
    ]
];

function testConnection($label, $dbConfig) {
    try {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Test de comptage des enregistrements
        if ($label === 'Base principale') {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM entertainments");
            $count = $stmt->fetch()['count'];
            return ['status' => 'success', 'message' => "✅ Connexion réussie ($count entertainments)"];
        } else {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM poster_iptv");
            $count = $stmt->fetch()['count'];
            return ['status' => 'success', 'message' => "✅ Connexion réussie ($count posters)"];
        }
    } catch (PDOException $e) {
        return ['status' => 'error', 'message' => "❌ Erreur: " . $e->getMessage()];
    }
}

// Test d'une requête de recherche simple
function testSearch($config) {
    try {
        $mainPdo = new PDO(
            "mysql:host={$config['main_db']['host']};dbname={$config['main_db']['dbname']};charset=utf8mb4",
            $config['main_db']['username'],
            $config['main_db']['password'],
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        $iptvPdo1 = new PDO(
            "mysql:host={$config['iptv_db']['host']};dbname={$config['iptv_db']['dbname']};charset=utf8mb4",
            $config['iptv_db']['username'],
            $config['iptv_db']['password'],
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        // Test de recherche
        $stmt = $iptvPdo1->prepare("SELECT COUNT(*) as count FROM poster_iptv WHERE title LIKE ?");
        $stmt->execute(['%test%']);
        $count = $stmt->fetch()['count'];
        
        return ['status' => 'success', 'message' => "✅ Test de recherche réussi ($count résultats pour 'test')"];
    } catch (PDOException $e) {
        return ['status' => 'error', 'message' => "❌ Erreur lors du test de recherche: " . $e->getMessage()];
    }
}

$action = $_GET['action'] ?? '';

if ($action === 'generate_config') {
    // Générer le code de configuration à copier dans vod_search.php
    header('Content-Type: text/plain');
    echo "// Configuration à copier dans vod_search.php\n";
    echo "\$config = [\n";
    echo "    'main_db' => [\n";
    echo "        'host' => '{$config['main_db']['host']}',\n";
    echo "        'dbname' => '{$config['main_db']['dbname']}',\n";
    echo "        'username' => '{$config['main_db']['username']}',\n";
    echo "        'password' => '{$config['main_db']['password']}'\n";
    echo "    ],\n";
    echo "    'iptv_db' => [\n";
    echo "        'host' => '{$config['iptv_db']['host']}',\n";
    echo "        'dbname' => '{$config['iptv_db']['dbname']}',\n";
    echo "        'username' => '{$config['iptv_db']['username']}',\n";
    echo "        'password' => '{$config['iptv_db']['password']}'\n";
    echo "    ],\n";
    echo "    'iptv_db2' => [\n";
    echo "        'host' => '{$config['iptv_db2']['host']}',\n";
    echo "        'dbname' => '{$config['iptv_db2']['dbname']}',\n";
    echo "        'username' => '{$config['iptv_db2']['username']}',\n";
    echo "        'password' => '{$config['iptv_db2']['password']}'\n";
    echo "    ],\n";
    echo "    'tmdb_api_key' => 'YOUR_TMDB_API_KEY' // Remplacez par votre clé API TMDB\n";
    echo "];\n";
    exit;
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/phosphor-icons@2.0.0/src/css/icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h1 class="mb-4">
                    <i class="ph ph-gear me-2"></i>
                    Configuration VOD IPTV
                </h1>
                
                <!-- Test des connexions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ph ph-database me-2"></i>
                            Test des connexions aux bases de données
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <h6>Base principale</h6>
                                <p class="small text-muted">Base de données Laravel (entertainments)</p>
                                <?php
                                $result = testConnection('Base principale', $config['main_db']);
                                $alertClass = $result['status'] === 'success' ? 'alert-success' : 'alert-danger';
                                ?>
                                <div class="alert <?= $alertClass ?> py-2">
                                    <?= $result['message'] ?>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <h6>IPTV DB 1</h6>
                                <p class="small text-muted">Première base IPTV (poster_iptv)</p>
                                <?php
                                $result = testConnection('IPTV DB 1', $config['iptv_db']);
                                $alertClass = $result['status'] === 'success' ? 'alert-success' : 'alert-danger';
                                ?>
                                <div class="alert <?= $alertClass ?> py-2">
                                    <?= $result['message'] ?>
                                </div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <h6>IPTV DB 2</h6>
                                <p class="small text-muted">Deuxième base IPTV (poster_iptv)</p>
                                <?php
                                $result = testConnection('IPTV DB 2', $config['iptv_db2']);
                                $alertClass = $result['status'] === 'success' ? 'alert-success' : 'alert-danger';
                                ?>
                                <div class="alert <?= $alertClass ?> py-2">
                                    <?= $result['message'] ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Test de recherche -->
                        <hr>
                        <h6>Test de recherche</h6>
                        <?php
                        $searchResult = testSearch($config);
                        $alertClass = $searchResult['status'] === 'success' ? 'alert-success' : 'alert-danger';
                        ?>
                        <div class="alert <?= $alertClass ?> py-2">
                            <?= $searchResult['message'] ?>
                        </div>
                    </div>
                </div>
                
                <!-- Configuration actuelle -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ph ph-file-code me-2"></i>
                            Configuration actuelle
                        </h5>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><code><?php
echo "// Configuration des bases de données\n";
echo "\$config = [\n";
foreach ($config as $key => $dbConfig) {
    echo "    '{$key}' => [\n";
    foreach ($dbConfig as $param => $value) {
        echo "        '{$param}' => '{$value}',\n";
    }
    echo "    ],\n";
}
echo "    'tmdb_api_key' => 'YOUR_TMDB_API_KEY'\n";
echo "];\n";
?></code></pre>
                        
                        <div class="d-flex gap-2 mt-3">
                            <a href="?action=generate_config" class="btn btn-primary" target="_blank">
                                <i class="ph ph-download me-2"></i>
                                Télécharger la configuration
                            </a>
                            <a href="vod_search.php" class="btn btn-success">
                                <i class="ph ph-magnifying-glass me-2"></i>
                                Aller à la recherche VOD
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ph ph-info me-2"></i>
                            Instructions d'installation
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li class="mb-2">
                                <strong>Vérifiez les connexions</strong> ci-dessus. Toutes doivent être vertes.
                            </li>
                            <li class="mb-2">
                                <strong>Modifiez la configuration</strong> dans <code>vod_search.php</code> si nécessaire :
                                <ul class="mt-1">
                                    <li>Changez les paramètres de base de données</li>
                                    <li>Ajoutez votre clé API TMDB</li>
                                </ul>
                            </li>
                            <li class="mb-2">
                                <strong>Obtenez une clé API TMDB</strong> :
                                <ul class="mt-1">
                                    <li>Inscrivez-vous sur <a href="https://www.themoviedb.org/" target="_blank">themoviedb.org</a></li>
                                    <li>Allez dans Settings > API</li>
                                    <li>Créez une nouvelle clé API</li>
                                    <li>Remplacez <code>YOUR_TMDB_API_KEY</code> par votre clé</li>
                                </ul>
                            </li>
                            <li class="mb-2">
                                <strong>Testez le système</strong> en accédant à <a href="vod_search.php">vod_search.php</a>
                            </li>
                        </ol>
                        
                        <div class="alert alert-info mt-3">
                            <i class="ph ph-lightbulb me-2"></i>
                            <strong>Fonctionnalités du système :</strong>
                            <ul class="mb-0 mt-2">
                                <li>Recherche simultanée dans les deux bases IPTV</li>
                                <li>Affichage uniquement des éléments non présents dans le système principal</li>
                                <li>Bouton "Ajouter" : Ajoute le film/série dans la base principale</li>
                                <li>Bouton "Corriger" : Cherche et corrige le TMDB ID via l'API TMDB</li>
                                <li>Interface responsive avec notifications en temps réel</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 