<?php
// Script de test pour le filtrage français M3U

require_once 'import_m3u.php';

// Exemples de lignes EXTINF pour tester la détection française
$testLines = [
    // Formats qui DOIVENT être détectés comme français
    '#EXTINF:-1 tvg-id="FR: TF1" tvg-name="FR: TF1" tvg-logo="" group-title="French",FR: TF1',
    '#EXTINF:-1 tvg-id="" tvg-name="FR - M6" tvg-logo="" group-title="French",FR - M6',
    '#EXTINF:-1 tvg-id="" tvg-name="" tvg-logo="" group-title="French",FR: Breaking Bad S01E01',
    '#EXTINF:-1 tvg-id="" tvg-name="" tvg-logo="" group-title="Series",[FR] Game of Thrones S01E01',
    '#EXTINF:-1 tvg-id="" tvg-name="" tvg-logo="" group-title="Movies",{FR} Avengers Endgame',
    '#EXTINF:-1 tvg-id="" tvg-name="" tvg-logo="" group-title="VOD",The Witcher S01E01 FR|',
    '#EXTINF:-1 tvg-id="" tvg-name="" tvg-logo="" group-title="Series",Stranger Things S04E01 FR -',
    
    // Formats qui NE DOIVENT PAS être détectés comme français
    '#EXTINF:-1 tvg-id="" tvg-name="EN: BBC One" tvg-logo="" group-title="English",EN: BBC One',
    '#EXTINF:-1 tvg-id="" tvg-name="" tvg-logo="" group-title="US",Breaking Bad S01E01',
    '#EXTINF:-1 tvg-id="" tvg-name="" tvg-logo="" group-title="Movies",Avengers Endgame',
    '#EXTINF:-1 tvg-id="" tvg-name="" tvg-logo="" group-title="German",[DE] Game of Thrones S01E01',
    '#EXTINF:-1 tvg-id="" tvg-name="" tvg-logo="" group-title="Spanish",{ES} The Witcher S01E01',
];

echo "🧪 Test du filtrage français\n";
echo "============================\n\n";

$frenchCount = 0;
$totalCount = count($testLines);

foreach ($testLines as $index => $line) {
    $isFrench = isFrenchChannel($line);
    $status = $isFrench ? "✅ FRANÇAIS" : "❌ NON-FRANÇAIS";
    
    if ($isFrench) {
        $frenchCount++;
    }
    
    echo sprintf("Test %d: %s\n", $index + 1, $status);
    echo "Ligne: " . substr($line, 0, 80) . "...\n\n";
}

echo "📊 Résultats du test:\n";
echo "- Total de lignes testées: $totalCount\n";
echo "- Détectées comme françaises: $frenchCount\n";
echo "- Attendu comme français: 7 (lignes 1-7)\n";
echo "- Attendu comme non-français: " . ($totalCount - 7) . " (lignes 8+)\n\n";

// Test de création d'un fichier M3U d'exemple
$sampleM3U = "#EXTM3U\n";
foreach ($testLines as $line) {
    $sampleM3U .= $line . "\n";
    $sampleM3U .= "http://example.com/stream" . rand(1000, 9999) . ".m3u8\n";
}

$testFile = sys_get_temp_dir() . '/test_playlist.m3u';
file_put_contents($testFile, $sampleM3U);

echo "🔍 Test du filtrage complet sur fichier M3U:\n";
try {
    $filterResult = filterFrenchChannels($testFile);
    
    echo "- Fichier original: " . strlen($sampleM3U) . " octets, " . $totalCount . " chaînes\n";
    echo "- Chaînes françaises détectées: " . $filterResult['total_french_detected'] . "\n";
    echo "- Chaînes conservées: " . $filterResult['filtered_count'] . "\n";
    echo "- Fichier filtré: " . $filterResult['filtered_size'] . " octets\n";
    echo "- Réduction: " . round((1 - $filterResult['filtered_size'] / strlen($sampleM3U)) * 100, 1) . "%\n\n";
    
    // Nettoyer les fichiers de test
    unlink($testFile);
    if (file_exists($filterResult['filtered_file'])) {
        unlink($filterResult['filtered_file']);
    }
    
    echo "✅ Test du filtrage français terminé avec succès!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur lors du test: " . $e->getMessage() . "\n";
    
    // Nettoyer en cas d'erreur
    if (file_exists($testFile)) {
        unlink($testFile);
    }
}
?> 