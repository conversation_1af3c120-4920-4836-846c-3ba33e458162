# Configuration des URLs M3U

## 📋 Variables d'environnement requises

Ajoutez ces variables dans votre fichier `.env` à la racine du projet :

```bash
# URLs M3U pour l'importation IPTV
UPDATE_IPTV1_URL="http://votre-serveur.com/get.php?username=XXXX&password=YYYY&type=m3u_plus&output=mpegts"
UPDATE_IPTV2_URL="http://votre-serveur2.com/playlist.m3u"
```

## 🔧 Format des URLs

### Exemple avec paramètres d'authentification :
```bash
UPDATE_IPTV1_URL="http://pvkeapwq.mexamo.xyz/get.php?username=3LAPD9M6&password=7BW6TMA4&type=m3u_plus&output=mpegts"
```

### Exemple avec URL directe :
```bash
UPDATE_IPTV2_URL="https://exemple.com/playlist.m3u"
```

## ✅ Validation

Pour tester vos URLs, utilisez l'un de ces scripts :

### Via interface web :
1. Allez sur `http://votre-site/public/vod/import_m3u.php`
2. Sélectionnez "Test (URL d'exemple)" pour tester le système
3. Une fois fonctionnel, configurez vos vraies URLs

### Via ligne de commande :
```bash
php debug_url.php
```

## 🛠️ Dépannage

### Erreur "URL rejected: Malformed input"
- Vérifiez que l'URL est bien formatée
- Assurez-vous qu'elle commence par `http://` ou `https://`
- Échappez les caractères spéciaux si nécessaire

### Erreur "URL non configurée"
- Vérifiez que les variables sont dans le fichier `.env`
- Redémarrez votre serveur web après modification du `.env`
- Vérifiez qu'il n'y a pas d'espaces autour des URLs

### Test avec cURL en ligne de commande :
```bash
curl -L -A "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" "VOTRE_URL_ICI" -o test.m3u
```

## 📁 Structure attendue du fichier M3U

```
#EXTM3U
#EXTINF:-1 tvg-id="..." tvg-name="FR: Série" tvg-logo="..." group-title="French",FR: Breaking Bad S01E01
http://serveur.com/stream1.m3u8
#EXTINF:-1 tvg-id="..." tvg-name="FR: Film" tvg-logo="..." group-title="Movies",[FR] Avengers
http://serveur.com/stream2.m3u8
```

## 🇫🇷 Filtrage français

Le système détecte automatiquement les chaînes françaises selon ces patterns :
- `tvg-name="FR:..."`
- `,FR - ` ou `,FR: `
- `[FR]` ou `{FR}`
- `FR|` ou `FR -`

Vous pouvez désactiver le filtrage en décochant l'option dans l'interface. 