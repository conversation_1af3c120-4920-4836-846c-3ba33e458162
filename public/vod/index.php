<?php
// Dashboard de pilotage VOD IPTV
$config = require_once 'config.php';

function getDashboardStats($config) {
    try {
        $mainPdo = getConnection($config['main_db']);
        $iptvPdo1 = getConnection($config['iptv_db']);
        $iptvPdo2 = getConnection($config['iptv_db2']);
        
        $stats = [];
        
        // Stats base principale
        $stmt = $mainPdo->query("SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tmdb_id IS NULL OR tmdb_id = '' THEN 1 END) as no_tmdb,
            COUNT(CASE WHEN poster_url IS NULL OR poster_url = '' THEN 1 END) as no_poster,
            COUNT(CASE WHEN type = 'movie' THEN 1 END) as movies,
            COUNT(CASE WHEN type = 'tvshow' THEN 1 END) as series,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent
        FROM entertainments WHERE deleted_at IS NULL");
        $stats['main'] = $stmt->fetch();
        
        // Stats IPTV DB 1
        $stmt = $iptvPdo1->query("SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tmdb_id IS NULL OR tmdb_id = '' OR tmdb_id = '0' THEN 1 END) as no_tmdb,
            COUNT(CASE WHEN poster_path IS NULL OR poster_path = '' THEN 1 END) as no_poster,
            COUNT(CASE WHEN type = 'movie' THEN 1 END) as movies,
            COUNT(CASE WHEN type IN ('tvshow', 'series') THEN 1 END) as series,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent
        FROM poster_iptv");
        $stats['iptv1'] = $stmt->fetch();
        
        // Stats IPTV DB 2
        $stmt = $iptvPdo2->query("SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tmdb_id IS NULL OR tmdb_id = '' OR tmdb_id = '0' THEN 1 END) as no_tmdb,
            COUNT(CASE WHEN poster_path IS NULL OR poster_path = '' THEN 1 END) as no_poster,
            COUNT(CASE WHEN type = 'movie' THEN 1 END) as movies,
            COUNT(CASE WHEN type IN ('tvshow', 'series') THEN 1 END) as series,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent
        FROM poster_iptv");
        $stats['iptv2'] = $stmt->fetch();
        
        // Calcul des doublons potentiels
        $stmt = $mainPdo->query("SELECT COUNT(*) as duplicates FROM entertainments e1 
            WHERE EXISTS (SELECT 1 FROM entertainments e2 
                WHERE e1.id != e2.id AND e1.tmdb_id = e2.tmdb_id 
                AND e1.tmdb_id IS NOT NULL AND e1.tmdb_id != '' 
                AND e1.deleted_at IS NULL AND e2.deleted_at IS NULL)");
        $stats['duplicates'] = $stmt->fetch()['duplicates'];
        
        return $stats;
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

function getConnection($dbConfig) {
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
    return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
}

$stats = getDashboardStats($config);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/phosphor-icons@2.0.0/src/css/icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="ph ph-chart-bar me-2"></i>
                Dashboard VOD IPTV
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="stats.php">
                        <i class="ph ph-chart-line-up me-1"></i>
                        Statistiques
                    </a>
                    <a class="nav-link" href="search.php">
                        <i class="ph ph-magnifying-glass me-1"></i>
                        Recherche
                    </a>
                    <a class="nav-link" href="import_m3u.php">
                        <i class="ph ph-download me-1"></i>
                        Import M3U
                    </a>
                    <a class="nav-link" href="notifications.php">
                        <i class="ph ph-bell me-1"></i>
                        Notifications
                    </a>
                    <a class="nav-link" href="settings.php">
                        <i class="ph ph-gear me-1"></i>
                        Paramètres
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <?php if (isset($stats['error'])): ?>
            <div class="alert alert-danger">
                <i class="ph ph-warning me-2"></i>
                Erreur: <?= htmlspecialchars($stats['error']) ?>
            </div>
        <?php else: ?>
            
            <!-- Vue d'ensemble -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Base Principale</h5>
                                    <h2 class="mb-0"><?= number_format($stats['main']['total']) ?></h2>
                                    <small>contenus actifs</small>
                                </div>
                                <i class="ph ph-database" style="font-size: 3rem; opacity: 0.3;"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">IPTV DB 1</h5>
                                    <h2 class="mb-0"><?= number_format($stats['iptv1']['total']) ?></h2>
                                    <small>éléments sources</small>
                                </div>
                                <i class="ph ph-television" style="font-size: 3rem; opacity: 0.3;"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">IPTV DB 2</h5>
                                    <h2 class="mb-0"><?= number_format($stats['iptv2']['total']) ?></h2>
                                    <small>éléments sources</small>
                                </div>
                                <i class="ph ph-television" style="font-size: 3rem; opacity: 0.3;"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h5 class="card-title">Doublons</h5>
                                    <h2 class="mb-0"><?= number_format($stats['duplicates']) ?></h2>
                                    <small>à vérifier</small>
                                </div>
                                <i class="ph ph-copy" style="font-size: 3rem; opacity: 0.3;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="ph ph-lightning me-2"></i>
                                Actions rapides
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <a href="search.php?special=no_tmdb&db=all" class="btn btn-outline-warning w-100">
                                        <i class="ph ph-warning me-2"></i>
                                        Corriger TMDB manquants
                                        <span class="badge bg-warning ms-2">
                                            <?= $stats['main']['no_tmdb'] + $stats['iptv1']['no_tmdb'] + $stats['iptv2']['no_tmdb'] ?>
                                        </span>
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="search.php?special=no_poster&db=all" class="btn btn-outline-secondary w-100">
                                        <i class="ph ph-image me-2"></i>
                                        Ajouter posters
                                        <span class="badge bg-secondary ms-2">
                                            <?= $stats['main']['no_poster'] + $stats['iptv1']['no_poster'] + $stats['iptv2']['no_poster'] ?>
                                        </span>
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="search.php?special=recent&db=all" class="btn btn-outline-info w-100">
                                        <i class="ph ph-clock me-2"></i>
                                        Vérifier récents
                                        <span class="badge bg-info ms-2">
                                            <?= $stats['main']['recent'] + $stats['iptv1']['recent'] + $stats['iptv2']['recent'] ?>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Détails par base -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Base Principale</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="mainChart"></canvas>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    Films: <?= number_format($stats['main']['movies']) ?><br>
                                    Séries: <?= number_format($stats['main']['series']) ?><br>
                                    Sans TMDB: <?= number_format($stats['main']['no_tmdb']) ?><br>
                                    Sans poster: <?= number_format($stats['main']['no_poster']) ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">IPTV DB Principale</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="iptv1Chart"></canvas>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    Films: <?= number_format($stats['iptv1']['movies']) ?><br>
                                    Séries: <?= number_format($stats['iptv1']['series']) ?><br>
                                    Sans TMDB: <?= number_format($stats['iptv1']['no_tmdb']) ?><br>
                                    Sans poster: <?= number_format($stats['iptv1']['no_poster']) ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">IPTV DB Secondaire</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="iptv2Chart"></canvas>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    Films: <?= number_format($stats['iptv2']['movies']) ?><br>
                                    Séries: <?= number_format($stats['iptv2']['series']) ?><br>
                                    Sans TMDB: <?= number_format($stats['iptv2']['no_tmdb']) ?><br>
                                    Sans poster: <?= number_format($stats['iptv2']['no_poster']) ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php endif; ?>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Configuration commune des graphiques
                const chartConfig = {
                    type: 'doughnut',
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: { 
                                    usePointStyle: true,
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return ` ${context.label}: ${context.raw.toLocaleString()}`;
                                    }
                                }
                            }
                        }
                    }
                };

                const colors = {
                    films: '#0d6efd',
                    series: '#198754',
                    noTmdb: '#ffc107',
                    noPoster: '#6c757d'
                };

                // Fonction de création de graphique
                function createChart(elementId, data) {
                    const ctx = document.getElementById(elementId);
                    if (!ctx) {
                        console.error(`Element ${elementId} not found`);
                        return;
                    }

                    return new Chart(ctx, {
                        ...chartConfig,
                        data: {
                            labels: ['Films', 'Séries', 'Sans TMDB', 'Sans poster'],
                            datasets: [{
                                data: data,
                                backgroundColor: [colors.films, colors.series, colors.noTmdb, colors.noPoster]
                            }]
                        }
                    });
                }

                // Création des graphiques
                createChart('mainChart', [
                    <?= $stats['main']['movies'] ?>, 
                    <?= $stats['main']['series'] ?>, 
                    <?= $stats['main']['no_tmdb'] ?>, 
                    <?= $stats['main']['no_poster'] ?>
                ]);

                createChart('iptv1Chart', [
                    <?= $stats['iptv1']['movies'] ?>, 
                    <?= $stats['iptv1']['series'] ?>, 
                    <?= $stats['iptv1']['no_tmdb'] ?>, 
                    <?= $stats['iptv1']['no_poster'] ?>
                ]);

                createChart('iptv2Chart', [
                    <?= $stats['iptv2']['movies'] ?>, 
                    <?= $stats['iptv2']['series'] ?>, 
                    <?= $stats['iptv2']['no_tmdb'] ?>, 
                    <?= $stats['iptv2']['no_poster'] ?>
                ]);

            } catch (error) {
                console.error('Erreur lors de l\'initialisation des graphiques:', error);
                document.querySelectorAll('.chart-container').forEach(container => {
                    container.innerHTML = '<div class="alert alert-danger">Erreur de chargement du graphique</div>';
                });
            }
        });

        // Script pour copier les valeurs du formulaire principal vers le formulaire direct
        document.getElementById('direct-save-button').addEventListener('click', function(e) {
            e.preventDefault();
            
            // Copier toutes les valeurs du formulaire principal
            const mainForm = document.getElementById('form-submit');
            const directForm = document.getElementById('direct-save-form');
            
            // Supprimer les anciens champs cachés pour éviter les doublons
            const oldHiddenInputs = directForm.querySelectorAll('input[type="hidden"]:not([name="bypass_importer"]):not([name="_token"]):not([name="_method"])');
            oldHiddenInputs.forEach(input => input.remove());
            
            // Copier tous les champs du formulaire principal
            const formElements = mainForm.elements;
            for (let i = 0; i < formElements.length; i++) {
                const element = formElements[i];
                const name = element.name;
                
                // Ignorer les éléments sans nom ou les boutons
                if (!name || element.type === 'submit') continue;
                
                // Traitement spécial pour les checkboxes
                if (element.type === 'checkbox') {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = name;
                    input.value = element.checked ? '1' : '0';
                    directForm.appendChild(input);
                    continue;
                }
                
                // Traitement spécial pour les select multiples
                if (element.multiple && element.options) {
                    const selectedValues = Array.from(element.selectedOptions).map(option => option.value);
                    selectedValues.forEach(value => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = name;
                        input.value = value;
                        directForm.appendChild(input);
                    });
                    continue;
                }
                
                // Copie standard pour les autres types de champs
                if (!directForm.querySelector(`[name="${name}"]`)) {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = name;
                    input.value = element.value;
                    directForm.appendChild(input);
                }
            }
            
            // Soumettre le formulaire direct
            directForm.submit();
        });
    </script>
</body>
</html> 