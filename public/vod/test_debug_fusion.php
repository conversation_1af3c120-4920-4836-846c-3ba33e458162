<?php
/**
 * Script de debug spécialisé pour le problème de fusion Standard vs IPTV
 */

require_once __DIR__ . '/../../vendor/autoload.php';
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Modules\Entertainment\Services\MultiIptvService;
use Illuminate\Support\Facades\Log;

header('Content-Type: application/json; charset=utf-8');

$tmdbId = $_GET['tmdb_id'] ?? null;

if (!$tmdbId) {
    echo json_encode(['error' => 'Paramètre tmdb_id requis'], JSON_PRETTY_PRINT);
    exit;
}

try {
    $service = new MultiIptvService();
    
    // Créer une instance pour accéder aux méthodes privées via réflexion
    $reflection = new ReflectionClass($service);
    
    // Méthodes privées qu'on veut tester
    $getSeriesFromDatabase = $reflection->getMethod('getSeriesFromDatabase');
    $getSeriesFromStandardDatabase = $reflection->getMethod('getSeriesFromStandardDatabase');
    $mergeSeriesData = $reflection->getMethod('mergeSeriesData');
    $mergeWithStandardData = $reflection->getMethod('mergeWithStandardData');
    $getMaxSeasonNumber = $reflection->getMethod('getMaxSeasonNumber');
    
    $getSeriesFromDatabase->setAccessible(true);
    $getSeriesFromStandardDatabase->setAccessible(true);
    $mergeSeriesData->setAccessible(true);
    $mergeWithStandardData->setAccessible(true);
    $getMaxSeasonNumber->setAccessible(true);
    
    $result = [
        'tmdb_id' => $tmdbId,
        'step_by_step_analysis' => []
    ];
    
    // ÉTAPE 1: Récupérer les données brutes
    $step1 = ['step' => 1, 'description' => 'Récupération données brutes'];
    
    $seriesData1 = $getSeriesFromDatabase->invoke($service, 'iptv_db', $tmdbId);
    $seriesData2 = $getSeriesFromDatabase->invoke($service, 'iptv_db2', $tmdbId);
    $standardData = $getSeriesFromStandardDatabase->invoke($service, $tmdbId);
    
    $step1['iptv1_seasons'] = $seriesData1 ? array_column($seriesData1, 'season_number') : [];
    $step1['iptv2_seasons'] = $seriesData2 ? array_column($seriesData2, 'season_number') : [];
    $step1['standard_seasons'] = $standardData ? array_column($standardData, 'season_number') : [];
    
    $step1['iptv1_count'] = count($step1['iptv1_seasons']);
    $step1['iptv2_count'] = count($step1['iptv2_seasons']);
    $step1['standard_count'] = count($step1['standard_seasons']);
    
    $result['step_by_step_analysis'][] = $step1;
    
    // ÉTAPE 2: Fusion IPTV1 + IPTV2
    $step2 = ['step' => 2, 'description' => 'Fusion IPTV1 + IPTV2'];
    
    $iptvMerged = null;
    if ($seriesData1 && $seriesData2) {
        $iptvMerged = $mergeSeriesData->invoke($service, $seriesData1, $seriesData2);
        $step2['result'] = 'Fusion effectuée';
        $step2['merged_seasons'] = array_column($iptvMerged, 'season_number');
        $step2['merged_count'] = count($iptvMerged);
    } elseif ($seriesData1) {
        $iptvMerged = $seriesData1;
        $step2['result'] = 'Seulement IPTV1 disponible';
        $step2['merged_seasons'] = array_column($iptvMerged, 'season_number');
        $step2['merged_count'] = count($iptvMerged);
    } elseif ($seriesData2) {
        $iptvMerged = $seriesData2;
        $step2['result'] = 'Seulement IPTV2 disponible';
        $step2['merged_seasons'] = array_column($iptvMerged, 'season_number');
        $step2['merged_count'] = count($iptvMerged);
    } else {
        $step2['result'] = 'Aucune donnée IPTV';
        $step2['merged_seasons'] = [];
        $step2['merged_count'] = 0;
    }
    
    $result['step_by_step_analysis'][] = $step2;
    
    // ÉTAPE 3: Calcul des maximums
    $step3 = ['step' => 3, 'description' => 'Calcul des maximums'];
    
    $maxIptvSeason = $iptvMerged ? $getMaxSeasonNumber->invoke($service, $iptvMerged) : 0;
    $maxStandardSeason = $standardData ? $getMaxSeasonNumber->invoke($service, $standardData) : 0;
    
    $step3['max_iptv_season'] = $maxIptvSeason;
    $step3['max_standard_season'] = $maxStandardSeason;
    $step3['condition_check'] = "{$maxStandardSeason} > {$maxIptvSeason} = " . ($maxStandardSeason > $maxIptvSeason ? 'TRUE' : 'FALSE');
    $step3['will_add_standard_seasons'] = $maxStandardSeason > $maxIptvSeason;
    
    $result['step_by_step_analysis'][] = $step3;
    
    // ÉTAPE 4: Fusion finale avec Standard
    $step4 = ['step' => 4, 'description' => 'Fusion finale avec Standard'];
    
    $finalResult = $mergeWithStandardData->invoke($service, $iptvMerged, $standardData, $tmdbId);
    
    if ($finalResult) {
        $step4['final_seasons'] = array_column($finalResult, 'season_number');
        $step4['final_count'] = count($finalResult);
        
        // Analyser d'où vient chaque saison
        $step4['season_sources'] = [];
        foreach ($finalResult as $season) {
            $step4['season_sources'][] = [
                'season_number' => $season['season_number'],
                'source' => $season['iptv_source'],
                'is_iptv' => $season['is_iptv']
            ];
        }
    } else {
        $step4['final_seasons'] = [];
        $step4['final_count'] = 0;
        $step4['season_sources'] = [];
    }
    
    $result['step_by_step_analysis'][] = $step4;
    
    // ANALYSE FINALE
    $result['problem_analysis'] = [];
    
    // Vérifier si la saison 3 existe dans iptv2 mais pas dans le résultat final IPTV
    if (in_array(3, $step1['iptv2_seasons']) && !in_array(3, $step2['merged_seasons'])) {
        $result['problem_analysis'][] = "🚨 PROBLÈME DÉTECTÉ: Saison 3 existe dans IPTV2 mais perdue lors de la fusion IPTV1+IPTV2";
    }
    
    // Vérifier si la saison 3 finale vient du standard alors qu'elle existe dans iptv2
    $season3Source = null;
    foreach ($step4['season_sources'] as $seasonSource) {
        if ($seasonSource['season_number'] == 3) {
            $season3Source = $seasonSource;
            break;
        }
    }
    
    if ($season3Source && in_array(3, $step1['iptv2_seasons']) && $season3Source['source'] === 'standard') {
        $result['problem_analysis'][] = "🚨 PROBLÈME CONFIRMÉ: Saison 3 récupérée depuis Standard alors qu'elle existe dans IPTV2";
    }
    
    if (empty($result['problem_analysis'])) {
        $result['problem_analysis'][] = "✅ Aucun problème détecté dans la logique de fusion";
    }
    
} catch (Exception $e) {
    $result = [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ];
}

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?> 