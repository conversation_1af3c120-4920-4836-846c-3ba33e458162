<?php
// API RESTful pour VOD IPTV
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$config = require_once 'config.php';

class VodApi {
    private $config;
    private $authRequired = true;
    
    public function __construct($config) {
        $this->config = $config;
    }
    
    private function getConnection($dbConfig) {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    }
    
    private function authenticate() {
        if (!$this->authRequired) return true;
        
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? '';
        
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return false;
        }
        
        $token = $matches[1];
        $expectedToken = env('API_TOKEN', 'your-secret-api-token');
        
        return hash_equals($expectedToken, $token);
    }
    
    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data, JSON_PRETTY_PRINT);
        exit;
    }
    
    private function sendError($message, $statusCode = 400) {
        $this->sendResponse(['error' => $message, 'status' => $statusCode], $statusCode);
    }
    
    public function handleRequest() {
        if (!$this->authenticate()) {
            $this->sendError('Unauthorized', 401);
        }
        
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', trim($path, '/'));
        
        // Enlever 'vod/api.php' du chemin
        array_shift($pathParts); // 'vod'
        array_shift($pathParts); // 'api.php'
        
        $endpoint = $pathParts[0] ?? '';
        $id = $pathParts[1] ?? null;
        
        switch ($endpoint) {
            case 'stats':
                $this->getStats();
                break;
                
            case 'search':
                $this->search();
                break;
                
            case 'content':
                if ($method === 'GET') {
                    $this->getContent($id);
                } elseif ($method === 'POST') {
                    $this->createContent();
                } elseif ($method === 'PUT') {
                    $this->updateContent($id);
                } elseif ($method === 'DELETE') {
                    $this->deleteContent($id);
                }
                break;
                
            case 'sync':
                $this->syncContent();
                break;
                
            case 'health':
                $this->healthCheck();
                break;
                
            default:
                $this->sendError('Endpoint not found', 404);
        }
    }
    
    private function getStats() {
        try {
            $mainPdo = $this->getConnection($this->config['main_db']);
            $iptvPdo1 = $this->getConnection($this->config['iptv_db']);
            $iptvPdo2 = $this->getConnection($this->config['iptv_db2']);
            
            $stats = [
                'main_db' => $this->getDbStats($mainPdo, 'entertainments'),
                'iptv_db' => $this->getDbStats($iptvPdo1, 'poster_iptv'),
                'iptv_db2' => $this->getDbStats($iptvPdo2, 'poster_iptv'),
                'timestamp' => date('c')
            ];
            
            $this->sendResponse($stats);
            
        } catch (Exception $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }
    
    private function getDbStats($pdo, $table) {
        $whereClause = $table === 'entertainments' ? 'WHERE deleted_at IS NULL' : '';
        
        $stmt = $pdo->query("SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN tmdb_id IS NULL OR tmdb_id = '' THEN 1 END) as no_tmdb,
            COUNT(CASE WHEN type = 'movie' THEN 1 END) as movies,
            COUNT(CASE WHEN type IN ('tvshow', 'series') THEN 1 END) as series
        FROM {$table} {$whereClause}");
        
        return $stmt->fetch();
    }
    
    private function search() {
        $query = $_GET['q'] ?? '';
        $db = $_GET['db'] ?? 'all';
        $limit = min((int) ($_GET['limit'] ?? 20), 100); // Max 100
        
        if (empty($query)) {
            $this->sendError('Query parameter required');
        }
        
        try {
            $results = [];
            
            if ($db === 'all' || $db === 'main_db') {
                $mainPdo = $this->getConnection($this->config['main_db']);
                $stmt = $mainPdo->prepare("SELECT id, name as title, type, tmdb_id, poster_url, 
                                          YEAR(release_date) as year, 'main_db' as source
                                          FROM entertainments 
                                          WHERE (name LIKE ? OR tmdb_id LIKE ?) AND deleted_at IS NULL 
                                          LIMIT ?");
                $searchTerm = "%{$query}%";
                $stmt->execute([$searchTerm, $searchTerm, $limit]);
                $results = array_merge($results, $stmt->fetchAll());
            }
            
            if ($db === 'all' || $db === 'iptv_db') {
                $iptvPdo = $this->getConnection($this->config['iptv_db']);
                $stmt = $iptvPdo->prepare("SELECT id, title, type, tmdb_id, poster_path as poster_url, 
                                          year, 'iptv_db' as source
                                          FROM poster_iptv 
                                          WHERE title LIKE ? OR tmdb_id LIKE ? 
                                          LIMIT ?");
                $searchTerm = "%{$query}%";
                $stmt->execute([$searchTerm, $searchTerm, $limit]);
                $results = array_merge($results, $stmt->fetchAll());
            }
            
            $this->sendResponse([
                'query' => $query,
                'count' => count($results),
                'results' => $results
            ]);
            
        } catch (Exception $e) {
            $this->sendError('Search error: ' . $e->getMessage(), 500);
        }
    }
    
    private function getContent($id) {
        if (!$id) {
            $this->sendError('Content ID required');
        }
        
        $db = $_GET['db'] ?? 'main_db';
        
        try {
            $pdo = $this->getConnection($this->config[$db]);
            
            if ($db === 'main_db') {
                $stmt = $pdo->prepare("SELECT * FROM entertainments WHERE id = ? AND deleted_at IS NULL");
            } else {
                $stmt = $pdo->prepare("SELECT * FROM poster_iptv WHERE id = ?");
            }
            
            $stmt->execute([$id]);
            $content = $stmt->fetch();
            
            if (!$content) {
                $this->sendError('Content not found', 404);
            }
            
            $this->sendResponse($content);
            
        } catch (Exception $e) {
            $this->sendError('Database error: ' . $e->getMessage(), 500);
        }
    }
    
    private function syncContent() {
        $sourceDb = $_POST['source_db'] ?? $_GET['source_db'] ?? '';
        $action = $_POST['action'] ?? $_GET['action'] ?? 'add_missing';
        
        if (!in_array($sourceDb, ['iptv_db', 'iptv_db2'])) {
            $this->sendError('Invalid source database');
        }
        
        try {
            $mainPdo = $this->getConnection($this->config['main_db']);
            $iptvPdo = $this->getConnection($this->config[$sourceDb]);
            
            // Trouver les contenus IPTV avec TMDB mais pas dans main
            $stmt = $iptvPdo->query("SELECT id, title, tmdb_id FROM poster_iptv 
                                    WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'");
            $iptvItems = $stmt->fetchAll();
            
            $toSync = [];
            foreach ($iptvItems as $item) {
                $checkStmt = $mainPdo->prepare("SELECT id FROM entertainments WHERE tmdb_id = ? AND deleted_at IS NULL");
                $checkStmt->execute([$item['tmdb_id']]);
                
                if (!$checkStmt->fetch()) {
                    $toSync[] = $item;
                }
            }
            
            $this->sendResponse([
                'source_db' => $sourceDb,
                'action' => $action,
                'total_iptv' => count($iptvItems),
                'to_sync' => count($toSync),
                'items' => array_slice($toSync, 0, 20) // Limiter pour l'affichage
            ]);
            
        } catch (Exception $e) {
            $this->sendError('Sync error: ' . $e->getMessage(), 500);
        }
    }
    
    private function healthCheck() {
        $health = [
            'status' => 'ok',
            'timestamp' => date('c'),
            'version' => '1.0.0',
            'databases' => []
        ];
        
        foreach (['main_db', 'iptv_db', 'iptv_db2'] as $db) {
            try {
                $pdo = $this->getConnection($this->config[$db]);
                $stmt = $pdo->query('SELECT 1');
                $health['databases'][$db] = 'connected';
            } catch (Exception $e) {
                $health['databases'][$db] = 'error: ' . $e->getMessage();
                $health['status'] = 'degraded';
            }
        }
        
        $this->sendResponse($health);
    }
}

// Charger les fonctions utilitaires
function env($key, $default = null) {
    global $config;
    // Implémentation simplifiée - adapter selon votre système
    return $default;
}

// Initialiser et traiter la requête
try {
    $api = new VodApi($config);
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error', 'message' => $e->getMessage()]);
}

// API endpoint dédié pour Import M3U V2
// Démarrer la session uniquement si pas encore active
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Configuration des erreurs
error_reporting(E_ALL);
ini_set('display_errors', 0); // Pas d'affichage pour l'API

// Headers pour API JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Fonction de réponse JSON
function jsonResponse($data, $httpCode = 200) {
    http_response_code($httpCode);
    echo json_encode($data);
    exit;
}

// Fonction d'erreur
function jsonError($message, $httpCode = 400) {
    jsonResponse(['success' => false, 'error' => $message], $httpCode);
}

// Vérifier que c'est une requête POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonError('Méthode non autorisée', 405);
}

// Vérifier l'action
if (!isset($_POST['action'])) {
    jsonError('Action manquante');
}

// Inclure uniquement la classe, pas le fichier complet
require_once 'config.php';

// Définir la classe directement ici pour éviter les conflits
class M3UImportV2Simple {
    private $temp_dir;
    
    public function __construct() {
        $this->temp_dir = __DIR__ . '/temp/';
    }
    
    public function generateDetailedStats($channels) {
        $stats = [
            'total_channels' => count($channels),
            'by_type' => ['movie' => 0, 'series' => 0, 'tv' => 0, 'unknown' => 0],
            'by_year' => [],
            'by_group' => [],
            'series_details' => [
                'total_series' => 0,
                'total_seasons' => 0,
                'total_episodes' => 0,
                'by_season' => []
            ],
            'top_groups' => [],
            'recent_years' => [],
            'url_analysis' => [
                'extensions' => [],
                'domains' => []
            ]
        ];
        
        foreach ($channels as $channel) {
            // Comptage par type
            $stats['by_type'][$channel['type']]++;
            
            // Analyse des années
            if ($channel['year']) {
                if (!isset($stats['by_year'][$channel['year']])) {
                    $stats['by_year'][$channel['year']] = 0;
                }
                $stats['by_year'][$channel['year']]++;
            }
            
            // Analyse des groupes
            $groupName = $channel['group_title'] ?: 'Non défini';
            if (!isset($stats['by_group'][$groupName])) {
                $stats['by_group'][$groupName] = 0;
            }
            $stats['by_group'][$groupName]++;
            
            // Analyse des séries
            if ($channel['type'] === 'series') {
                $stats['series_details']['total_series']++;
                if ($channel['season']) {
                    if (!isset($stats['series_details']['by_season'][$channel['season']])) {
                        $stats['series_details']['by_season'][$channel['season']] = 0;
                    }
                    $stats['series_details']['by_season'][$channel['season']]++;
                    $stats['series_details']['total_episodes']++;
                }
            }
            
            // Analyse des URLs
            $url = $channel['url'];
            $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
            if ($extension) {
                if (!isset($stats['url_analysis']['extensions'][$extension])) {
                    $stats['url_analysis']['extensions'][$extension] = 0;
                }
                $stats['url_analysis']['extensions'][$extension]++;
            }
            
            $domain = parse_url($url, PHP_URL_HOST);
            if ($domain) {
                if (!isset($stats['url_analysis']['domains'][$domain])) {
                    $stats['url_analysis']['domains'][$domain] = 0;
                }
                $stats['url_analysis']['domains'][$domain]++;
            }
        }
        
        // Calculer les saisons uniques
        $stats['series_details']['total_seasons'] = count($stats['series_details']['by_season']);
        
        // Trier et limiter
        arsort($stats['by_group']);
        $stats['top_groups'] = array_slice($stats['by_group'], 0, 10, true);
        
        krsort($stats['by_year']);
        $stats['recent_years'] = array_slice($stats['by_year'], 0, 10, true);
        
        arsort($stats['url_analysis']['extensions']);
        arsort($stats['url_analysis']['domains']);
        
        return $stats;
    }
    
    public function searchInChannels($channels, $query = '', $type = 'all', $yearFrom = null, $yearTo = null, $group = '', $season = null, $episode = null, $urlFilter = '') {
        $results = [];
        $stats = [
            'total_searched' => count($channels),
            'matches_found' => 0,
            'by_type' => ['movie' => 0, 'series' => 0, 'tv' => 0, 'unknown' => 0],
            'by_year' => [],
            'by_group' => []
        ];
        
        foreach ($channels as $channel) {
            $match = true;
            
            // Filtrage par type
            if ($type !== 'all' && $channel['type'] !== $type) {
                $match = false;
            }
            
            // Filtrage par texte
            if (!empty($query) && $match) {
                $searchText = strtolower($channel['title'] . ' ' . $channel['tvg_name'] . ' ' . $channel['group_title']);
                $queryLower = strtolower($query);
                if (strpos($searchText, $queryLower) === false) {
                    $match = false;
                }
            }
            
            // Filtrage par année
            if ($match && ($yearFrom !== null || $yearTo !== null)) {
                $channelYear = $channel['year'];
                if ($channelYear === null) {
                    $match = false;
                } else {
                    if ($yearFrom !== null && $channelYear < $yearFrom) $match = false;
                    if ($yearTo !== null && $channelYear > $yearTo) $match = false;
                }
            }
            
            // Autres filtres...
            if ($match && !empty($group)) {
                if (stripos($channel['group_title'], $group) === false) {
                    $match = false;
                }
            }
            
            if ($match && $season !== null) {
                if ($channel['season'] !== $season) {
                    $match = false;
                }
            }
            
            if ($match && $episode !== null) {
                if ($channel['episode'] !== $episode) {
                    $match = false;
                }
            }
            
            if ($match && !empty($urlFilter)) {
                if (stripos($channel['url'], $urlFilter) === false) {
                    $match = false;
                }
            }
            
            if ($match) {
                $results[] = $channel;
                $stats['matches_found']++;
                $stats['by_type'][$channel['type']]++;
                
                if ($channel['year']) {
                    if (!isset($stats['by_year'][$channel['year']])) {
                        $stats['by_year'][$channel['year']] = 0;
                    }
                    $stats['by_year'][$channel['year']]++;
                }
                
                $groupName = $channel['group_title'] ?: 'Non défini';
                if (!isset($stats['by_group'][$groupName])) {
                    $stats['by_group'][$groupName] = 0;
                }
                $stats['by_group'][$groupName]++;
            }
        }
        
        // Trier les résultats par titre
        usort($results, function($a, $b) {
            return strcmp($a['title'], $b['title']);
        });
        
        return [
            'results' => $results,
            'total' => count($results),
            'stats' => $stats
        ];
    }
    
    public function getProgress($type = 'filter') {
        $progress_file = $this->temp_dir . $type . '_progress.json';
        if (file_exists($progress_file)) {
            return json_decode(file_get_contents($progress_file), true);
        }
        return null;
    }
    
    public function cleanupTempFiles() {
        $files = glob($this->temp_dir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
    }
}

// Initialiser l'importeur
try {
    $importer = new M3UImportV2();
} catch (Exception $e) {
    jsonError('Erreur initialisation: ' . $e->getMessage(), 500);
}

// Traiter l'action
$action = $_POST['action'];

switch ($action) {
    case 'get_stats':
        if (!isset($_SESSION['m3u_data'])) {
            jsonError('Pas de données en session');
        }
        
        $stats = $importer->generateDetailedStats($_SESSION['m3u_data']['channels']);
        jsonResponse(['success' => true, 'stats' => $stats]);
        break;
        
    case 'search':
        if (!isset($_SESSION['m3u_data'])) {
            jsonError('Pas de données en session');
        }
        
        $query = $_POST['query'] ?? '';
        $type = $_POST['type'] ?? 'all';
        $yearFrom = !empty($_POST['yearFrom']) ? intval($_POST['yearFrom']) : null;
        $yearTo = !empty($_POST['yearTo']) ? intval($_POST['yearTo']) : null;
        $group = $_POST['group'] ?? '';
        $season = !empty($_POST['season']) ? intval($_POST['season']) : null;
        $episode = !empty($_POST['episode']) ? intval($_POST['episode']) : null;
        $urlFilter = $_POST['urlFilter'] ?? '';
        
        $results = $importer->searchInChannels(
            $_SESSION['m3u_data']['channels'],
            $query, $type, $yearFrom, $yearTo, $group, $season, $episode, $urlFilter
        );
        
        jsonResponse([
            'success' => true,
            'results' => $results['results'],
            'total' => $results['total'],
            'stats' => $results['stats']
        ]);
        break;
        
    case 'get_channels':
        if (isset($_SESSION['m3u_data'])) {
            jsonResponse([
                'success' => true,
                'channels' => $_SESSION['m3u_data']['channels'],
                'total' => $_SESSION['m3u_data']['total_channels']
            ]);
        } else {
            jsonError('Pas de données en session');
        }
        break;
        
    case 'get_progress':
        $type = $_POST['type'] ?? 'filter';
        $progress = $importer->getProgress($type);
        jsonResponse($progress);
        break;
        
    case 'cleanup':
        $importer->cleanupTempFiles();
        unset($_SESSION['m3u_data']);
        jsonResponse(['success' => true]);
        break;
        
    default:
        jsonError('Action inconnue: ' . $action);
}
?> 