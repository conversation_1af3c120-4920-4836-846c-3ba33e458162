<?php
/**
 * Script de debug pour MultiIptvService
 * Test pour série avec saison 3 manquante
 */

// Configuration Laravel bootstrap
require_once __DIR__ . '/../../vendor/autoload.php';
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Modules\Entertainment\Services\MultiIptvService;
use Illuminate\Support\Facades\Log;

header('Content-Type: application/json; charset=utf-8');

// Paramètres
$tmdbId = $_GET['tmdb_id'] ?? null;
$action = $_GET['action'] ?? 'debug';

if (!$tmdbId) {
    echo json_encode([
        'error' => 'Paramètre tmdb_id requis',
        'usage' => 'test_multi_iptv_debug.php?tmdb_id=123456&action=debug|refresh|nocache'
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $service = new MultiIptvService();
    
    if (!$service->isEnabled()) {
        echo json_encode([
            'error' => 'Service MultiIptv désactivé',
            'config_check' => [
                'iptv_integration' => config('entertainment.iptv_integration', false),
                'multi_iptv_enabled' => config('entertainment.multi_iptv_enabled', false),
                'iptv_series_enabled' => config('entertainment.iptv_series_enabled', true)
            ]
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $result = [
        'tmdb_id' => $tmdbId,
        'action' => $action,
        'timestamp' => now()->toDateTimeString()
    ];
    
    switch ($action) {
        case 'debug':
            // Debug des sources brutes
            $result['debug_sources'] = $service->debugTvShowSources($tmdbId);
            
            // Données avec cache
            $result['cached_data'] = $service->getTvShowData($tmdbId);
            $result['cached_seasons_count'] = $result['cached_data'] ? count($result['cached_data']) : 0;
            
            // Vérification si la saison 3 est présente
            $season3Found = false;
            if ($result['cached_data']) {
                foreach ($result['cached_data'] as $season) {
                    if ($season['season_number'] == 3) {
                        $season3Found = true;
                        $result['season_3_info'] = [
                            'found' => true,
                            'source' => $season['iptv_source'],
                            'episodes_count' => count($season['episodes'])
                        ];
                        break;
                    }
                }
            }
            
            if (!$season3Found) {
                $result['season_3_info'] = ['found' => false];
            }
            
            break;
            
        case 'refresh':
            // Forcer la mise à jour en vidant le cache
            $result['action_performed'] = 'Cache vidé et données rechargées';
            $result['new_data'] = $service->refreshTvShowData($tmdbId);
            $result['new_seasons_count'] = $result['new_data'] ? count($result['new_data']) : 0;
            break;
            
        case 'nocache':
            // Récupérer les données sans cache
            $result['action_performed'] = 'Données récupérées sans cache';
            $result['fresh_data'] = $service->getTvShowDataNoCache($tmdbId);
            $result['fresh_seasons_count'] = $result['fresh_data'] ? count($result['fresh_data']) : 0;
            break;
            
        case 'compare':
            // Comparer cache vs fresh data
            $cachedData = $service->getTvShowData($tmdbId);
            $freshData = $service->getTvShowDataNoCache($tmdbId);
            
            $result['comparison'] = [
                'cached_seasons' => $cachedData ? count($cachedData) : 0,
                'fresh_seasons' => $freshData ? count($freshData) : 0,
                'difference' => ($freshData ? count($freshData) : 0) - ($cachedData ? count($cachedData) : 0)
            ];
            
            if ($result['comparison']['difference'] > 0) {
                $result['analysis'] = 'Il y a plus de saisons dans les données fraîches - problème de cache détecté';
                $result['recommendation'] = 'Utilisez action=refresh pour vider le cache';
            } elseif ($result['comparison']['difference'] < 0) {
                $result['analysis'] = 'Il y a moins de saisons dans les données fraîches - problème de base de données';
            } else {
                $result['analysis'] = 'Aucune différence détectée entre cache et données fraîches';
            }
            
            $result['cached_data'] = $cachedData;
            $result['fresh_data'] = $freshData;
            break;
            
        default:
            $result['error'] = 'Action non reconnue. Utilisez: debug, refresh, nocache, compare';
    }
    
} catch (Exception $e) {
    $result = [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ];
}

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?> 