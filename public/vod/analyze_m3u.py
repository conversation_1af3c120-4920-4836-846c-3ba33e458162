#!/usr/bin/env python3
import re
import os
import json
import sys
import argparse
from datetime import datetime
from collections import defaultdict

def analyze_playlist(input_file, output_file):
    # Dictionnaires pour stocker les films et séries
    movies = []
    series = []
    # Dictionnaire pour stocker les saisons par série
    series_seasons = defaultdict(set)
    
    # Patterns de détection généraux pour identifier les lignes
    # Pattern simple : si contient SXX EXX → série, sinon → film
    series_patterns = [
        re.compile(r'\sS(\d+)\s*E(\d+)', re.IGNORECASE),  # S01 E01, S1 E1
        re.compile(r'\sS(\d+)E(\d+)', re.IGNORECASE),     # S01E01
        re.compile(r'\s(\d+)x(\d+)', re.IGNORECASE),      # 1x01, 01x01
        re.compile(r'Season\s+(\d+).*?Episode\s+(\d+)', re.IGNORECASE),  # Season 1 Episode 1
        re.compile(r'Sai<PERSON>\s+(\d+).*?Episode\s+(\d+)', re.IGNORECASE),  # Saison 1 Episode 1
    ]
    
    # Lire le fichier M3U
    with open(input_file, 'r', encoding='utf-8') as infile:
        lines = infile.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
            
        # Vérifier si c'est une ligne EXTINF
        if line.startswith('#EXTINF'):
            # Extraire le titre (après la dernière virgule)
            title_match = re.search(r',([^,]+)$', line)
            if not title_match:
                i += 1
                continue
            
            title = title_match.group(1).strip()
            
            # Extraire tvg-name si présent
            tvg_name = ""
            tvg_match = re.search(r'tvg-name="([^"]*)"', line)
            if tvg_match:
                tvg_name = tvg_match.group(1)
            
            # Extraire group-title si présent
            group_title = ""
            group_match = re.search(r'group-title="([^"]*)"', line)
            if group_match:
                group_title = group_match.group(1)
            
            # Lire la ligne suivante pour obtenir l'URL
            url = ""
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                # Vérifier que ce n'est pas une autre ligne de métadonnées
                if not next_line.startswith('#') and next_line:
                    url = next_line
                    i += 1  # Passer à la ligne suivante puisqu'on l'a lue
                
                # Combiner titre et tvg_name pour la recherche
                search_text = f"{title} {tvg_name}"
                
                # Vérifier si c'est une série en testant tous les patterns
                series_match = None
                for pattern in series_patterns:
                    series_match = pattern.search(search_text)
                    if series_match:
                        break
                
                if series_match:
                    # C'est une série
                    season_number = int(series_match.group(1))
                    episode_number = int(series_match.group(2))
                    
                    # Nettoyer le nom de la série
                    series_name = re.sub(r'\s*S\d+.*', '', title).strip()
                    series_name = re.sub(r'\s*Season\s+\d+.*', '', series_name, flags=re.IGNORECASE).strip()
                    series_name = re.sub(r'\s*Saison\s+\d+.*', '', series_name, flags=re.IGNORECASE).strip()
                    series_name = re.sub(r'\s*\d+x\d+.*', '', series_name).strip()
                    
                    series.append({
                        'title': title,
                        'clean_name': series_name,
                        'season': season_number,
                        'episode': episode_number,
                        'tvg_name': tvg_name,
                        'group_title': group_title,
                        'url': url,
                        'type': 'series'
                    })
                    series_seasons[series_name].add(season_number)
                else:
                    # C'est un film (par défaut)
                    # Extraire l'année si présente
                    year = None
                    year_match = re.search(r'\((\d{4})\)', title)
                    if year_match:
                        year = int(year_match.group(1))
                    
                    # Nettoyer le titre du film
                    clean_title = re.sub(r'\s*\(\d{4}\)', '', title).strip()
                    
                    movies.append({
                        'title': title,
                        'clean_name': clean_title,
                        'year': year,
                        'tvg_name': tvg_name,
                        'group_title': group_title,
                        'url': url,
                        'type': 'movie'
                    })
        
        i += 1
    
    # Calculer les statistiques
    total_seasons = sum(len(seasons) for seasons in series_seasons.values())
    unique_series = len(series_seasons)
    
    # Préparer les données JSON
    result = {
        'analysis_info': {
            'input_file': input_file,
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_items': len(movies) + len(series)
        },
        'statistics': {
            'total_movies': len(movies),
            'total_series_episodes': len(series),
            'unique_series': unique_series,
            'total_seasons': total_seasons
        },
        'movies': movies,
        'series': series,
        'series_summary': []
    }
    
    # Ajouter le résumé des séries
    for series_name, seasons in series_seasons.items():
        episodes_count = len([s for s in series if s['clean_name'] == series_name])
        result['series_summary'].append({
            'name': series_name,
            'seasons_count': len(seasons),
            'episodes_count': episodes_count,
            'seasons': sorted(list(seasons))
        })
    
    # Trier les séries par nom
    result['series_summary'].sort(key=lambda x: x['name'])
    
    # Écrire les résultats dans le fichier JSON
    with open(output_file, 'w', encoding='utf-8') as outfile:
        json.dump(result, outfile, indent=2, ensure_ascii=False)
    
    return result

def main():
    parser = argparse.ArgumentParser(description='Analyser un fichier M3U et générer un rapport JSON')
    parser.add_argument('input_file', help='Fichier M3U à analyser')
    parser.add_argument('output_file', help='Fichier JSON de sortie')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"Erreur: Le fichier {args.input_file} n'existe pas.")
        sys.exit(1)
    
    try:
        result = analyze_playlist(args.input_file, args.output_file)
        
        print(f"Analyse terminée avec succès.")
        print(f"Films trouvés: {result['statistics']['total_movies']}")
        print(f"Épisodes de séries: {result['statistics']['total_series_episodes']}")
        print(f"Séries uniques: {result['statistics']['unique_series']}")
        print(f"Saisons totales: {result['statistics']['total_seasons']}")
        print(f"Résultats sauvés dans: {args.output_file}")
            
    except Exception as e:
        print(f"Erreur lors de l'analyse: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 