# Guide de debug MultiIptvService - Saison manquante

## 🎯 Problème
Vous avez ajouté une saison 3 dans une base IPTV (iptv2) mais elle n'apparaît pas dans l'application.

## 🔍 Diagnostic

### 1. Via l'interface web
```bash
# Remplacez 123456 par le vrai TMDB ID de votre série
http://votre-domaine.com/vod/test_multi_iptv_debug.php?tmdb_id=123456&action=debug
```

### 2. Via la ligne de commande (recommandé)
```bash
# Debug complet
php artisan multi-iptv:debug 123456

# Comparer cache vs données fraîches
php artisan multi-iptv:debug 123456 --action=compare

# Vider le cache et tester
php artisan multi-iptv:debug 123456 --clear-cache --action=refresh
```

## 🛠️ Solutions

### Solution 1: Problème de cache (le plus fréquent)
Si le problème vient du cache :

```bash
# Forcer le rafraîchissement
php artisan multi-iptv:debug 123456 --action=refresh
```

**Via PHP dans votre code :**
```php
use Modules\Entertainment\Services\MultiIptvService;

$service = new MultiIptvService();
$freshData = $service->refreshTvShowData($tmdbId);
```

### Solution 2: Vérifier la configuration
```bash
# Vérifier que le service est activé
php artisan multi-iptv:debug 123456
```

La sortie doit montrer :
- ✅ `iptv_integration: true`
- ✅ `multi_iptv_enabled: true` 
- ✅ `iptv_series_enabled: true`

### Solution 3: Vérifier les données brutes
```bash
# Voir les données de chaque base
php artisan multi-iptv:debug 123456 --action=debug
```

Vérifiez que :
- iptv_db2 montre bien la saison 3
- Toutes les connexions sont actives

## 📊 Interprétation des résultats

### Résultat normal (fonctionnel)
```
Source      | Connecté | Série trouvée | Saisons | Numéros saisons
------------|----------|---------------|---------|----------------
iptv_db     | ✅       | ✅            | 2       | 1, 2
iptv_db2    | ✅       | ✅            | 3       | 1, 2, 3
standard    | ✅       | ❌            | 0       |

✅ 3 saisons trouvées
✅ Saison 3 trouvée !
Source IPTV: iptv_db2
```

### Problème de cache détecté
```
Comparison:
Cache    | Fraîches | Différence
---------|----------|------------
2        | 3        | 1

⚠️ Les données fraîches ont 1 saison(s) de plus - problème de cache détecté !
Saisons manquantes dans le cache: 3
```

### Problème de données
```
Source      | Connecté | Série trouvée | Saisons | Numéros saisons
------------|----------|---------------|---------|----------------
iptv_db     | ✅       | ✅            | 2       | 1, 2
iptv_db2    | ✅       | ✅            | 2       | 1, 2  ← Problème ici
```

## 🔄 Comment fonctionne la fusion

Le service `MultiIptvService` fusionne les données dans cet ordre :
1. **iptv_db** (base 1) → données de base
2. **iptv_db2** (base 2) → fusion avec base 1
3. **standard** → fusion finale si nécessaire

La logique de fusion :
- Si une saison existe dans les deux bases → fusion des épisodes
- Si une saison n'existe que dans base 2 → **elle est ajoutée** ✅
- Cache pendant 1 heure (3600 secondes)

## 🚨 Actions de dépannage

### 1. Cache corrompu
```bash
# Vider tout le cache de la série
php artisan multi-iptv:debug 123456 --action=refresh

# Ou vider manuellement
php artisan cache:forget multi_iptv_series_123456
php artisan cache:forget standard_series_count_123456
```

### 2. Données pas synchronisées
Vérifiez dans votre base iptv2 :
```sql
-- Remplacez 123456 par votre TMDB ID
SELECT p.tmdb_id, s.season_number, COUNT(e.id) as episodes
FROM poster_iptv p
JOIN season_iptv s ON s.poster_iptv_id = p.id  
LEFT JOIN episode_iptv e ON e.season_iptv_id = s.id
WHERE p.tmdb_id = 123456 AND p.type = 'tvshow'
GROUP BY p.tmdb_id, s.season_number
ORDER BY s.season_number;
```

### 3. Configuration manquante
Dans votre fichier `.env` ou config :
```env
# Activer l'intégration IPTV
IPTV_INTEGRATION=true
MULTI_IPTV_ENABLED=true
IPTV_SERIES_ENABLED=true
```

## 📝 Logs utiles

Les logs sont dans `storage/logs/laravel.log`, cherchez :
```
[MultiIptv Service] Processing TV show with TMDB ID: 123456
[MultiIptv Service] DB2 series data: 3 seasons
[MultiIptv Service] Merging IPTV databases...
[MultiIptv Service] Adding new season 3 from DB2
[MultiIptv Service] Final result has 3 seasons
```

## ⚡ Solution rapide

Pour la plupart des cas (problème de cache) :
```bash
# Une seule commande pour résoudre
php artisan multi-iptv:debug YOUR_TMDB_ID --action=refresh
```

Et vérifiez immédiatement si la saison 3 apparaît dans votre application.

## 🔧 Nouvelles méthodes disponibles

Le service a été amélioré avec ces nouvelles méthodes publiques :

```php
// Forcer le rafraîchissement des données
$service->refreshTvShowData($tmdbId);

// Récupérer sans cache (debug)
$service->getTvShowDataNoCache($tmdbId);

// Debug des sources brutes
$service->debugTvShowSources($tmdbId);

// Vider le cache manuellement
$service->clearCache($tmdbId, 'series');
``` 