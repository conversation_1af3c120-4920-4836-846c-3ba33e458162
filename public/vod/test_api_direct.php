<?php
// Test direct de l'API avec simulation de session
session_start();

// Simuler des données de session
if (!isset($_SESSION['m3u_data'])) {
    // Charger le fichier test
    require_once 'import_m3u_v2.php';
    $importer = new M3UImportV2();
    
    $testFile = __DIR__ . '/test_playlist.m3u';
    if (file_exists($testFile)) {
        $result = $importer->parseM3UContent($testFile);
        if (isset($result['channels'])) {
            $_SESSION['m3u_data'] = [
                'source' => 'test',
                'file_path' => $testFile,
                'channels' => $result['channels'],
                'total_channels' => count($result['channels']),
                'filter_stats' => ['filtered' => false],
                'timestamp' => time()
            ];
            echo "✅ Données de session créées: " . count($result['channels']) . " chaînes\n";
        }
    }
}

// Maintenant tester l'API
$_POST['action'] = 'get_stats';

// Capturer la sortie
ob_start();
include 'api.php';
$output = ob_get_clean();

echo "=== Réponse API ===\n";
echo $output . "\n";

// Tester si c'est du JSON valide
$decoded = json_decode($output, true);
if ($decoded !== null) {
    echo "✅ JSON valide\n";
    if (isset($decoded['success']) && $decoded['success']) {
        echo "✅ API fonctionnelle\n";
        echo "Statistiques trouvées:\n";
        if (isset($decoded['stats']['total_channels'])) {
            echo "- Total chaînes: " . $decoded['stats']['total_channels'] . "\n";
            echo "- Films: " . $decoded['stats']['by_type']['movie'] . "\n";
            echo "- Séries: " . $decoded['stats']['by_type']['series'] . "\n";
        }
    } else {
        echo "❌ Erreur API: " . ($decoded['error'] ?? 'Inconnue') . "\n";
    }
} else {
    echo "❌ Réponse JSON invalide\n";
}
?> 