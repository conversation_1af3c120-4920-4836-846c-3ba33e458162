<?php
// Système de gestion des saisons et épisodes
// Interface complète pour gérer les saisons et épisodes d'une série

// Charger la configuration
$config = require_once 'config.php';

// Fonctions utilitaires
function getConnection($dbConfig) {
    try {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    } catch (PDOException $e) {
        die("Erreur de connexion à {$dbConfig['dbname']}: " . $e->getMessage());
    }
}

function getEntertainmentInfo($pdo, $entertainmentId, $sourceDb = 'main') {
    if ($sourceDb === 'main') {
        $stmt = $pdo->prepare("SELECT * FROM entertainments WHERE id = ? AND deleted_at IS NULL");
        $stmt->execute([$entertainmentId]);
        return $stmt->fetch();
    } else {
        // Pour les données IPTV, on cherche dans poster_iptv
        $stmt = $pdo->prepare("SELECT * FROM poster_iptv WHERE id = ?");
        $stmt->execute([$entertainmentId]);
        return $stmt->fetch();
    }
}

function getIptvEntertainmentInfo($iptvPdo, $posterIptvId) {
    $stmt = $iptvPdo->prepare("SELECT * FROM poster_iptv WHERE id = ?");
    $stmt->execute([$posterIptvId]);
    return $stmt->fetch();
}

function getSeasons($pdo, $entertainmentId, $sourceDb = 'main') {
    if ($sourceDb === 'main') {
        $stmt = $pdo->prepare("
            SELECT s.*, COUNT(e.id) as episode_count 
            FROM seasons s 
            LEFT JOIN episodes e ON s.id = e.season_id AND e.deleted_at IS NULL
            WHERE s.entertainment_id = ? AND s.deleted_at IS NULL 
            GROUP BY s.id 
            ORDER BY CAST(s.season_index AS UNSIGNED) ASC
        ");
        $stmt->execute([$entertainmentId]);
        return $stmt->fetchAll();
    } else {
        // Pour les données IPTV
        $stmt = $pdo->prepare("
            SELECT s.*, COUNT(e.id) as episode_count,
                   s.id as id, s.season_number as season_index, s.title as name
            FROM season_iptv s 
            LEFT JOIN episode_iptv e ON s.id = e.season_iptv_id
            WHERE s.poster_iptv_id = ?
            GROUP BY s.id 
            ORDER BY CAST(s.season_number AS UNSIGNED) ASC
        ");
        $stmt->execute([$entertainmentId]);
        return $stmt->fetchAll();
    }
}

function getEpisodes($pdo, $seasonId, $sourceDb = 'main') {
    if ($sourceDb === 'main') {
        $stmt = $pdo->prepare("
            SELECT * FROM episodes 
            WHERE season_id = ? AND deleted_at IS NULL 
            ORDER BY CAST(episode_number AS UNSIGNED) ASC
        ");
        $stmt->execute([$seasonId]);
        return $stmt->fetchAll();
    } else {
        // Pour les données IPTV
        $stmt = $pdo->prepare("
            SELECT e.*, e.title as name, e.episode_number, e.description, e.created_at,
                   GROUP_CONCAT(CONCAT(s.quality, ':', s.language, ':', s.url) SEPARATOR '|') as sources
            FROM episode_iptv e
            LEFT JOIN source_episode_iptv s ON e.id = s.episode_iptv_id
            WHERE e.season_iptv_id = ?
            GROUP BY e.id
            ORDER BY CAST(e.episode_number AS UNSIGNED) ASC
        ");
        $stmt->execute([$seasonId]);
        return $stmt->fetchAll();
    }
}

function searchTmdbSeason($seriesTmdbId, $seasonNumber, $bearerToken, $apiBase) {
    if (!$seriesTmdbId || !$seasonNumber) return null;
    
    $url = "{$apiBase}/tv/{$seriesTmdbId}/season/{$seasonNumber}?language=fr-FR";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $bearerToken,
        'accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false || $httpCode !== 200) {
        return null;
    }
    
    return json_decode($response, true);
}

// Traitement des requêtes AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    $sourceDb = $_POST['source_db'] ?? 'main';
    
    // Déterminer la bonne connexion en fonction de la source
    if ($sourceDb === 'main') {
        $pdo = getConnection($config['main_db']);
    } else {
        if (!isset($config[$sourceDb])) {
            echo json_encode(['success' => false, 'message' => "Base de données '$sourceDb' non configurée"]);
            exit;
        }
        $pdo = getConnection($config[$sourceDb]);
    }
    
    if ($action === 'create_season') {
        $entertainmentId = $_POST['entertainment_id'] ?? '';
        $seasonData = json_decode($_POST['season_data'] ?? '{}', true);
        
        if (!$entertainmentId || empty($seasonData)) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                // Insertion dans la table seasons principale
                $sql = "INSERT INTO seasons (
                    entertainment_id, name, season_index, tmdb_id, poster_url, 
                    short_desc, description, access, status, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
                
                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute([
                    $entertainmentId,
                    $seasonData['name'] ?? 'Saison ' . ($seasonData['season_index'] ?? '1'),
                    $seasonData['season_index'] ?? 1,
                    $seasonData['tmdb_id'] ?? null,
                    $seasonData['poster_url'] ?? null,
                    $seasonData['short_desc'] ?? null,
                    $seasonData['description'] ?? null,
                    'free',
                    1,
                    1
                ]);
            } else {
                // Insertion dans la table season_iptv
                $sql = "INSERT INTO season_iptv (
                    poster_iptv_id, season_number, title, created_at
                ) VALUES (?, ?, ?, NOW())";
                
                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute([
                    $entertainmentId,
                    $seasonData['season_index'] ?? 1,
                    $seasonData['name'] ?? 'Saison ' . ($seasonData['season_index'] ?? '1')
                ]);
            }
            
            if ($result) {
                $seasonId = $pdo->lastInsertId();
                echo json_encode(['success' => true, 'message' => 'Saison créée avec succès', 'season_id' => $seasonId]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'create_episode') {
        $seasonId = $_POST['season_id'] ?? '';
        $episodeData = json_decode($_POST['episode_data'] ?? '{}', true);
        
        if (!$seasonId || empty($episodeData)) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                // Récupérer l'entertainment_id de la saison
                $stmt = $pdo->prepare("SELECT entertainment_id FROM seasons WHERE id = ?");
                $stmt->execute([$seasonId]);
                $season = $stmt->fetch();
                
                if (!$season) {
                    echo json_encode(['success' => false, 'message' => 'Saison non trouvée']);
                    exit;
                }
                
                $sql = "INSERT INTO episodes (
                    entertainment_id, season_id, name, episode_number, poster_url, 
                    short_desc, description, duration, release_date, access, status, 
                    tmdb_id, tmdb_season, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
                
                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute([
                    $season['entertainment_id'],
                    $seasonId,
                    $episodeData['name'] ?? 'Épisode ' . ($episodeData['episode_number'] ?? '1'),
                    $episodeData['episode_number'] ?? 1,
                    $episodeData['poster_url'] ?? null,
                    $episodeData['short_desc'] ?? null,
                    $episodeData['description'] ?? null,
                    $episodeData['duration'] ?? null,
                    $episodeData['release_date'] ?? null,
                    'free',
                    1,
                    $episodeData['tmdb_id'] ?? null,
                    $episodeData['tmdb_season'] ?? null,
                    1
                ]);
            } else {
                // Vérifier que la saison IPTV existe
                $stmt = $pdo->prepare("SELECT poster_iptv_id FROM season_iptv WHERE id = ?");
                $stmt->execute([$seasonId]);
                $season = $stmt->fetch();
                
                if (!$season) {
                    echo json_encode(['success' => false, 'message' => 'Saison IPTV non trouvée']);
                    exit;
                }
                
                // Insertion dans la table episode_iptv
                $sql = "INSERT INTO episode_iptv (
                    season_iptv_id, episode_number, title, description, created_at
                ) VALUES (?, ?, ?, ?, NOW())";
                
                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute([
                    $seasonId,
                    $episodeData['episode_number'] ?? 1,
                    $episodeData['name'] ?? 'Épisode ' . ($episodeData['episode_number'] ?? '1'),
                    $episodeData['description'] ?? null
                ]);
            }
            
            if ($result) {
                $episodeId = $pdo->lastInsertId();
                echo json_encode(['success' => true, 'message' => 'Épisode créé avec succès', 'episode_id' => $episodeId]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la création']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'get_tmdb_season') {
        $tmdbId = $_POST['tmdb_id'] ?? '';
        $seasonNumber = $_POST['season_number'] ?? '';
        
        if (!$tmdbId || !$seasonNumber) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        $seasonData = searchTmdbSeason($tmdbId, $seasonNumber, $config['tmdb_bearer_token'], $config['tmdb_api_base']);
        
        if ($seasonData) {
            echo json_encode(['success' => true, 'season_data' => $seasonData]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Saison non trouvée sur TMDB']);
        }
        exit;
    }
    
    if ($action === 'import_tmdb_episodes') {
        $seasonId = $_POST['season_id'] ?? '';
        $tmdbData = json_decode($_POST['tmdb_data'] ?? '{}', true);
        
        if (!$seasonId || empty($tmdbData)) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                // Récupérer l'entertainment_id de la saison
                $stmt = $pdo->prepare("SELECT entertainment_id FROM seasons WHERE id = ?");
                $stmt->execute([$seasonId]);
                $season = $stmt->fetch();
                
                if (!$season) {
                    echo json_encode(['success' => false, 'message' => 'Saison non trouvée']);
                    exit;
                }
                
                $importedCount = 0;
                $episodes = $tmdbData['episodes'] ?? [];
                
                foreach ($episodes as $episode) {
                    // Vérifier si l'épisode existe déjà
                    $stmt = $pdo->prepare("SELECT id FROM episodes WHERE season_id = ? AND episode_number = ? AND deleted_at IS NULL");
                    $stmt->execute([$seasonId, $episode['episode_number']]);
                    
                    if ($stmt->fetch()) {
                        continue; // Épisode déjà existant
                    }
                    
                    $sql = "INSERT INTO episodes (
                        entertainment_id, season_id, name, episode_number, poster_url, 
                        short_desc, description, duration, release_date, access, status, 
                        tmdb_id, tmdb_season, created_by, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
                    
                    $posterUrl = null;
                    if (!empty($episode['still_path'])) {
                        $posterUrl = "https://image.tmdb.org/t/p/w500" . $episode['still_path'];
                    }
                    
                    $stmt = $pdo->prepare($sql);
                    $result = $stmt->execute([
                        $season['entertainment_id'],
                        $seasonId,
                        $episode['name'] ?? 'Épisode ' . $episode['episode_number'],
                        $episode['episode_number'],
                        $posterUrl,
                        null,
                        $episode['overview'] ?? null,
                        !empty($episode['runtime']) ? $episode['runtime'] . ' min' : null,
                        $episode['air_date'] ?? null,
                        'free',
                        1,
                        $episode['id'] ?? null,
                        $episode['season_number'] ?? null,
                        1
                    ]);
                    
                    if ($result) {
                        $importedCount++;
                    }
                }
            } else {
                // Import pour les données IPTV
                $stmt = $pdo->prepare("SELECT poster_iptv_id FROM season_iptv WHERE id = ?");
                $stmt->execute([$seasonId]);
                $season = $stmt->fetch();
                
                if (!$season) {
                    echo json_encode(['success' => false, 'message' => 'Saison IPTV non trouvée']);
                    exit;
                }
                
                $importedCount = 0;
                $episodes = $tmdbData['episodes'] ?? [];
                
                foreach ($episodes as $episode) {
                    // Vérifier si l'épisode existe déjà
                    $stmt = $pdo->prepare("SELECT id FROM episode_iptv WHERE season_iptv_id = ? AND episode_number = ?");
                    $stmt->execute([$seasonId, $episode['episode_number']]);
                    
                    if ($stmt->fetch()) {
                        continue; // Épisode déjà existant
                    }
                    
                    $sql = "INSERT INTO episode_iptv (
                        season_iptv_id, episode_number, title, description, created_at
                    ) VALUES (?, ?, ?, ?, NOW())";
                    
                    $stmt = $pdo->prepare($sql);
                    $result = $stmt->execute([
                        $seasonId,
                        $episode['episode_number'],
                        $episode['name'] ?? 'Épisode ' . $episode['episode_number'],
                        $episode['overview'] ?? null
                    ]);
                    
                    if ($result) {
                        $importedCount++;
                    }
                }
            }
            
            echo json_encode(['success' => true, 'message' => "$importedCount épisode(s) importé(s) avec succès"]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'delete_season') {
        $seasonId = $_POST['season_id'] ?? '';
        
        if (!$seasonId) {
            echo json_encode(['success' => false, 'message' => 'ID manquant']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                // Soft delete de la saison et des épisodes principales
                $stmt = $pdo->prepare("UPDATE seasons SET deleted_at = NOW() WHERE id = ?");
                $result1 = $stmt->execute([$seasonId]);
                
                $stmt = $pdo->prepare("UPDATE episodes SET deleted_at = NOW() WHERE season_id = ?");
                $result2 = $stmt->execute([$seasonId]);
            } else {
                // Suppression des données IPTV (suppression réelle)
                $stmt = $pdo->prepare("DELETE FROM episode_iptv WHERE season_iptv_id = ?");
                $result2 = $stmt->execute([$seasonId]);
                
                $stmt = $pdo->prepare("DELETE FROM season_iptv WHERE id = ?");
                $result1 = $stmt->execute([$seasonId]);
            }
            
            if ($result1) {
                echo json_encode(['success' => true, 'message' => 'Saison supprimée avec succès']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la suppression']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'delete_episode') {
        $episodeId = $_POST['episode_id'] ?? '';
        
        if (!$episodeId) {
            echo json_encode(['success' => false, 'message' => 'ID manquant']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                // Soft delete de l'épisode principal
                $stmt = $pdo->prepare("UPDATE episodes SET deleted_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$episodeId]);
            } else {
                // Suppression de l'épisode IPTV (suppression réelle)
                $stmt = $pdo->prepare("DELETE FROM episode_iptv WHERE id = ?");
                $result = $stmt->execute([$episodeId]);
            }
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Épisode supprimé avec succès']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la suppression']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'get_episode_sources') {
        $episodeId = $_POST['episode_id'] ?? '';
        
        if (!$episodeId) {
            echo json_encode(['success' => false, 'message' => 'ID manquant']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                // Pour la base principale, pas de sources séparées
                echo json_encode(['success' => true, 'sources' => []]);
            } else {
                // Récupérer les sources de l'épisode IPTV
                $stmt = $pdo->prepare("SELECT id, url, quality, language FROM source_episode_iptv WHERE episode_iptv_id = ? ORDER BY id");
                $stmt->execute([$episodeId]);
                $sources = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo json_encode(['success' => true, 'sources' => $sources]);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'add_episode_source') {
        $episodeId = $_POST['episode_id'] ?? '';
        $sourceData = json_decode($_POST['source_data'] ?? '{}', true);
        
        if (!$episodeId || empty($sourceData)) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                echo json_encode(['success' => false, 'message' => 'Sources non supportées pour la base principale']);
            } else {
                // Ajouter une source pour l'épisode IPTV
                $sql = "INSERT INTO source_episode_iptv (episode_iptv_id, url, quality, language, created_at) VALUES (?, ?, ?, ?, NOW())";
                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute([
                    $episodeId,
                    $sourceData['url'] ?? '',
                    $sourceData['quality'] ?? 'HD',
                    $sourceData['language'] ?? 'fr'
                ]);
                
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'Source ajoutée avec succès']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Erreur lors de l\'ajout']);
                }
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'delete_episode_source') {
        $sourceId = $_POST['source_id'] ?? '';
        
        if (!$sourceId) {
            echo json_encode(['success' => false, 'message' => 'ID manquant']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                echo json_encode(['success' => false, 'message' => 'Sources non supportées pour la base principale']);
            } else {
                // Supprimer la source IPTV
                $stmt = $pdo->prepare("DELETE FROM source_episode_iptv WHERE id = ?");
                $result = $stmt->execute([$sourceId]);
                
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'Source supprimée avec succès']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Erreur lors de la suppression']);
                }
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'get_movie_sources') {
        $movieId = $_POST['movie_id'] ?? '';
        
        if (!$movieId) {
            echo json_encode(['success' => false, 'message' => 'ID manquant']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                // Pour la base principale, pas de sources séparées
                echo json_encode(['success' => true, 'sources' => []]);
            } else {
                // Récupérer les sources du film IPTV
                $stmt = $pdo->prepare("SELECT id, url, quality, language FROM source_iptv WHERE poster_iptv_id = ? ORDER BY id");
                $stmt->execute([$movieId]);
                $sources = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo json_encode(['success' => true, 'sources' => $sources]);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'add_movie_source') {
        $movieId = $_POST['movie_id'] ?? '';
        $sourceData = json_decode($_POST['source_data'] ?? '{}', true);
        
        if (!$movieId || empty($sourceData)) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                echo json_encode(['success' => false, 'message' => 'Sources non supportées pour la base principale']);
            } else {
                // Ajouter une source pour le film IPTV
                $sql = "INSERT INTO source_iptv (poster_iptv_id, url, quality, language) VALUES (?, ?, ?, ?)";
                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute([
                    $movieId,
                    $sourceData['url'] ?? '',
                    $sourceData['quality'] ?? 'HD',
                    $sourceData['language'] ?? 'fr'
                ]);
                
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'Source ajoutée avec succès']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Erreur lors de l\'ajout']);
                }
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'delete_movie_source') {
        $sourceId = $_POST['source_id'] ?? '';
        
        if (!$sourceId) {
            echo json_encode(['success' => false, 'message' => 'ID manquant']);
            exit;
        }
        
        try {
            if ($sourceDb === 'main') {
                echo json_encode(['success' => false, 'message' => 'Sources non supportées pour la base principale']);
            } else {
                // Supprimer la source IPTV du film
                $stmt = $pdo->prepare("DELETE FROM source_iptv WHERE id = ?");
                $result = $stmt->execute([$sourceId]);
                
                if ($result) {
                    echo json_encode(['success' => true, 'message' => 'Source supprimée avec succès']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Erreur lors de la suppression']);
                }
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
}

// Interface principale
$entertainmentId = $_GET['entertainment_id'] ?? '';
$sourceDb = $_GET['source_db'] ?? 'main';
$title = $_GET['title'] ?? '';

if (!$entertainmentId) {
    die("ID de série manquant");
}

try {
    // Déterminer la source de données et la connexion appropriée
    if ($sourceDb === 'main') {
        $pdo = getConnection($config['main_db']);
        $entertainment = getEntertainmentInfo($pdo, $entertainmentId, 'main');
        
        if (!$entertainment) {
            die("Série non trouvée dans la base principale");
        }
        
        $seasons = getSeasons($pdo, $entertainmentId, 'main');
    } else {
        // Pour les données IPTV, chercher la configuration correspondante
        $iptvConfig = null;
        
        // Chercher dans les configurations IPTV disponibles
        if (isset($config[$sourceDb])) {
            $iptvConfig = $config[$sourceDb];
        }
        
        if (!$iptvConfig) {
            die("Base de données IPTV '$sourceDb' non configurée");
        }
        
        $pdo = getConnection($iptvConfig);
        $entertainment = getEntertainmentInfo($pdo, $entertainmentId, 'iptv');
        
        if (!$entertainment) {
            // Debug : afficher les informations pour diagnostiquer
            die("Série non trouvée dans la base IPTV '$sourceDb'. " . 
                "Vérifiez que l'ID $entertainmentId existe dans la table poster_iptv de " .
                $iptvConfig['dbname'] . " sur " . $iptvConfig['host']);
        }
        
        $seasons = getSeasons($pdo, $entertainmentId, 'iptv');
        
        // Pour les données IPTV, ajuster les champs pour la compatibilité d'affichage
        if ($entertainment && !isset($entertainment['name'])) {
            $entertainment['name'] = $entertainment['title'] ?? $title;
            $entertainment['type'] = 'tvshow'; // Présumer que c'est une série
        }
    }
} catch (Exception $e) {
    $error = "Erreur: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Saisons - <?= htmlspecialchars($entertainment['name'] ?? $title) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/phosphor-icons@2.0.0/src/css/icons.css" rel="stylesheet">
    
    <style>
        .season-card { 
            border-left: 4px solid #667eea; 
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .season-card:hover { 
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }
        .episode-card { 
            border-left: 3px solid #6c757d; 
            transition: all 0.2s ease;
        }
        .episode-card:hover { 
            border-left-color: #667eea; 
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .badge {
            font-size: 0.75rem;
            padding: 0.35em 0.65em;
        }
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="ph ph-television me-2"></i>
                VOD IPTV
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="search.php">
                    <i class="ph ph-magnifying-glass me-1"></i>
                    Recherche
                </a>
                <a class="nav-link active" href="#">
                    <i class="ph ph-list-numbers me-1"></i>
                    Saisons
                </a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <!-- En-tête de la série -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card text-white" style="background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #667eea 100%); border: none; box-shadow: 0 10px 25px rgba(0,0,0,0.1);">
                    <div class="card-body py-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="mb-2 fw-bold">
                                    <i class="ph ph-television me-2"></i>
                                    <?= htmlspecialchars($entertainment['name'] ?? $title) ?>
                                </h1>
                                <p class="mb-0">
                                    <span class="badge bg-white bg-opacity-20 me-2">
                                        <i class="ph ph-database me-1"></i>
                                        <?= $sourceDb === 'main' ? 'Base principale' : "IPTV ($sourceDb)" ?>
                                    </span>
                                    <?php if (!empty($entertainment['tmdb_id'])): ?>
                                        <span class="badge bg-warning bg-opacity-90 text-dark me-2">
                                            <i class="ph ph-film-strip me-1"></i>
                                            TMDB: <?= htmlspecialchars($entertainment['tmdb_id']) ?>
                                        </span>
                                    <?php endif; ?>
                                    <span class="badge bg-success bg-opacity-90 me-2">
                                        <i class="ph ph-play me-1"></i>
                                        <?= ucfirst($entertainment['type'] ?? 'tvshow') ?>
                                    </span>
                                    <span class="badge bg-info bg-opacity-90">
                                        <i class="ph ph-list-numbers me-1"></i>
                                        <?= count($seasons) ?> saison(s)
                                    </span>
                                </p>
                            </div>
                            <div class="text-end">
                                <a href="search.php" class="btn btn-light btn-lg me-3 fw-semibold">
                                    <i class="ph ph-arrow-left me-2"></i>
                                    Retour
                                </a>
                                <?php if ($sourceDb !== 'main' && ($entertainment['type'] ?? '') === 'movie'): ?>
                                    <button class="btn btn-success btn-lg me-3 fw-semibold" onclick="showMovieSourcesModal(<?= $entertainmentId ?>, '<?= htmlspecialchars($entertainment['name'] ?? $title, ENT_QUOTES) ?>')">
                                        <i class="ph ph-play me-2"></i>
                                        Gérer Sources Film
                                    </button>
                                <?php endif; ?>
                                <button class="btn btn-warning btn-lg fw-semibold" onclick="showCreateSeasonModal()">
                                    <i class="ph ph-plus me-2"></i>
                                    Nouvelle Saison
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="ph ph-warning me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- Liste des saisons -->
        <div class="row">
            <?php if (empty($seasons)): ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="ph ph-list-numbers text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">Aucune saison trouvée</h4>
                        <p class="text-muted">Commencez par créer votre première saison</p>
                        <button class="btn btn-warning btn-lg" onclick="showCreateSeasonModal()">
                            <i class="ph ph-plus me-2"></i>
                            Créer la première saison
                        </button>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($seasons as $season): ?>
                    <div class="col-12 mb-4">
                        <div class="card season-card">
                            <div class="card-header text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="ph ph-list-numbers me-2"></i>
                                        <?= htmlspecialchars($season['name'] ?? ('Saison ' . ($season['season_number'] ?? $season['season_index'] ?? '?'))) ?>
                                        <span class="badge bg-white bg-opacity-20 ms-2 text-dark">
                                            <?= $season['episode_count'] ?> épisode(s)
                                        </span>
                                    </h5>
                                    <div>
                                        <button class="btn btn-light btn-sm me-2" 
                                                onclick="showCreateEpisodeModal(<?= $season['id'] ?>, '<?= htmlspecialchars($season['name'] ?? ('Saison ' . ($season['season_number'] ?? $season['season_index'] ?? '?')), ENT_QUOTES) ?>')">
                                            <i class="ph ph-plus me-1"></i>
                                            Ajouter Épisode
                                        </button>
                                        <button class="btn btn-warning btn-sm me-2" 
                                                onclick="loadTmdbSeason(<?= $season['id'] ?>, '<?= htmlspecialchars($entertainment['tmdb_id'] ?? '', ENT_QUOTES) ?>', <?= $season['season_index'] ?>)">
                                            <i class="ph ph-download-simple me-1"></i>
                                            TMDB
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteSeason(<?= $season['id'] ?>, '<?= htmlspecialchars($season['name'] ?? ('Saison ' . ($season['season_number'] ?? $season['season_index'] ?? '?')), ENT_QUOTES) ?>')">
                                            <i class="ph ph-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                                                                <?php if (!empty($season['description'])): ?>
                                    <p class="text-muted mb-3"><?= htmlspecialchars($season['description'] ?? '') ?></p>
                                <?php endif; ?>
                                
                                <!-- Episodes de cette saison -->
                                <div id="episodes-season-<?= $season['id'] ?>">
                                    <?php
                                    $episodes = getEpisodes($pdo, $season['id'], $sourceDb === 'main' ? 'main' : 'iptv');
                                    if (empty($episodes)):
                                    ?>
                                        <div class="text-center py-3 text-muted">
                                            <i class="ph ph-film-strip me-1"></i>
                                            Aucun épisode pour cette saison
                                        </div>
                                    <?php else: ?>
                                        <div class="row g-3">
                                            <?php foreach ($episodes as $episode): ?>
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="card episode-card h-100">
                                                        <div class="card-body p-3">
                                                            <div class="d-flex gap-3">
                                                                <div class="flex-shrink-0">
                                                                    <div class="episode-number d-flex align-items-center justify-content-center" 
                                                                         style="width: 40px; height: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 50%; font-weight: bold; font-size: 0.9rem;">
                                                                        <?= htmlspecialchars($episode['episode_number'] ?? '?') ?>
                                                                    </div>
                                                                </div>
                                                                <div class="flex-grow-1">
                                                                    <h6 class="card-title mb-1" style="font-size: 0.9rem;">
                                                                        <?= htmlspecialchars($episode['name'] ?? ('Épisode ' . ($episode['episode_number'] ?? '?'))) ?>
                                                                    </h6>
                                                                    <?php if (!empty($episode['duration'])): ?>
                                                                        <small class="text-muted d-block">
                                                                            <i class="ph ph-clock me-1"></i>
                                                                            <?= htmlspecialchars($episode['duration'] ?? '') ?>
                                                                        </small>
                                                                    <?php endif; ?>
                                                                    <?php if (!empty($episode['release_date'])): ?>
                                                                        <small class="text-muted d-block">
                                                                            <i class="ph ph-calendar me-1"></i>
                                                                            <?= date('d/m/Y', strtotime($episode['release_date'])) ?>
                                                                        </small>
                                                                    <?php endif; ?>
                                                                    <?php if ($sourceDb !== 'main' && !empty($episode['sources'])): ?>
                                                                        <small class="text-success d-block">
                                                                            <i class="ph ph-play me-1"></i>
                                                                            <?= count(explode('|', $episode['sources'])) ?> source(s) vidéo
                                                                        </small>
                                                                    <?php endif; ?>
                                                                </div>
                                                                <div class="flex-shrink-0">
                                                                    <?php if ($sourceDb !== 'main'): ?>
                                                                        <button class="btn btn-warning btn-sm me-2" 
                                                                                onclick="showSourcesModal(<?= $episode['id'] ?>, '<?= htmlspecialchars($episode['name'] ?? ('Épisode ' . ($episode['episode_number'] ?? '?')), ENT_QUOTES) ?>')"
                                                                                title="Gérer les sources">
                                                                            <i class="ph ph-play me-1"></i>
                                                                            Sources
                                                                        </button>
                                                                    <?php endif; ?>
                                                                    <button class="btn btn-outline-danger btn-sm" 
                                                                            onclick="deleteEpisode(<?= $episode['id'] ?>, '<?= htmlspecialchars($episode['name'] ?? ('Épisode ' . ($episode['episode_number'] ?? '?')), ENT_QUOTES) ?>', <?= $season['id'] ?>)"
                                                                            title="Supprimer">
                                                                        <i class="ph ph-trash" style="font-size: 0.8rem;"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Toast container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body" id="successMessage"></div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
        <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body" id="errorMessage"></div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    </div>

    <!-- Modal Créer Saison -->
    <div class="modal fade" id="createSeasonModal" tabindex="-1" aria-labelledby="createSeasonModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createSeasonModalLabel">
                        <i class="ph ph-plus me-2"></i>
                        Créer une nouvelle saison
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createSeasonForm">
                        <div class="mb-3">
                            <label for="seasonName" class="form-label">Nom de la saison</label>
                            <input type="text" class="form-control" id="seasonName" placeholder="Ex: Saison 1" required>
                        </div>
                        <div class="mb-3">
                            <label for="seasonIndex" class="form-label">Numéro de saison</label>
                            <input type="number" class="form-control" id="seasonIndex" value="<?= count($seasons) + 1 ?>" min="1" required>
                        </div>
                        <div class="mb-3">
                            <label for="seasonDescription" class="form-label">Description <small class="text-muted">(optionnel)</small></label>
                            <textarea class="form-control" id="seasonDescription" rows="3" placeholder="Description de la saison..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="createSeason()">
                        <i class="ph ph-plus me-1"></i>
                        Créer la saison
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Créer Épisode -->
    <div class="modal fade" id="createEpisodeModal" tabindex="-1" aria-labelledby="createEpisodeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createEpisodeModalLabel">
                        <i class="ph ph-plus me-2"></i>
                        Ajouter un épisode
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createEpisodeForm">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="episodeName" class="form-label">Nom de l'épisode</label>
                                    <input type="text" class="form-control" id="episodeName" placeholder="Ex: Épisode 1" required>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="episodeNumber" class="form-label">Numéro d'épisode</label>
                                            <input type="number" class="form-control" id="episodeNumber" value="1" min="1" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="episodeDuration" class="form-label">Durée <small class="text-muted">(optionnel)</small></label>
                                            <input type="text" class="form-control" id="episodeDuration" placeholder="Ex: 45:00">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="episodeReleaseDate" class="form-label">Date de sortie <small class="text-muted">(optionnel)</small></label>
                                    <input type="date" class="form-control" id="episodeReleaseDate">
                                </div>
                                <div class="mb-3">
                                    <label for="episodeDescription" class="form-label">Description <small class="text-muted">(optionnel)</small></label>
                                    <textarea class="form-control" id="episodeDescription" rows="3" placeholder="Description de l'épisode..."></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <label class="form-label">Poster de l'épisode</label>
                                    <div class="border rounded p-3 bg-light">
                                        <div id="episodePosterPreview" style="height: 150px; display: flex; align-items: center; justify-content: center;">
                                            <i class="ph ph-image text-muted" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <input type="text" class="form-control form-control-sm" id="episodePosterUrl" placeholder="URL du poster...">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="targetSeasonId">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="createEpisode()">
                        <i class="ph ph-plus me-1"></i>
                        Créer l'épisode
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Gérer Sources -->
    <div class="modal fade" id="sourcesModal" tabindex="-1" aria-labelledby="sourcesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sourcesModalLabel">
                        <i class="ph ph-play me-2"></i>
                        Gérer les sources vidéo
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">
                            <i class="ph ph-film-strip me-1"></i>
                            <span id="currentEpisodeName">Épisode</span>
                        </h6>
                        
                        <!-- Formulaire d'ajout de source -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <i class="ph ph-plus me-1"></i>
                                Ajouter une nouvelle source
                            </div>
                            <div class="card-body">
                                <form id="addSourceForm">
                                    <input type="hidden" id="sourceEpisodeId" value="">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="sourceUrl" class="form-label">URL de la source *</label>
                                            <input type="url" class="form-control" id="sourceUrl" placeholder="https://..." required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="sourceQuality" class="form-label">Qualité</label>
                                            <select class="form-select" id="sourceQuality">
                                                <option value="HD">HD</option>
                                                <option value="720p">720p</option>
                                                <option value="1080p">1080p</option>
                                                <option value="4K">4K</option>
                                                <option value="SD">SD</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="sourceLanguage" class="form-label">Langue</label>
                                            <select class="form-select" id="sourceLanguage">
                                                <option value="fr">Français</option>
                                                <option value="en">Anglais</option>
                                                <option value="es">Espagnol</option>
                                                <option value="de">Allemand</option>
                                                <option value="it">Italien</option>
                                                <option value="vo">Version originale</option>
                                            </select>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="addEpisodeSource()">
                                        <i class="ph ph-plus me-1"></i>
                                        Ajouter cette source
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Liste des sources existantes -->
                        <div class="card">
                            <div class="card-header">
                                <i class="ph ph-list me-1"></i>
                                Sources existantes
                            </div>
                            <div class="card-body">
                                <div id="sourcesList">
                                    <div class="text-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Chargement...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Gérer Sources Film -->
    <div class="modal fade" id="movieSourcesModal" tabindex="-1" aria-labelledby="movieSourcesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="movieSourcesModalLabel">
                        <i class="ph ph-film-strip me-2"></i>
                        Gérer les sources du film
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">
                            <i class="ph ph-film-strip me-1"></i>
                            <span id="currentMovieName">Film</span>
                        </h6>
                        
                        <!-- Formulaire d'ajout de source -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <i class="ph ph-plus me-1"></i>
                                Ajouter une nouvelle source
                            </div>
                            <div class="card-body">
                                <form id="addMovieSourceForm">
                                    <input type="hidden" id="sourceMovieId" value="">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="movieSourceUrl" class="form-label">URL de la source *</label>
                                            <input type="url" class="form-control" id="movieSourceUrl" placeholder="https://..." required>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="movieSourceQuality" class="form-label">Qualité</label>
                                            <select class="form-select" id="movieSourceQuality">
                                                <option value="HD">HD</option>
                                                <option value="720p">720p</option>
                                                <option value="1080p">1080p</option>
                                                <option value="4K">4K</option>
                                                <option value="SD">SD</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="movieSourceLanguage" class="form-label">Langue</label>
                                            <select class="form-select" id="movieSourceLanguage">
                                                <option value="fr">Français</option>
                                                <option value="en">Anglais</option>
                                                <option value="es">Espagnol</option>
                                                <option value="de">Allemand</option>
                                                <option value="it">Italien</option>
                                                <option value="vo">Version originale</option>
                                            </select>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="addMovieSource()">
                                        <i class="ph ph-plus me-1"></i>
                                        Ajouter cette source
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Liste des sources existantes -->
                        <div class="card">
                            <div class="card-header">
                                <i class="ph ph-list me-1"></i>
                                Sources existantes
                            </div>
                            <div class="card-body">
                                <div id="movieSourcesList">
                                    <div class="text-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Chargement...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Import TMDB -->
    <div class="modal fade" id="importTmdbModal" tabindex="-1" aria-labelledby="importTmdbModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importTmdbModalLabel">
                        <i class="ph ph-download-simple me-2"></i>
                        Importer depuis TMDB
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="tmdbLoadingArea" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2 text-muted">Chargement des données TMDB...</p>
                    </div>
                    <div id="tmdbDataArea" style="display: none;">
                        <div class="alert alert-info">
                            <i class="ph ph-info me-2"></i>
                            Prévisualisation des épisodes à importer
                        </div>
                        <div id="tmdbEpisodesList"></div>
                        <input type="hidden" id="tmdbSeasonId">
                        <input type="hidden" id="tmdbSeasonData">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="importTmdbBtn" onclick="importTmdbEpisodes()" style="display: none;">
                        <i class="ph ph-download-simple me-1"></i>
                        Importer les épisodes
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function showToast(type, message) {
            const toast = document.getElementById(type + 'Toast');
            const messageEl = document.getElementById(type + 'Message');
            messageEl.textContent = message;
            new bootstrap.Toast(toast).show();
        }

        function showCreateSeasonModal() {
            const modal = new bootstrap.Modal(document.getElementById('createSeasonModal'));
            modal.show();
        }

        function showCreateEpisodeModal(seasonId, seasonName) {
            document.getElementById('targetSeasonId').value = seasonId;
            document.getElementById('createEpisodeModalLabel').innerHTML = `
                <i class="ph ph-plus me-2"></i>
                Ajouter un épisode à ${seasonName}
            `;
            
            // Calculer le prochain numéro d'épisode
            const episodeCards = document.querySelectorAll(`#episodes-season-${seasonId} .episode-number`);
            const nextEpisodeNumber = episodeCards.length + 1;
            document.getElementById('episodeNumber').value = nextEpisodeNumber;
            document.getElementById('episodeName').value = `Épisode ${nextEpisodeNumber}`;
            
            const modal = new bootstrap.Modal(document.getElementById('createEpisodeModal'));
            modal.show();
        }

        function createSeason() {
            const seasonData = {
                name: document.getElementById('seasonName').value,
                season_index: document.getElementById('seasonIndex').value,
                description: document.getElementById('seasonDescription').value
            };
            
            if (!seasonData.name || !seasonData.season_index) {
                showToast('error', 'Veuillez remplir les champs obligatoires');
                return;
            }
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=create_season&source_db=<?= $sourceDb ?>&entertainment_id=<?= $entertainmentId ?>&season_data=${encodeURIComponent(JSON.stringify(seasonData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    bootstrap.Modal.getInstance(document.getElementById('createSeasonModal')).hide();
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', 'Erreur lors de la création');
            });
        }

        function createEpisode() {
            const episodeData = {
                name: document.getElementById('episodeName').value,
                episode_number: document.getElementById('episodeNumber').value,
                duration: document.getElementById('episodeDuration').value,
                release_date: document.getElementById('episodeReleaseDate').value,
                description: document.getElementById('episodeDescription').value,
                poster_url: document.getElementById('episodePosterUrl').value
            };
            
            const seasonId = document.getElementById('targetSeasonId').value;
            
            if (!episodeData.name || !episodeData.episode_number) {
                showToast('error', 'Veuillez remplir les champs obligatoires');
                return;
            }
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=create_episode&source_db=<?= $sourceDb ?>&season_id=${seasonId}&episode_data=${encodeURIComponent(JSON.stringify(episodeData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    bootstrap.Modal.getInstance(document.getElementById('createEpisodeModal')).hide();
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', 'Erreur lors de la création');
            });
        }

        function deleteSeason(seasonId, seasonName) {
            if (!confirm(`Êtes-vous sûr de vouloir supprimer "${seasonName}" et tous ses épisodes ?`)) {
                return;
            }
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=delete_season&source_db=<?= $sourceDb ?>&season_id=${seasonId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', 'Erreur lors de la suppression');
            });
        }

        function deleteEpisode(episodeId, episodeName, seasonId) {
            if (!confirm(`Êtes-vous sûr de vouloir supprimer "${episodeName}" ?`)) {
                return;
            }
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=delete_episode&source_db=<?= $sourceDb ?>&episode_id=${episodeId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', 'Erreur lors de la suppression');
            });
        }

        function loadTmdbSeason(seasonId, tmdbId, seasonNumber) {
            if (!tmdbId) {
                showToast('error', 'Aucun TMDB ID trouvé pour cette série');
                return;
            }
            
            // Afficher le modal et charger les données
            const modal = new bootstrap.Modal(document.getElementById('importTmdbModal'));
            modal.show();
            
            document.getElementById('tmdbSeasonId').value = seasonId;
            document.getElementById('tmdbLoadingArea').style.display = 'block';
            document.getElementById('tmdbDataArea').style.display = 'none';
            document.getElementById('importTmdbBtn').style.display = 'none';
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=get_tmdb_season&tmdb_id=${tmdbId}&season_number=${seasonNumber}`
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('tmdbLoadingArea').style.display = 'none';
                
                if (data.success) {
                    document.getElementById('tmdbSeasonData').value = JSON.stringify(data.season_data);
                    displayTmdbEpisodes(data.season_data.episodes);
                    document.getElementById('tmdbDataArea').style.display = 'block';
                    document.getElementById('importTmdbBtn').style.display = 'inline-block';
                } else {
                    showToast('error', data.message);
                    modal.hide();
                }
            })
            .catch(error => {
                document.getElementById('tmdbLoadingArea').style.display = 'none';
                showToast('error', 'Erreur lors du chargement TMDB');
                modal.hide();
            });
        }

        function displayTmdbEpisodes(episodes) {
            const container = document.getElementById('tmdbEpisodesList');
            container.innerHTML = '';
            
            episodes.forEach(episode => {
                const episodeCard = document.createElement('div');
                episodeCard.className = 'card mb-2';
                episodeCard.innerHTML = `
                    <div class="card-body p-3">
                        <div class="d-flex gap-3">
                            <div class="flex-shrink-0">
                                <div class="episode-number">${episode.episode_number}</div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${episode.name}</h6>
                                <small class="text-muted">
                                    ${episode.air_date ? new Date(episode.air_date).toLocaleDateString('fr-FR') : 'Date inconnue'}
                                    ${episode.runtime ? ` • ${episode.runtime} min` : ''}
                                </small>
                                ${episode.overview ? `<p class="mb-0 mt-1" style="font-size: 0.8rem;">${episode.overview.substring(0, 150)}${episode.overview.length > 150 ? '...' : ''}</p>` : ''}
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(episodeCard);
            });
        }

        function importTmdbEpisodes() {
            const seasonId = document.getElementById('tmdbSeasonId').value;
            const tmdbData = document.getElementById('tmdbSeasonData').value;
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=import_tmdb_episodes&source_db=<?= $sourceDb ?>&season_id=${seasonId}&tmdb_data=${encodeURIComponent(tmdbData)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    bootstrap.Modal.getInstance(document.getElementById('importTmdbModal')).hide();
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', 'Erreur lors de l\'importation');
            });
        }

        // Gestion des sources d'épisodes
        function showSourcesModal(episodeId, episodeName) {
            document.getElementById('sourceEpisodeId').value = episodeId;
            document.getElementById('currentEpisodeName').textContent = episodeName;
            
            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById('sourcesModal'));
            modal.show();
            
            // Charger les sources existantes
            loadEpisodeSources(episodeId);
            
            // Réinitialiser le formulaire
            document.getElementById('addSourceForm').reset();
        }
        
        function loadEpisodeSources(episodeId) {
            document.getElementById('sourcesList').innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            `;
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=get_episode_sources&source_db=<?= $sourceDb ?>&episode_id=${episodeId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySources(data.sources);
                } else {
                    document.getElementById('sourcesList').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="ph ph-warning me-1"></i>
                            ${data.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('sourcesList').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="ph ph-warning me-1"></i>
                        Erreur lors du chargement des sources
                    </div>
                `;
            });
        }
        
        function displaySources(sources) {
            const container = document.getElementById('sourcesList');
            
            if (sources.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-3 text-muted">
                        <i class="ph ph-film-strip" style="font-size: 2rem;"></i>
                        <p class="mb-0 mt-2">Aucune source vidéo configurée</p>
                        <small>Ajoutez votre première source ci-dessus</small>
                    </div>
                `;
                return;
            }
            
            let html = '';
            sources.forEach((source, index) => {
                const languageNames = {
                    'fr': 'Français',
                    'en': 'Anglais', 
                    'es': 'Espagnol',
                    'de': 'Allemand',
                    'it': 'Italien',
                    'vo': 'Version originale'
                };
                
                html += `
                    <div class="d-flex align-items-center justify-content-between border rounded p-3 mb-2 ${index === 0 ? 'bg-light' : ''}">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center gap-2 mb-1">
                                <span class="badge bg-primary">${source.quality}</span>
                                <span class="badge bg-secondary">${languageNames[source.language] || source.language}</span>
                                ${index === 0 ? '<span class="badge bg-success">Principale</span>' : ''}
                            </div>
                            <div class="text-break" style="font-size: 0.9rem;">
                                <i class="ph ph-link me-1"></i>
                                <span class="text-muted">${source.url.length > 60 ? source.url.substring(0, 60) + '...' : source.url}</span>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <button class="btn btn-outline-danger btn-sm" 
                                    onclick="deleteEpisodeSource(${source.id})"
                                    title="Supprimer cette source">
                                <i class="ph ph-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function addEpisodeSource() {
            const episodeId = document.getElementById('sourceEpisodeId').value;
            const sourceData = {
                url: document.getElementById('sourceUrl').value,
                quality: document.getElementById('sourceQuality').value,
                language: document.getElementById('sourceLanguage').value
            };
            
            if (!sourceData.url) {
                showToast('error', 'Veuillez saisir l\'URL de la source');
                return;
            }
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=add_episode_source&source_db=<?= $sourceDb ?>&episode_id=${episodeId}&source_data=${encodeURIComponent(JSON.stringify(sourceData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    document.getElementById('addSourceForm').reset();
                    loadEpisodeSources(episodeId);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', 'Erreur lors de l\'ajout de la source');
            });
        }
        
        function deleteEpisodeSource(sourceId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette source ?')) {
                return;
            }
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=delete_episode_source&source_db=<?= $sourceDb ?>&source_id=${sourceId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    // Recharger les sources de l'épisode en cours
                    const episodeId = document.getElementById('sourceEpisodeId').value;
                    loadEpisodeSources(episodeId);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', 'Erreur lors de la suppression');
            });
        }

        // Gestion des sources de films
        function showMovieSourcesModal(movieId, movieName) {
            document.getElementById('sourceMovieId').value = movieId;
            document.getElementById('currentMovieName').textContent = movieName;
            
            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById('movieSourcesModal'));
            modal.show();
            
            // Charger les sources existantes
            loadMovieSources(movieId);
            
            // Réinitialiser le formulaire
            document.getElementById('addMovieSourceForm').reset();
        }
        
        function loadMovieSources(movieId) {
            document.getElementById('movieSourcesList').innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            `;
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=get_movie_sources&source_db=<?= $sourceDb ?>&movie_id=${movieId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayMovieSources(data.sources);
                } else {
                    document.getElementById('movieSourcesList').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="ph ph-warning me-1"></i>
                            ${data.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('movieSourcesList').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="ph ph-warning me-1"></i>
                        Erreur lors du chargement des sources
                    </div>
                `;
            });
        }
        
        function displayMovieSources(sources) {
            const container = document.getElementById('movieSourcesList');
            
            if (sources.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-3 text-muted">
                        <i class="ph ph-film-strip" style="font-size: 2rem;"></i>
                        <p class="mb-0 mt-2">Aucune source vidéo configurée</p>
                        <small>Ajoutez votre première source ci-dessus</small>
                    </div>
                `;
                return;
            }
            
            let html = '';
            sources.forEach((source, index) => {
                const languageNames = {
                    'fr': 'Français',
                    'en': 'Anglais', 
                    'es': 'Espagnol',
                    'de': 'Allemand',
                    'it': 'Italien',
                    'vo': 'Version originale'
                };
                
                html += `
                    <div class="d-flex align-items-center justify-content-between border rounded p-3 mb-2 ${index === 0 ? 'bg-light' : ''}">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center gap-2 mb-1">
                                <span class="badge bg-primary">${source.quality}</span>
                                <span class="badge bg-secondary">${languageNames[source.language] || source.language}</span>
                                ${index === 0 ? '<span class="badge bg-success">Principale</span>' : ''}
                            </div>
                            <div class="text-break" style="font-size: 0.9rem;">
                                <i class="ph ph-link me-1"></i>
                                <span class="text-muted">${source.url.length > 60 ? source.url.substring(0, 60) + '...' : source.url}</span>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <button class="btn btn-outline-danger btn-sm" 
                                    onclick="deleteMovieSource(${source.id})"
                                    title="Supprimer cette source">
                                <i class="ph ph-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function addMovieSource() {
            const movieId = document.getElementById('sourceMovieId').value;
            const sourceData = {
                url: document.getElementById('movieSourceUrl').value,
                quality: document.getElementById('movieSourceQuality').value,
                language: document.getElementById('movieSourceLanguage').value
            };
            
            if (!sourceData.url) {
                showToast('error', 'Veuillez saisir l\'URL de la source');
                return;
            }
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=add_movie_source&source_db=<?= $sourceDb ?>&movie_id=${movieId}&source_data=${encodeURIComponent(JSON.stringify(sourceData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    document.getElementById('addMovieSourceForm').reset();
                    loadMovieSources(movieId);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', 'Erreur lors de l\'ajout de la source');
            });
        }
        
        function deleteMovieSource(sourceId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette source ?')) {
                return;
            }
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=delete_movie_source&source_db=<?= $sourceDb ?>&source_id=${sourceId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    // Recharger les sources du film en cours
                    const movieId = document.getElementById('sourceMovieId').value;
                    loadMovieSources(movieId);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', 'Erreur lors de la suppression');
            });
        }

        // Aperçu du poster d'épisode
        document.addEventListener('DOMContentLoaded', function() {
            const posterInput = document.getElementById('episodePosterUrl');
            if (posterInput) {
                posterInput.addEventListener('input', function() {
                    const url = this.value;
                    const preview = document.getElementById('episodePosterPreview');
                    
                    if (url && url.startsWith('http')) {
                        preview.innerHTML = `<img src="${url}" alt="Poster preview" style="max-width: 100%; height: auto; border-radius: 4px;" onerror="this.parentElement.innerHTML='<i class=\\'ph ph-image-broken text-muted\\' style=\\'font-size: 2rem;\\'></i><br><small class=\\'text-danger\\'>Image introuvable</small>';">`;
                    } else {
                        preview.innerHTML = '<i class="ph ph-image text-muted" style="font-size: 2rem;"></i>';
                    }
                });
            }
        });
    </script>
</body>
</html>
