<?php
/**
 * Script de diagnostic pour les variables d'environnement
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Configuration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">

<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h3>🔍 Diagnostic Configuration - Variables d'environnement</h3>
        </div>
        <div class="card-body">
            
            <?php
            echo "<h5>1. Localisation du fichier .env</h5>";
            $envFile = dirname(__DIR__, 2) . '/.env';
            echo "<p><code>$envFile</code></p>";
            echo "<p>Existe: " . (file_exists($envFile) ? "✅ Oui" : "❌ Non") . "</p>";
            
            if (file_exists($envFile)) {
                echo "<h5>2. Contenu brut des lignes UPDATE_IPTV</h5>";
                $content = file_get_contents($envFile);
                $lines = explode("\n", $content);
                
                echo "<pre class='bg-dark text-light p-3'>";
                foreach ($lines as $lineNum => $line) {
                    if (strpos($line, 'UPDATE_IPTV') !== false) {
                        echo "Ligne " . ($lineNum + 1) . ": " . htmlspecialchars($line) . "\n";
                        
                        // Analyser la ligne
                        echo "  → Longueur: " . strlen($line) . " caractères\n";
                        echo "  → Caractères invisibles: ";
                        for ($i = 0; $i < strlen($line); $i++) {
                            $char = $line[$i];
                            if (ord($char) < 32 || ord($char) > 126) {
                                echo "[" . ord($char) . "]";
                            }
                        }
                        echo "\n";
                        echo "  → Contient '=': " . (strpos($line, '=') !== false ? "Oui à position " . strpos($line, '=') : "Non") . "\n\n";
                    }
                }
                echo "</pre>";
                
                echo "<h5>3. Test du parser de variables</h5>";
                
                // Charger avec notre fonction
                try {
                    include_once 'config.php';
                    echo "<div class='alert alert-success'>✅ config.php chargé avec succès</div>";
                    
                    // Récupérer les variables UPDATE_IPTV spécifiquement
                    $envLines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                    $parsedVars = [];
                    
                    foreach ($envLines as $line) {
                        if (strpos($line, '#') === 0) continue;
                        if (strpos($line, 'UPDATE_IPTV') !== false) {
                            echo "<p><strong>Analyse de:</strong> <code>" . htmlspecialchars($line) . "</code></p>";
                            
                            $parts = explode('=', $line, 2);
                            if (count($parts) === 2) {
                                $key = trim($parts[0]);
                                $value = trim($parts[1]);
                                
                                echo "<ul>";
                                echo "<li>Clé: <code>" . htmlspecialchars($key) . "</code></li>";
                                echo "<li>Valeur brute: <code>" . htmlspecialchars($value) . "</code></li>";
                                
                                // Traitement des guillemets
                                if (preg_match('/^([\'"])(.*)\1$/', $value, $matches)) {
                                    $value = $matches[2];
                                    echo "<li>Valeur après suppression guillemets: <code>" . htmlspecialchars($value) . "</code></li>";
                                }
                                
                                $parsedVars[$key] = $value;
                                echo "<li>Valeur finale: <code>" . htmlspecialchars($value) . "</code></li>";
                                echo "<li>Vide ? " . (empty($value) ? "❌ Oui" : "✅ Non") . "</li>";
                                echo "</ul><hr>";
                            } else {
                                echo "<p class='text-danger'>❌ Impossible de parser cette ligne (pas de =)</p>";
                            }
                        }
                    }
                    
                    echo "<h5>4. Variables finales parsées</h5>";
                    echo "<pre class='bg-light p-3'>";
                    var_dump($parsedVars);
                    echo "</pre>";
                    
                    echo "<h5>5. Test classe M3UImportV2</h5>";
                    try {
                        // Simuler le chargement comme dans la classe
                        $config = require 'config.php';
                        echo "<p>✅ Configuration chargée</p>";
                        
                        $urls = [
                            'iptv1' => $config['update_iptv1_url'] ?? '',
                            'iptv2' => $config['update_iptv2_url'] ?? '',
                            'test' => __DIR__ . '/test_playlist.m3u'
                        ];
                        
                        echo "<pre class='bg-info text-white p-3'>";
                        echo "URLs disponibles:\n";
                        foreach ($urls as $key => $url) {
                            echo "$key: " . ($url ? "✅ " . htmlspecialchars($url) : "❌ VIDE") . "\n";
                        }
                        echo "</pre>";
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>❌ Erreur classe: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>❌ Erreur config.php: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
            ?>
            
            <hr>
            <div class="mt-3">
                <a href="import_m3u_v2.php" class="btn btn-primary">← Retour Import M3U V2</a>
                <a href="check_system.php" class="btn btn-outline-secondary">Check Système</a>
            </div>
            
        </div>
    </div>
</div>

</body>
</html> 