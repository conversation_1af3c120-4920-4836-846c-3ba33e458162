<?php
// Page de statistiques du système VOD IPTV

// Charger la configuration
$config = require_once 'config.php';

function getConnection($dbConfig) {
    try {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    } catch (PDOException $e) {
        return null;
    }
}

function getStats($pdo, $dbName) {
    if (!$pdo) {
        return [
            'total' => 0,
            'movies' => 0,
            'tvshows' => 0,
            'with_tmdb' => 0,
            'without_tmdb' => 0,
            'error' => 'Connexion impossible'
        ];
    }
    
    try {
        if ($dbName === 'main_db') {
            // Structure de la base principale (entertainments)
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM entertainments WHERE deleted_at IS NULL");
            $total = $stmt->fetch()['count'];
            
            // Par type
            $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM entertainments WHERE deleted_at IS NULL GROUP BY type");
            $byType = [];
            while ($row = $stmt->fetch()) {
                $byType[$row['type']] = $row['count'];
            }
            
            // Avec/sans TMDB ID
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM entertainments WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND deleted_at IS NULL");
            $withTmdb = $stmt->fetch()['count'];
        } else {
            // Structure des bases IPTV (poster_iptv)
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM poster_iptv");
            $total = $stmt->fetch()['count'];
            
            // Par type
            $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM poster_iptv GROUP BY type");
            $byType = [];
            while ($row = $stmt->fetch()) {
                $byType[$row['type']] = $row['count'];
            }
            
            // Avec/sans TMDB ID
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM poster_iptv WHERE tmdb_id IS NOT NULL AND tmdb_id != ''");
            $withTmdb = $stmt->fetch()['count'];
        }
        
        return [
            'total' => $total,
            'movies' => $byType['movie'] ?? 0,
            'tvshows' => ($byType['tvshow'] ?? 0) + ($byType['tv_show'] ?? 0) + ($byType['series'] ?? 0),
            'with_tmdb' => $withTmdb,
            'without_tmdb' => $total - $withTmdb,
            'error' => null
        ];
    } catch (Exception $e) {
        return [
            'total' => 0,
            'movies' => 0,
            'tvshows' => 0,
            'with_tmdb' => 0,
            'without_tmdb' => 0,
            'error' => $e->getMessage()
        ];
    }
}

// Récupérer les statistiques
$mainPdo = getConnection($config['main_db']);
$iptvPdo1 = getConnection($config['iptv_db']);
$iptvPdo2 = getConnection($config['iptv_db2']);

$mainStats = getStats($mainPdo, 'main_db');
$iptv1Stats = getStats($iptvPdo1, 'iptv_db');
$iptv2Stats = getStats($iptvPdo2, 'iptv_db2');

// Gestion des filtres et listes avec pagination
$selectedDb = $_GET['db'] ?? null;
$selectedFilter = $_GET['filter'] ?? null;
$currentPage = (int) ($_GET['page'] ?? 1);
$perPage = 25; // Nombre d'éléments par page
$filterResults = [];
$filterTitle = '';
$totalResults = 0;
$totalPages = 0;

if ($selectedDb && $selectedFilter) {
    $paginationData = getFilteredResults($selectedDb, $selectedFilter, $mainPdo, $iptvPdo1, $iptvPdo2, $currentPage, $perPage);
    $filterResults = $paginationData['results'];
    $totalResults = $paginationData['total'];
    $totalPages = $paginationData['totalPages'];
    $filterTitle = getFilterTitle($selectedDb, $selectedFilter);
}

function getFilteredResults($db, $filter, $mainPdo, $iptvPdo1, $iptvPdo2, $page = 1, $perPage = 25) {
    $offset = ($page - 1) * $perPage;
    $results = [];
    $total = 0;
    
    try {
        if ($filter === 'title_mismatch') {
            // Traitement spécial pour les correspondances de titres
            $data = findTitleMismatchesStats($db, $mainPdo, $iptvPdo1, $iptvPdo2, $perPage, $offset);
            $results = $data['results'];
            $total = $data['total'];
        } else {
            // Traitement normal pour les autres filtres
            switch ($db) {
                case 'main_db':
                    if (!$mainPdo) return ['results' => [], 'total' => 0, 'totalPages' => 0];
                    $data = getMainDbResults($mainPdo, $filter, $perPage, $offset);
                    $results = $data['results'];
                    $total = $data['total'];
                    break;
                case 'iptv_db':
                    if (!$iptvPdo1) return ['results' => [], 'total' => 0, 'totalPages' => 0];
                    $data = getIptvDbResults($iptvPdo1, $filter, $perPage, $offset);
                    $results = $data['results'];
                    $total = $data['total'];
                    break;
                case 'iptv_db2':
                    if (!$iptvPdo2) return ['results' => [], 'total' => 0, 'totalPages' => 0];
                    $data = getIptvDbResults($iptvPdo2, $filter, $perPage, $offset);
                    $results = $data['results'];
                    $total = $data['total'];
                    break;
            }
        }
    } catch (Exception $e) {
        // Log error if needed
    }
    
    $totalPages = $total > 0 ? ceil($total / $perPage) : 0;
    
    return [
        'results' => $results,
        'total' => $total,
        'totalPages' => $totalPages
    ];
}

function getMainDbResults($pdo, $filter, $limit, $offset) {
    $baseWhere = "WHERE deleted_at IS NULL";
    
    switch ($filter) {
        case 'series':
            $baseWhere .= " AND type = 'tvshow'";
            break;
        case 'movies':
            $baseWhere .= " AND type = 'movie'";
            break;
        case 'series_with_imdb':
            $baseWhere .= " AND type = 'tvshow' AND (tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0')";
            break;
        case 'series_without_imdb':
            $baseWhere .= " AND type = 'tvshow' AND (tmdb_id IS NULL OR tmdb_id = '' OR tmdb_id = '0')";
            break;
        case 'movies_with_imdb':
            $baseWhere .= " AND type = 'movie' AND (tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0')";
            break;
        case 'movies_without_imdb':
            $baseWhere .= " AND type = 'movie' AND (tmdb_id IS NULL OR tmdb_id = '' OR tmdb_id = '0')";
            break;
    }
    
    // Compter le total
    $countSql = "SELECT COUNT(*) as total FROM entertainments {$baseWhere}";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute();
    $total = $countStmt->fetch()['total'];
    
    // Récupérer les résultats avec pagination
    $sql = "SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, IMDb_rating, 
                   poster_url, created_at 
            FROM entertainments 
            {$baseWhere}
            ORDER BY created_at DESC 
            LIMIT {$limit} OFFSET {$offset}";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll();
    
    return [
        'results' => $results,
        'total' => $total
    ];
}

function getIptvDbResults($pdo, $filter, $limit, $offset) {
    $baseWhere = "WHERE 1=1";
    
    switch ($filter) {
        case 'series':
            $baseWhere .= " AND type IN ('tvshow', 'series', 'tv')";
            break;
        case 'movies':
            $baseWhere .= " AND type = 'movie'";
            break;
        case 'series_with_imdb':
            $baseWhere .= " AND type IN ('tvshow', 'series', 'tv') AND (tmdb_id IS NOT NULL AND tmdb_id != '')";
            break;
        case 'series_without_imdb':
            $baseWhere .= " AND type IN ('tvshow', 'series', 'tv') AND (tmdb_id IS NULL OR tmdb_id = '')";
            break;
        case 'movies_with_imdb':
            $baseWhere .= " AND type = 'movie' AND (tmdb_id IS NOT NULL AND tmdb_id != '')";
            break;
        case 'movies_without_imdb':
            $baseWhere .= " AND type = 'movie' AND (tmdb_id IS NULL OR tmdb_id = '')";
            break;
    }
    
    // Compter le total
    $countSql = "SELECT COUNT(*) as total FROM poster_iptv {$baseWhere}";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute();
    $total = $countStmt->fetch()['total'];
    
    // Récupérer les résultats avec pagination
    $sql = "SELECT id, title, type, year, tmdb_id, poster_path, poster_image, created_at 
            FROM poster_iptv 
            {$baseWhere}
            ORDER BY created_at DESC 
            LIMIT {$limit} OFFSET {$offset}";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll();
    
    return [
        'results' => $results,
        'total' => $total
    ];
}

function findTitleMismatchesStats($db, $mainPdo, $iptvPdo1, $iptvPdo2, $limit, $offset) {
    $results = [];
    $total = 0;
    
    try {
        // D'abord compter le total pour la pagination
        $countResults = [];
        
        if ($db === 'main_db') {
            $stmt = $mainPdo->prepare("SELECT COUNT(*) as total FROM entertainments WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0' AND deleted_at IS NULL");
            $stmt->execute();
            $total = $stmt->fetch()['total'];
            
            // Récupérer les éléments avec pagination
            $stmt = $mainPdo->prepare("SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id FROM entertainments WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0' AND deleted_at IS NULL ORDER BY created_at DESC LIMIT {$limit} OFFSET {$offset}");
            $stmt->execute();
            $items = $stmt->fetchAll();
            
        } elseif ($db === 'iptv_db') {
            $stmt = $iptvPdo1->prepare("SELECT COUNT(*) as total FROM poster_iptv WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'");
            $stmt->execute();
            $total = $stmt->fetch()['total'];
            
            // Récupérer les éléments avec pagination
            $stmt = $iptvPdo1->prepare("SELECT id, title, type, year, tmdb_id FROM poster_iptv WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0' ORDER BY created_at DESC LIMIT {$limit} OFFSET {$offset}");
            $stmt->execute();
            $items = $stmt->fetchAll();
            
        } elseif ($db === 'iptv_db2') {
            $stmt = $iptvPdo2->prepare("SELECT COUNT(*) as total FROM poster_iptv WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'");
            $stmt->execute();
            $total = $stmt->fetch()['total'];
            
            // Récupérer les éléments avec pagination
            $stmt = $iptvPdo2->prepare("SELECT id, title, type, year, tmdb_id FROM poster_iptv WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0' ORDER BY created_at DESC LIMIT {$limit} OFFSET {$offset}");
            $stmt->execute();
            $items = $stmt->fetchAll();
        }
        
        // Pour chaque élément récupéré, chercher les correspondances par titre dans les autres bases
        $matchItems = [];
        foreach ($items as $item) {
            $matches = [];
            
            // Déterminer le type standardisé pour la recherche
            $searchType = $item['type'];
            if (in_array($item['type'], ['tvshow', 'series', 'tv'])) {
                $searchType = 'tvshow'; // Type standardisé pour les séries
            }
            
            // Chercher dans la base principale si l'item ne vient pas de là
            if ($db !== 'main_db') {
                $stmt = $mainPdo->prepare("
                    SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id 
                    FROM entertainments 
                    WHERE name = ? AND tmdb_id != ? AND tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'
                    AND (type = ? OR (type = 'tvshow' AND ? IN ('tvshow', 'series', 'tv')))
                    AND deleted_at IS NULL
                ");
                $stmt->execute([$item['title'], $item['tmdb_id'], $searchType, $item['type']]);
                $mainMatches = $stmt->fetchAll();
                foreach ($mainMatches as $match) {
                    $match['db_source'] = 'main_db';
                    $matches[] = $match;
                }
            }
            
            // Chercher dans IPTV DB 1 si l'item ne vient pas de là
            if ($db !== 'iptv_db') {
                $stmt = $iptvPdo1->prepare("
                    SELECT id, title, type, year, tmdb_id 
                    FROM poster_iptv 
                    WHERE title = ? AND tmdb_id != ? AND tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'
                    AND (type = ? OR (type IN ('tvshow', 'series', 'tv') AND ? IN ('tvshow', 'series', 'tv')))
                ");
                $stmt->execute([$item['title'], $item['tmdb_id'], $item['type'], $searchType]);
                $iptv1Matches = $stmt->fetchAll();
                foreach ($iptv1Matches as $match) {
                    $match['db_source'] = 'iptv_db';
                    $matches[] = $match;
                }
            }
            
            // Chercher dans IPTV DB 2 si l'item ne vient pas de là
            if ($db !== 'iptv_db2') {
                $stmt = $iptvPdo2->prepare("
                    SELECT id, title, type, year, tmdb_id 
                    FROM poster_iptv 
                    WHERE title = ? AND tmdb_id != ? AND tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'
                    AND (type = ? OR (type IN ('tvshow', 'series', 'tv') AND ? IN ('tvshow', 'series', 'tv')))
                ");
                $stmt->execute([$item['title'], $item['tmdb_id'], $item['type'], $searchType]);
                $iptv2Matches = $stmt->fetchAll();
                foreach ($iptv2Matches as $match) {
                    $match['db_source'] = 'iptv_db2';
                    $matches[] = $match;
                }
            }
            
            // Si on a trouvé des correspondances, ajouter aux résultats
            if (!empty($matches)) {
                $item['title_matches'] = $matches;
                $item['match_count'] = count($matches);
                $matchItems[] = $item;
            }
        }
        
        // Retourner seulement les éléments avec correspondances mais garder le total original pour la pagination
        return [
            'results' => $matchItems,
            'total' => count($matchItems), // Pour cette fonction spéciale, on compte les matchs réels
            'original_total' => $total // Garder le total original si besoin
        ];
        
    } catch (Exception $e) {
        error_log("Erreur dans findTitleMismatchesStats: " . $e->getMessage());
        return ['results' => [], 'total' => 0];
    }
}

function getFilterTitle($db, $filter) {
    $dbNames = [
        'main_db' => 'Base principale',
        'iptv_db' => 'IPTV DB 1',
        'iptv_db2' => 'IPTV DB 2'
    ];
    
    $filterNames = [
        'series' => 'Toutes les séries',
        'movies' => 'Tous les films',
        'series_with_imdb' => 'Séries avec TMDB',
        'series_without_imdb' => 'Séries sans TMDB',
        'movies_with_imdb' => 'Films avec TMDB',
        'movies_without_imdb' => 'Films sans TMDB',
        'title_mismatch' => 'Titres identiques, TMDB différents'
    ];
    
    return ($filterNames[$filter] ?? 'Filtre') . ' - ' . ($dbNames[$db] ?? 'Base inconnue');
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistiques - VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/phosphor-icons@2.0.0/src/css/icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="ph ph-television me-2"></i>
                VOD IPTV
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="search.php">
                    <i class="ph ph-magnifying-glass me-1"></i>
                    Recherche
                </a>
                <a class="nav-link active" href="stats.php">
                    <i class="ph ph-chart-bar me-1"></i>
                    Stats
                </a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="mb-0">
                        <i class="ph ph-chart-bar me-2"></i>
                        Statistiques
                    </h1>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="ph ph-house me-1"></i>
                        Accueil
                    </a>
                </div>

                <div class="row">
                    <!-- Base principale -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="ph ph-database me-2"></i>
                                    Base principale
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if ($mainStats['error']): ?>
                                    <div class="alert alert-danger">
                                        <i class="ph ph-warning me-2"></i>
                                        <?= htmlspecialchars($mainStats['error']) ?>
                                    </div>
                                <?php else: ?>
                                    <div class="row text-center">
                                        <div class="col-12 mb-3">
                                            <h2 class="text-success"><?= number_format($mainStats['total']) ?></h2>
                                            <small class="text-muted">Total entertainments</small>
                                        </div>
                                        <div class="col-6">
                                            <h4><?= number_format($mainStats['movies']) ?></h4>
                                            <small class="text-muted">Films</small>
                                        </div>
                                        <div class="col-6">
                                            <h4><?= number_format($mainStats['tvshows']) ?></h4>
                                            <small class="text-muted">Séries</small>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <span class="badge bg-success"><?= number_format($mainStats['with_tmdb']) ?></span>
                                            <small class="d-block text-muted">Avec TMDB</small>
                                        </div>
                                        <div class="col-6">
                                            <span class="badge bg-warning"><?= number_format($mainStats['without_tmdb']) ?></span>
                                            <small class="d-block text-muted">Sans TMDB</small>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- IPTV DB 1 -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="ph ph-satellite-dish me-2"></i>
                                    IPTV DB 1
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if ($iptv1Stats['error']): ?>
                                    <div class="alert alert-danger">
                                        <i class="ph ph-warning me-2"></i>
                                        <?= htmlspecialchars($iptv1Stats['error']) ?>
                                    </div>
                                <?php else: ?>
                                    <div class="row text-center">
                                        <div class="col-12 mb-3">
                                            <h2 class="text-info"><?= number_format($iptv1Stats['total']) ?></h2>
                                            <small class="text-muted">Total posters</small>
                                        </div>
                                        <div class="col-6">
                                            <h4><?= number_format($iptv1Stats['movies']) ?></h4>
                                            <small class="text-muted">Films</small>
                                        </div>
                                        <div class="col-6">
                                            <h4><?= number_format($iptv1Stats['tvshows']) ?></h4>
                                            <small class="text-muted">Séries</small>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <span class="badge bg-success"><?= number_format($iptv1Stats['with_tmdb']) ?></span>
                                            <small class="d-block text-muted">Avec TMDB</small>
                                        </div>
                                        <div class="col-6">
                                            <span class="badge bg-warning"><?= number_format($iptv1Stats['without_tmdb']) ?></span>
                                            <small class="d-block text-muted">Sans TMDB</small>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- IPTV DB 2 -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="ph ph-satellite-dish me-2"></i>
                                    IPTV DB 2
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if ($iptv2Stats['error']): ?>
                                    <div class="alert alert-danger">
                                        <i class="ph ph-warning me-2"></i>
                                        <?= htmlspecialchars($iptv2Stats['error']) ?>
                                    </div>
                                <?php else: ?>
                                    <div class="row text-center">
                                        <div class="col-12 mb-3">
                                            <h2 class="text-secondary"><?= number_format($iptv2Stats['total']) ?></h2>
                                            <small class="text-muted">Total posters</small>
                                        </div>
                                        <div class="col-6">
                                            <h4><?= number_format($iptv2Stats['movies']) ?></h4>
                                            <small class="text-muted">Films</small>
                                        </div>
                                        <div class="col-6">
                                            <h4><?= number_format($iptv2Stats['tvshows']) ?></h4>
                                            <small class="text-muted">Séries</small>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <span class="badge bg-success"><?= number_format($iptv2Stats['with_tmdb']) ?></span>
                                            <small class="d-block text-muted">Avec TMDB</small>
                                        </div>
                                        <div class="col-6">
                                            <span class="badge bg-warning"><?= number_format($iptv2Stats['without_tmdb']) ?></span>
                                            <small class="d-block text-muted">Sans TMDB</small>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Résumé global -->
                <?php if (!$iptv1Stats['error'] && !$iptv2Stats['error']): ?>
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="ph ph-chart-pie me-2"></i>
                                        Résumé global IPTV
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <h3 class="text-primary"><?= number_format($iptv1Stats['total'] + $iptv2Stats['total']) ?></h3>
                                            <small class="text-muted">Total IPTV</small>
                                        </div>
                                        <div class="col-md-3">
                                            <h3 class="text-info"><?= number_format($iptv1Stats['movies'] + $iptv2Stats['movies']) ?></h3>
                                            <small class="text-muted">Films IPTV</small>
                                        </div>
                                        <div class="col-md-3">
                                            <h3 class="text-warning"><?= number_format($iptv1Stats['tvshows'] + $iptv2Stats['tvshows']) ?></h3>
                                            <small class="text-muted">Séries IPTV</small>
                                        </div>
                                        <div class="col-md-3">
                                            <h3 class="text-success"><?= number_format($iptv1Stats['with_tmdb'] + $iptv2Stats['with_tmdb']) ?></h3>
                                            <small class="text-muted">Avec TMDB</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Section de filtrage et listing -->
                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="ph ph-funnel me-2"></i>
                                Listes et filtres avancés
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- Base principale -->
                                <div class="col-md-4">
                                    <h6 class="text-success">
                                        <i class="ph ph-database me-1"></i>
                                        Base principale
                                    </h6>
                                    <div class="d-grid gap-2">
                                        <a href="?db=main_db&filter=series" class="btn btn-outline-primary btn-sm">
                                            <i class="ph ph-television me-1"></i>
                                            Toutes les séries
                                        </a>
                                        <a href="?db=main_db&filter=movies" class="btn btn-outline-primary btn-sm">
                                            <i class="ph ph-film-strip me-1"></i>
                                            Tous les films
                                        </a>
                                        <a href="?db=main_db&filter=series_with_imdb" class="btn btn-outline-success btn-sm">
                                            <i class="ph ph-check-circle me-1"></i>
                                            Séries avec TMDB
                                        </a>
                                        <a href="?db=main_db&filter=series_without_imdb" class="btn btn-outline-warning btn-sm">
                                            <i class="ph ph-warning me-1"></i>
                                            Séries sans TMDB
                                        </a>
                                        <a href="?db=main_db&filter=movies_with_imdb" class="btn btn-outline-success btn-sm">
                                            <i class="ph ph-check-circle me-1"></i>
                                            Films avec TMDB
                                        </a>
                                        <a href="?db=main_db&filter=movies_without_imdb" class="btn btn-outline-warning btn-sm">
                                            <i class="ph ph-warning me-1"></i>
                                            Films sans TMDB
                                        </a>
                                        <a href="?db=main_db&filter=title_mismatch" class="btn btn-outline-danger btn-sm">
                                            <i class="ph ph-warning-diamond me-1"></i>
                                            Titres = / TMDB ≠
                                        </a>
                                    </div>
                                </div>
                                
                                <!-- IPTV DB 1 -->
                                <div class="col-md-4">
                                    <h6 class="text-info">
                                        <i class="ph ph-satellite-dish me-1"></i>
                                        IPTV DB 1
                                    </h6>
                                    <div class="d-grid gap-2">
                                        <a href="?db=iptv_db&filter=series" class="btn btn-outline-primary btn-sm">
                                            <i class="ph ph-television me-1"></i>
                                            Toutes les séries
                                        </a>
                                        <a href="?db=iptv_db&filter=movies" class="btn btn-outline-primary btn-sm">
                                            <i class="ph ph-film-strip me-1"></i>
                                            Tous les films
                                        </a>
                                        <a href="?db=iptv_db&filter=series_with_imdb" class="btn btn-outline-success btn-sm">
                                            <i class="ph ph-check-circle me-1"></i>
                                            Séries avec TMDB
                                        </a>
                                        <a href="?db=iptv_db&filter=series_without_imdb" class="btn btn-outline-warning btn-sm">
                                            <i class="ph ph-warning me-1"></i>
                                            Séries sans TMDB
                                        </a>
                                        <a href="?db=iptv_db&filter=movies_with_imdb" class="btn btn-outline-success btn-sm">
                                            <i class="ph ph-check-circle me-1"></i>
                                            Films avec TMDB
                                        </a>
                                        <a href="?db=iptv_db&filter=movies_without_imdb" class="btn btn-outline-warning btn-sm">
                                            <i class="ph ph-warning me-1"></i>
                                            Films sans TMDB
                                        </a>
                                        <a href="?db=iptv_db&filter=title_mismatch" class="btn btn-outline-danger btn-sm">
                                            <i class="ph ph-warning-diamond me-1"></i>
                                            Titres = / TMDB ≠
                                        </a>
                                    </div>
                                </div>
                                
                                <!-- IPTV DB 2 -->
                                <div class="col-md-4">
                                    <h6 class="text-secondary">
                                        <i class="ph ph-satellite-dish me-1"></i>
                                        IPTV DB 2
                                    </h6>
                                    <div class="d-grid gap-2">
                                        <a href="?db=iptv_db2&filter=series" class="btn btn-outline-primary btn-sm">
                                            <i class="ph ph-television me-1"></i>
                                            Toutes les séries
                                        </a>
                                        <a href="?db=iptv_db2&filter=movies" class="btn btn-outline-primary btn-sm">
                                            <i class="ph ph-film-strip me-1"></i>
                                            Tous les films
                                        </a>
                                        <a href="?db=iptv_db2&filter=series_with_imdb" class="btn btn-outline-success btn-sm">
                                            <i class="ph ph-check-circle me-1"></i>
                                            Séries avec TMDB
                                        </a>
                                        <a href="?db=iptv_db2&filter=series_without_imdb" class="btn btn-outline-warning btn-sm">
                                            <i class="ph ph-warning me-1"></i>
                                            Séries sans TMDB
                                        </a>
                                        <a href="?db=iptv_db2&filter=movies_with_imdb" class="btn btn-outline-success btn-sm">
                                            <i class="ph ph-check-circle me-1"></i>
                                            Films avec TMDB
                                        </a>
                                        <a href="?db=iptv_db2&filter=movies_without_imdb" class="btn btn-outline-warning btn-sm">
                                            <i class="ph ph-warning me-1"></i>
                                            Films sans TMDB
                                        </a>
                                        <a href="?db=iptv_db2&filter=title_mismatch" class="btn btn-outline-danger btn-sm">
                                            <i class="ph ph-warning-diamond me-1"></i>
                                            Titres = / TMDB ≠
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Affichage des résultats de filtrage -->
                <?php if (!empty($filterResults) && $filterTitle): ?>
                <div class="mt-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="ph ph-list me-2"></i>
                                <?= htmlspecialchars($filterTitle) ?>
                            </h5>
                            <span class="badge bg-primary"><?= number_format($totalResults) ?> résultats total</span>
                            <span class="badge bg-secondary">Page <?= $currentPage ?>/<?= $totalPages ?></span>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Titre</th>
                                            <th>Type</th>
                                            <th>Année</th>
                                            <th>TMDB ID</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($filterResults as $item): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary"><?= htmlspecialchars($item['id']) ?></span>
                                            </td>
                                            <td>
                                                <strong><?= htmlspecialchars($item['title']) ?></strong>
                                                
                                                                                                 <?php if ($selectedFilter === 'title_mismatch' && !empty($item['title_matches'])): ?>
                                                     <div class="mt-2">
                                                         <small class="text-danger fw-bold">
                                                             <i class="ph ph-warning-diamond me-1"></i>
                                                             <?= $item['match_count'] ?> conflit(s) détecté(s) :
                                                         </small>
                                                         <?php foreach ($item['title_matches'] as $match): ?>
                                                             <div class="mt-1">
                                                                 <div class="d-flex justify-content-between align-items-center bg-light p-1 rounded" style="font-size: 0.8rem;">
                                                                     <div>
                                                                         <span class="badge bg-danger text-white me-1" style="font-size: 0.65rem;">
                                                                             <?= htmlspecialchars($match['db_source']) ?>
                                                                         </span>
                                                                         <small class="text-dark" style="font-size: 0.7rem;">
                                                                             ID: <?= $match['id'] ?> | 
                                                                             Type: <?= ucfirst($match['type']) ?> | 
                                                                             TMDB: <?= htmlspecialchars($match['tmdb_id']) ?>
                                                                             <?php if (!empty($match['year'])): ?>
                                                                                 | Année: <?= htmlspecialchars($match['year']) ?>
                                                                             <?php endif; ?>
                                                                         </small>
                                                                     </div>
                                                                     <button 
                                                                         class="btn btn-outline-warning btn-xs"
                                                                         style="font-size: 0.6rem; padding: 1px 3px;"
                                                                         onclick="searchTmdbForCorrect(<?= $match['id'] ?>, '<?= $match['db_source'] ?>', '<?= htmlspecialchars($item['title'], ENT_QUOTES) ?>', '<?= $match['type'] ?>')"
                                                                         title="Corriger le TMDB ID de cet élément en conflit"
                                                                     >
                                                                         <i class="ph ph-wrench me-1"></i>
                                                                         Corriger
                                                                     </button>
                                                                 </div>
                                                             </div>
                                                         <?php endforeach; ?>
                                                     </div>
                                                 <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge <?= $item['type'] === 'movie' ? 'bg-info' : 'bg-success' ?>">
                                                    <i class="ph <?= $item['type'] === 'movie' ? 'ph-film-strip' : 'ph-television' ?> me-1"></i>
                                                    <?= ucfirst($item['type']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($item['year'])): ?>
                                                    <span class="text-muted"><?= htmlspecialchars($item['year']) ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($item['tmdb_id'])): ?>
                                                    <span class="badge bg-success">
                                                        <i class="ph ph-database me-1"></i>
                                                        <?= htmlspecialchars($item['tmdb_id']) ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($selectedFilter === 'title_mismatch'): ?>
                                                    <!-- Pour les correspondances : bouton principal "Corriger" -->
                                                    <button 
                                                        class="btn btn-warning btn-sm"
                                                        onclick="searchTmdbForCorrect(<?= $item['id'] ?>, '<?= $selectedDb ?>', '<?= htmlspecialchars($item['title'], ENT_QUOTES) ?>', '<?= $item['type'] ?>')"
                                                    >
                                                        <i class="ph ph-wrench me-1"></i>
                                                        Corriger TMDB
                                                    </button>
                                                <?php elseif ($selectedDb === 'main_db'): ?>
                                                    <!-- Base principale : toujours bouton corriger -->
                                                    <button 
                                                        class="btn btn-warning btn-sm"
                                                        onclick="searchTmdbForCorrect(<?= $item['id'] ?>, '<?= $selectedDb ?>', '<?= htmlspecialchars($item['title'], ENT_QUOTES) ?>', '<?= $item['type'] ?>')"
                                                    >
                                                        <i class="ph ph-wrench me-1"></i>
                                                        Corriger
                                                    </button>
                                                <?php else: ?>
                                                    <!-- Bases IPTV : boutons selon TMDB ID -->
                                                    <?php if (!empty($item['tmdb_id'])): ?>
                                                        <!-- Avec TMDB ID : bouton Ajouter -->
                                                        <button 
                                                            class="btn btn-success btn-sm"
                                                            onclick="searchTmdbForAdd(<?= $item['id'] ?>, '<?= $selectedDb ?>', '<?= htmlspecialchars($item['title'], ENT_QUOTES) ?>', '<?= $item['type'] ?>')"
                                                        >
                                                            <i class="ph ph-plus me-1"></i>
                                                            Ajouter
                                                        </button>
                                                    <?php else: ?>
                                                        <!-- Sans TMDB ID : boutons Corriger et Ajouter -->
                                                        <div class="btn-group" role="group">
                                                            <button 
                                                                class="btn btn-warning btn-sm"
                                                                onclick="searchTmdbForCorrect(<?= $item['id'] ?>, '<?= $selectedDb ?>', '<?= htmlspecialchars($item['title'], ENT_QUOTES) ?>', '<?= $item['type'] ?>')"
                                                            >
                                                                <i class="ph ph-wrench me-1"></i>
                                                                Corriger
                                                            </button>
                                                            <button 
                                                                class="btn btn-success btn-sm"
                                                                onclick="searchTmdbForAdd(<?= $item['id'] ?>, '<?= $selectedDb ?>', '<?= htmlspecialchars($item['title'], ENT_QUOTES) ?>', '<?= $item['type'] ?>')"
                                                            >
                                                                <i class="ph ph-plus me-1"></i>
                                                                Ajouter
                                                            </button>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                            <nav aria-label="Navigation pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <!-- Première page -->
                                    <?php if ($currentPage > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?db=<?= $selectedDb ?>&filter=<?= $selectedFilter ?>&page=1">
                                                <i class="ph ph-caret-double-left"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?db=<?= $selectedDb ?>&filter=<?= $selectedFilter ?>&page=<?= $currentPage - 1 ?>">
                                                <i class="ph ph-caret-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <!-- Pages autour de la page actuelle -->
                                    <?php
                                    $startPage = max(1, $currentPage - 2);
                                    $endPage = min($totalPages, $currentPage + 2);
                                    
                                    for ($i = $startPage; $i <= $endPage; $i++):
                                    ?>
                                        <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                                            <a class="page-link" href="?db=<?= $selectedDb ?>&filter=<?= $selectedFilter ?>&page=<?= $i ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <!-- Dernière page -->
                                    <?php if ($currentPage < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?db=<?= $selectedDb ?>&filter=<?= $selectedFilter ?>&page=<?= $currentPage + 1 ?>">
                                                <i class="ph ph-caret-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?db=<?= $selectedDb ?>&filter=<?= $selectedFilter ?>&page=<?= $totalPages ?>">
                                                <i class="ph ph-caret-double-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                            
                            <!-- Informations de pagination -->
                            <div class="text-center text-muted mb-3">
                                Affichage de <?= number_format(($currentPage - 1) * $perPage + 1) ?> à 
                                <?= number_format(min($currentPage * $perPage, $totalResults)) ?> 
                                sur <?= number_format($totalResults) ?> résultats
                            </div>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <a href="stats.php" class="btn btn-secondary">
                                    <i class="ph ph-arrow-left me-1"></i>
                                    Retour aux statistiques
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="mt-4">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="ph ph-info me-2"></i>
                                Informations
                            </h6>
                            <ul class="list-unstyled mb-0">
                                <li>• Les statistiques sont mises à jour en temps réel</li>
                                <li>• Seuls les contenus non présents dans la base principale sont proposés à l'ajout</li>
                                <li>• Les éléments "Sans TMDB" peuvent être corrigés via l'API TMDB</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour sélection TMDB -->
    <div class="modal fade" id="tmdbModal" tabindex="-1" aria-labelledby="tmdbModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tmdbModalLabel">
                        <i class="ph ph-magnifying-glass me-2"></i>
                        Choisir le contenu TMDB
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="tmdbResults"></div>
                    <div id="tmdbLoading" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Recherche en cours...</span>
                        </div>
                        <p class="mt-2">Recherche sur TMDB...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast notifications -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body" id="successMessage"></div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
        <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body" id="errorMessage"></div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentItemId, currentSourceDb, isCorrectMode = false;

        function showToast(type, message) {
            const toast = document.getElementById(type + 'Toast');
            const messageEl = document.getElementById(type + 'Message');
            messageEl.textContent = message;
            new bootstrap.Toast(toast).show();
        }

        function searchTmdbForAdd(itemId, sourceDb, title, type) {
            currentItemId = itemId;
            currentSourceDb = sourceDb;
            isCorrectMode = false;
            
            // Stocker le type pour les recherches personnalisées
            window.currentSearchType = type;
            
            // Changer le titre de la modal
            document.getElementById('tmdbModalLabel').innerHTML = `
                <i class="ph ph-magnifying-glass me-2"></i>
                Choisir le contenu TMDB à ajouter
            `;
            
            searchTmdbCommon(title, type);
        }

        function searchTmdbForCorrect(itemId, sourceDb, title, type) {
            currentItemId = itemId;
            currentSourceDb = sourceDb;
            isCorrectMode = true;
            
            // Stocker le type pour les recherches personnalisées
            window.currentSearchType = type;
            
            // Changer le titre de la modal
            document.getElementById('tmdbModalLabel').innerHTML = `
                <i class="ph ph-wrench me-2"></i>
                Choisir le bon contenu TMDB pour corriger
            `;
            
            searchTmdbCommon(title, type);
        }
        
        function searchTmdbCommon(title, type) {
            // Afficher la modal et le loader
            const modal = new bootstrap.Modal(document.getElementById('tmdbModal'));
            modal.show();
            
            document.getElementById('tmdbLoading').style.display = 'block';
            document.getElementById('tmdbResults').innerHTML = '';
            
            // Rechercher sur TMDB
            fetch('search.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=search_tmdb&title=${encodeURIComponent(title)}&type=${type}`
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('tmdbLoading').style.display = 'none';
                
                if (data.success) {
                    displayTmdbResults(data.results);
                } else {
                    document.getElementById('tmdbResults').innerHTML = `
                        <div class="alert alert-warning">
                            <i class="ph ph-warning me-2"></i>
                            ${data.message}
                        </div>
                        <div class="card mt-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ph ph-pencil me-2"></i>
                                    Essayer avec un autre nom
                                </h6>
                                <p class="card-text text-muted">
                                    Saisissez un nom personnalisé pour relancer la recherche TMDB
                                </p>
                                <div class="input-group">
                                    <input type="text" 
                                           id="customSearchInput" 
                                           class="form-control" 
                                           placeholder="Tapez un nouveau nom..."
                                           onkeypress="if(event.key==='Enter') searchWithCustomName()">
                                    <button class="btn btn-primary" onclick="searchWithCustomName()">
                                        <i class="ph ph-magnifying-glass me-1"></i>
                                        Rechercher
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Focus sur le champ de saisie
                    setTimeout(() => {
                        const input = document.getElementById('customSearchInput');
                        if (input) input.focus();
                    }, 100);
                }
            })
            .catch(error => {
                document.getElementById('tmdbLoading').style.display = 'none';
                document.getElementById('tmdbResults').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="ph ph-x-circle me-2"></i>
                        Erreur lors de la recherche TMDB
                    </div>
                `;
            });
        }

        function searchWithCustomName() {
            const customInput = document.getElementById('customSearchInput');
            if (!customInput) return;
            
            const customTitle = customInput.value.trim();
            if (!customTitle) {
                showToast('error', 'Veuillez saisir un nom à rechercher');
                return;
            }
            
            // Relancer la recherche avec le nom personnalisé
            const originalType = getCurrentSearchType();
            searchTmdbCommon(customTitle, originalType);
        }
        
        function getCurrentSearchType() {
            return window.currentSearchType || 'movie';
        }

        function displayTmdbResults(results) {
            let html = '<div class="row">';
            
            results.forEach((result, index) => {
                const title = result.title || result.name || 'Titre non disponible';
                const releaseDate = result.release_date || result.first_air_date || '';
                const year = releaseDate ? new Date(releaseDate).getFullYear() : '';
                const overview = result.overview || 'Aucune description disponible';
                const rating = result.vote_average ? result.vote_average.toFixed(1) : 'N/A';
                
                html += `
                    <div class="col-12 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex gap-3">
                                    <div class="flex-shrink-0">
                                        ${result.poster_url ? 
                                            `<img src="${result.poster_url}" alt="${title}" style="width: 80px; height: 120px; object-fit: cover; border-radius: 8px;">` :
                                            `<div style="width: 80px; height: 120px; background: #f8f9fa; border-radius: 8px;" class="d-flex align-items-center justify-content-center">
                                                <i class="ph ph-image text-muted" style="font-size: 1.5rem;"></i>
                                            </div>`
                                        }
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="card-title mb-2">
                                            ${title} ${year ? `(${year})` : ''}
                                        </h6>
                                        <div class="mb-2">
                                            <span class="badge bg-primary me-2">
                                                ${result.media_type === 'tv' ? 'Série TV' : 'Film'}
                                            </span>
                                            <span class="badge bg-warning text-dark">
                                                <i class="ph ph-star-fill me-1"></i>
                                                ${rating}
                                            </span>
                                        </div>
                                        <p class="card-text small text-muted mb-3">
                                            ${overview.length > 150 ? overview.substring(0, 150) + '...' : overview}
                                        </p>
                                        <button 
                                            class="btn btn-success btn-sm"
                                            onclick="confirmSelection(${JSON.stringify(result).replace(/"/g, '&quot;')})"
                                        >
                                            <i class="ph ph-check me-1"></i>
                                            ${isCorrectMode ? 'Corriger avec ce résultat' : 'Choisir ce résultat'}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            document.getElementById('tmdbResults').innerHTML = html;
        }

        function confirmSelection(tmdbData) {
            const button = event.target;
            const originalText = button.innerHTML;
            
            if (isCorrectMode) {
                button.innerHTML = '<i class="ph ph-circle-notch ph-spin me-1"></i>Correction...';
                button.disabled = true;

                fetch('search.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=correct&iptv_id=${currentItemId}&source_db=${currentSourceDb}&tmdb_data=${encodeURIComponent(JSON.stringify(tmdbData))}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('success', data.message);
                        
                        // Fermer la modal
                        bootstrap.Modal.getInstance(document.getElementById('tmdbModal')).hide();
                        
                        // Recharger la page pour voir les modifications
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                        
                    } else {
                        showToast('error', data.message);
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    showToast('error', 'Erreur lors de la correction');
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            } else {
                button.innerHTML = '<i class="ph ph-circle-notch ph-spin me-1"></i>Ajout...';
                button.disabled = true;

                fetch('search.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=add&iptv_id=${currentItemId}&source_db=${currentSourceDb}&tmdb_data=${encodeURIComponent(JSON.stringify(tmdbData))}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('success', data.message);
                        
                        // Fermer la modal
                        bootstrap.Modal.getInstance(document.getElementById('tmdbModal')).hide();
                        
                        // Recharger la page pour voir les modifications
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                        
                    } else {
                        showToast('error', data.message);
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    showToast('error', 'Erreur lors de l\'ajout');
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        }
    </script>
</body>
</html> 