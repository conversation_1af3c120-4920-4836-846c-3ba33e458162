// Import M3U V2 - JavaScript Interface
// Variables globales
let downloadInterval;
let filterInterval;
let parseInterval;

// Gestion du formulaire
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('downloadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        startDownload();
    });
});

function startDownload() {
    const formData = new FormData(document.getElementById('downloadForm'));
    formData.append('action', 'download');
    
    // Afficher la progression
    document.getElementById('progressContainer').style.display = 'block';
    document.querySelector('.loading-spinner').style.display = 'inline-block';
    
    // Réinitialiser les sections
    document.getElementById('downloadProgress').style.width = '0%';
    document.getElementById('downloadStatus').textContent = 'Initialisation...';
    
    const enableFilter = formData.get('enable_filter') === 'on';
    if (enableFilter) {
        document.getElementById('filterSection').style.display = 'block';
        document.getElementById('filterProgress').style.width = '0%';
        document.getElementById('filterStatus').textContent = 'Intégré dans le téléchargement...';
    }
    
    // Démarrer le suivi de progression
    startProgressTracking(enableFilter);
    
    // Lancer le téléchargement
    fetch('import_m3u_v2.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showResults(data);
        } else {
            showError(data.error);
        }
    })
    .catch(error => {
        showError('Erreur réseau: ' + error.message);
    })
    .finally(() => {
        stopProgressTracking();
        document.querySelector('.loading-spinner').style.display = 'none';
    });
}

function startProgressTracking(enableFilter) {
    // Plus besoin de suivi séparé du filtrage puisque c'est maintenant synchrone
    // Le filtrage est intégré dans l'action download
    
    // Afficher les sections appropriées
    if (enableFilter) {
        document.getElementById('filterSection').style.display = 'block';
        document.getElementById('filterProgress').style.width = '0%';
        document.getElementById('filterStatus').textContent = 'Intégré dans le téléchargement...';
    }
    
    // Pas besoin de suivi de progression détaillé car tout se fait de façon synchrone
    // L'utilisateur verra directement les résultats finaux
}

function stopProgressTracking() {
    if (downloadInterval) clearInterval(downloadInterval);
    if (filterInterval) clearInterval(filterInterval);
}

function showResults(data) {
    // Mettre à jour les barres de progression à 100%
    document.getElementById('downloadProgress').style.width = '100%';
    document.getElementById('downloadStatus').textContent = 'Téléchargement terminé';
    
    if (data.filter_stats && data.filter_stats.filtered) {
        document.getElementById('filterProgress').style.width = '100%';
        document.getElementById('filterStatus').textContent = `Terminé: ${data.filter_stats.french_kept?.toLocaleString()} chaînes françaises conservées`;
    }
    
    // Attendre un peu puis masquer la progression et afficher les résultats
    setTimeout(() => {
    // Masquer la progression
    document.getElementById('progressContainer').style.display = 'none';
    
    // Afficher les résultats
    document.getElementById('resultsContainer').style.display = 'block';
    
    // Statistiques
    document.getElementById('totalChannels').textContent = data.channels_count?.toLocaleString() || '0';
    
    if (data.filter_stats && data.filter_stats.filtered) {
        document.getElementById('filterStatsCard').style.display = 'block';
        document.getElementById('filteredChannels').textContent = data.filter_stats.french_kept?.toLocaleString() || '0';
    }
    
    // Afficher les détails de taille
    if (data.filter_stats && data.filter_stats.filtered_size) {
        const sizeMB = (data.filter_stats.filtered_size / 1024 / 1024).toFixed(1);
        document.getElementById('fileSize').textContent = sizeMB + ' MB';
    }
    
    // Afficher les statistiques détaillées et initialiser la recherche
    loadDetailedStats();
    initializeSearch();
    
    // Faire défiler jusqu'aux résultats
    document.getElementById('resultsContainer').scrollIntoView({behavior: 'smooth'});
    }, 1500); // Petite pause pour que l'utilisateur voie la completion
}

function loadDetailedStats() {
    fetch('import_m3u_v2.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'action=get_stats'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayDetailedStats(data.stats);
        } else {
            document.getElementById('detailedStats').innerHTML = 
                '<div class="alert alert-warning">Impossible de charger les statistiques</div>';
        }
    })
    .catch(error => {
        document.getElementById('detailedStats').innerHTML = 
            '<div class="alert alert-danger">Erreur chargement des statistiques: ' + error.message + '</div>';
    });
}

function initializeSearch() {
    // Charger les groupes disponibles
    fetch('import_m3u_v2.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: 'action=get_channels'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.channels) {
            populateGroupFilter(data.channels);
        }
    })
    .catch(error => {
        console.error('Erreur chargement des groupes:', error);
    });
}

function displayDetailedStats(stats) {
    let html = '<div class="alert alert-success mb-3">';
    html += '<i class="bi bi-check-circle"></i> ';
    html += `<strong>Analyse complète :</strong> TOUS les ${stats.total_channels.toLocaleString()} éléments ont été traités sans limitation.`;
    html += '</div>';
    
    html += '<div class="row">';
    
    // Statistiques générales
    html += '<div class="col-md-6">';
    html += '<h6><i class="bi bi-pie-chart"></i> Répartition par type</h6>';
    html += '<div class="row">';
    html += `<div class="col-6"><span class="badge bg-primary">🎬 Films</span><br><strong>${stats.by_type.movie.toLocaleString()}</strong></div>`;
    html += `<div class="col-6"><span class="badge bg-success">📺 Séries</span><br><strong>${stats.by_type.series.toLocaleString()}</strong></div>`;
    html += '</div>';
    html += '<div class="row mt-2">';
    html += `<div class="col-6"><span class="badge bg-secondary">📡 TV</span><br><strong>${stats.by_type.tv.toLocaleString()}</strong></div>`;
    html += `<div class="col-6"><span class="badge bg-warning">❓ Inconnu</span><br><strong>${stats.by_type.unknown.toLocaleString()}</strong></div>`;
    html += '</div>';
    
    // Statistiques séries
    if (stats.series_details.total_series > 0) {
        html += '<hr><h6><i class="bi bi-collection-play"></i> Détails séries</h6>';
        html += `<p><strong>${stats.series_details.total_series.toLocaleString()}</strong> séries différentes<br>`;
        html += `<strong>${stats.series_details.total_seasons.toLocaleString()}</strong> saisons<br>`;
        html += `<strong>${stats.series_details.total_episodes.toLocaleString()}</strong> épisodes</p>`;
    }
    html += '</div>';
    
    // Top groupes
    html += '<div class="col-md-6">';
    html += '<h6><i class="bi bi-tags"></i> Top 5 groupes</h6>';
    let groupCount = 0;
    for (const [group, count] of Object.entries(stats.top_groups)) {
        if (groupCount >= 5) break;
        html += `<div class="d-flex justify-content-between">`;
        html += `<span>${escapeHtml(group)}</span>`;
        html += `<span class="badge bg-info">${count}</span>`;
        html += `</div>`;
        groupCount++;
    }
    
    // Années récentes
    if (Object.keys(stats.recent_years).length > 0) {
        html += '<hr><h6><i class="bi bi-calendar"></i> Années récentes</h6>';
        let yearCount = 0;
        for (const [year, count] of Object.entries(stats.recent_years)) {
            if (yearCount >= 5) break;
            html += `<div class="d-flex justify-content-between">`;
            html += `<span>${year}</span>`;
            html += `<span class="badge bg-secondary">${count}</span>`;
            html += `</div>`;
            yearCount++;
        }
    }
    html += '</div>';
    
    html += '</div>';
    
    // Analyse des URLs
    html += '<hr><div class="row">';
    html += '<div class="col-md-6">';
    html += '<h6><i class="bi bi-file-earmark"></i> Extensions de fichiers</h6>';
    let extCount = 0;
    for (const [ext, count] of Object.entries(stats.url_analysis.extensions)) {
        if (extCount >= 5) break;
        html += `<span class="badge bg-light text-dark me-1">.${ext} (${count})</span>`;
        extCount++;
    }
    html += '</div>';
    
    html += '<div class="col-md-6">';
    html += '<h6><i class="bi bi-globe"></i> Top domaines</h6>';
    let domainCount = 0;
    for (const [domain, count] of Object.entries(stats.url_analysis.domains)) {
        if (domainCount >= 3) break;
        html += `<div class="d-flex justify-content-between">`;
        html += `<small>${escapeHtml(domain)}</small>`;
        html += `<span class="badge bg-primary">${count}</span>`;
        html += `</div>`;
        domainCount++;
    }
    html += '</div>';
    html += '</div>';
    
    document.getElementById('detailedStats').innerHTML = html;
}

function populateGroupFilter(channels) {
    const groups = new Set();
    channels.forEach(channel => {
        if (channel.group_title) {
            groups.add(channel.group_title);
        }
    });
    
    const select = document.getElementById('groupFilter');
    Array.from(groups).sort().forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        select.appendChild(option);
    });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showError(message) {
    document.getElementById('progressContainer').style.display = 'none';
    
    const errorHtml = `
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>Erreur :</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.getElementById('progressContainer').insertAdjacentHTML('afterend', errorHtml);
}

function cleanup() {
    if (confirm('Voulez-vous vraiment nettoyer tous les fichiers temporaires et recommencer ?')) {
        fetch('import_m3u_v2.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: 'action=cleanup'
        })
        .then(() => {
            location.reload();
        });
    }
}

// Défilement automatique vers la progression
function scrollToProgress() {
    setTimeout(() => {
        document.getElementById('progressContainer').scrollIntoView({behavior: 'smooth'});
    }, 500);
}

// Fonctions de recherche
function performSearch() {
    const query = document.getElementById('searchQuery').value;
    const type = document.getElementById('searchType').value;
    const yearFrom = document.getElementById('yearFrom').value;
    const yearTo = document.getElementById('yearTo').value;
    const group = document.getElementById('groupFilter').value;
    const season = document.getElementById('seasonFilter').value;
    const episode = document.getElementById('episodeFilter').value;
    const urlFilter = document.getElementById('urlFilter').value;
    
    const formData = new FormData();
    formData.append('action', 'search');
    formData.append('query', query);
    formData.append('type', type);
    if (yearFrom) formData.append('yearFrom', yearFrom);
    if (yearTo) formData.append('yearTo', yearTo);
    if (group) formData.append('group', group);
    if (season) formData.append('season', season);
    if (episode) formData.append('episode', episode);
    if (urlFilter) formData.append('urlFilter', urlFilter);
    
    document.getElementById('searchResults').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';
    
    fetch('import_m3u_v2.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displaySearchResults(data.results, data.stats);
        } else {
            document.getElementById('searchResults').innerHTML = 
                '<div class="alert alert-danger">Erreur: ' + data.error + '</div>';
        }
    })
    .catch(error => {
        document.getElementById('searchResults').innerHTML = 
            '<div class="alert alert-danger">Erreur de recherche: ' + error.message + '</div>';
    });
}

function displaySearchResults(results, stats) {
    let html = '';
    
    // En-tête avec statistiques
    html += `<div class="alert alert-info">
        <strong>Résultats:</strong> ${stats.matches_found} trouvés sur ${stats.total_searched} éléments
    </div>`;
    
    if (results.length === 0) {
        html += '<div class="alert alert-warning">Aucun résultat trouvé avec ces critères.</div>';
        document.getElementById('searchResults').innerHTML = html;
        return;
    }
    
    // Limite d'affichage raisonnable pour performance (peut être augmentée)
    const maxDisplay = 500;
    const displayResults = results.slice(0, maxDisplay);
    
    html += '<div class="table-responsive">';
    html += '<table class="table table-hover">';
    html += '<thead class="table-dark">';
    html += '<tr><th>Titre</th><th>Type</th><th>Groupe</th><th>URL</th><th>Actions</th></tr>';
    html += '</thead><tbody>';
    
    displayResults.forEach(channel => {
        let typeBadge = '';
        switch(channel.type) {
            case 'movie':
                typeBadge = '<span class="badge bg-primary">🎬 Film</span>';
                if (channel.year) typeBadge += ` <small>(${channel.year})</small>`;
                break;
            case 'series':
                typeBadge = '<span class="badge bg-success">📺 Série</span>';
                if (channel.season && channel.episode) {
                    typeBadge += ` <small>S${channel.season}E${channel.episode}</small>`;
                }
                break;
            default:
                typeBadge = '<span class="badge bg-secondary">📡 TV</span>';
        }
        
        html += '<tr>';
        html += `<td><strong>${escapeHtml(channel.title)}</strong>`;
        if (channel.tvg_name && channel.tvg_name !== channel.title) {
            html += `<br><small class="text-muted">${escapeHtml(channel.tvg_name)}</small>`;
        }
        html += '</td>';
        html += `<td>${typeBadge}</td>`;
        html += `<td><span class="badge bg-info">${escapeHtml(channel.group_title || 'Non défini')}</span></td>`;
        html += `<td><small><a href="${escapeHtml(channel.url)}" target="_blank" class="text-decoration-none">`;
        html += `${escapeHtml(channel.url.substring(0, 50))}${channel.url.length > 50 ? '...' : ''}`;
        html += `</a></small></td>`;
        html += '<td>';
        html += '<button class="btn btn-sm btn-outline-success me-1" title="Importer" disabled><i class="bi bi-plus"></i></button>';
        html += '<button class="btn btn-sm btn-outline-info" title="Détails" onclick="showChannelDetails(' + JSON.stringify(channel).replace(/"/g, '&quot;') + ')"><i class="bi bi-info-circle"></i></button>';
        html += '</td>';
        html += '</tr>';
    });
    
    html += '</tbody></table></div>';
    
    if (results.length > maxDisplay) {
        html += `<div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            Affichage limité aux ${maxDisplay} premiers résultats sur <strong>${results.length} trouvés</strong>.
            <br><small>💡 Utilisez des filtres plus spécifiques pour affiner votre recherche.</small>
        </div>`;
    }
    
    // Statistiques de recherche
    if (stats.by_type) {
        html += '<hr><h6>Répartition des résultats:</h6>';
        html += '<div class="row">';
        Object.entries(stats.by_type).forEach(([type, count]) => {
            if (count > 0) {
                const typeLabels = {
                    'movie': '🎬 Films',
                    'series': '📺 Séries', 
                    'tv': '📡 TV',
                    'unknown': '❓ Inconnu'
                };
                html += `<div class="col-auto"><span class="badge bg-secondary">${typeLabels[type]}: ${count}</span></div>`;
            }
        });
        html += '</div>';
    }
    
    document.getElementById('searchResults').innerHTML = html;
}

function clearSearch() {
    document.getElementById('searchQuery').value = '';
    document.getElementById('searchType').value = 'all';
    document.getElementById('yearFrom').value = '';
    document.getElementById('yearTo').value = '';
    document.getElementById('groupFilter').value = '';
    document.getElementById('seasonFilter').value = '';
    document.getElementById('episodeFilter').value = '';
    document.getElementById('urlFilter').value = '';
    
    document.getElementById('searchResults').innerHTML = 
        '<div class="alert alert-info"><i class="bi bi-info-circle"></i> Utilisez les filtres ci-dessus pour rechercher dans le contenu M3U.</div>';
}

function showAdvancedFilters() {
    const element = document.getElementById('advancedFilters');
    element.style.display = element.style.display === 'none' ? 'block' : 'none';
}

function showChannelDetails(channel) {
    let html = `
        <div class="modal fade" id="channelModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Détails: ${escapeHtml(channel.title)}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <table class="table table-sm">
                            <tr><td><strong>Titre:</strong></td><td>${escapeHtml(channel.title)}</td></tr>
                            <tr><td><strong>TVG Name:</strong></td><td>${escapeHtml(channel.tvg_name || 'Non défini')}</td></tr>
                            <tr><td><strong>Type:</strong></td><td>${channel.type}</td></tr>
                            <tr><td><strong>Groupe:</strong></td><td>${escapeHtml(channel.group_title || 'Non défini')}</td></tr>
                            ${channel.year ? `<tr><td><strong>Année:</strong></td><td>${channel.year}</td></tr>` : ''}
                            ${channel.season ? `<tr><td><strong>Saison:</strong></td><td>${channel.season}</td></tr>` : ''}
                            ${channel.episode ? `<tr><td><strong>Épisode:</strong></td><td>${channel.episode}</td></tr>` : ''}
                            <tr><td><strong>URL:</strong></td><td><small>${escapeHtml(channel.url)}</small></td></tr>
                        </table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                        <button type="button" class="btn btn-primary" disabled>Importer</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('channelModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    document.body.insertAdjacentHTML('beforeend', html);
    const modal = new bootstrap.Modal(document.getElementById('channelModal'));
    modal.show();
}

// Recherche en temps réel (optionnel)
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchQuery');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
}); 