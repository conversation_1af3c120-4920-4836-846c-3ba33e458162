<?php
/**
 * Script pour tester et vider le cache API tvshow-details
 */

require_once __DIR__ . '/../../vendor/autoload.php';
$app = require_once __DIR__ . '/../../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Modules\Entertainment\Services\MultiIptvService;
use Modules\Entertainment\Models\Entertainment;
use Illuminate\Support\Facades\Cache;

header('Content-Type: application/json; charset=utf-8');

$action = $_GET['action'] ?? 'test';
$tmdbId = $_GET['tmdb_id'] ?? null;
$entertainmentId = $_GET['entertainment_id'] ?? null;
$profileId = $_GET['profile_id'] ?? 1;

if (!$tmdbId && !$entertainmentId) {
    echo json_encode([
        'error' => 'Paramètre tmdb_id OU entertainment_id requis',
        'usage' => [
            'test' => 'test_api_cache.php?tmdb_id=123456&action=test&profile_id=1',
            'clear' => 'test_api_cache.php?tmdb_id=123456&action=clear',
            'clear_direct' => 'test_api_cache.php?entertainment_id=789&action=clear'
        ]
    ], JSON_PRETTY_PRINT);
    exit;
}

try {
    $result = [
        'action' => $action,
        'timestamp' => now()->toDateTimeString()
    ];

    // Récupérer l'Entertainment si on a seulement le TMDB ID
    if ($tmdbId && !$entertainmentId) {
        $entertainment = Entertainment::where('tmdb_id', $tmdbId)
            ->where('type', 'tvshow')
            ->first(['id', 'name', 'tmdb_id']);
            
        if ($entertainment) {
            $entertainmentId = $entertainment->id;
            $result['entertainment'] = [
                'id' => $entertainment->id,
                'name' => $entertainment->name,
                'tmdb_id' => $entertainment->tmdb_id
            ];
        } else {
            echo json_encode([
                'error' => "Aucun Entertainment trouvé pour TMDB ID: {$tmdbId}"
            ], JSON_PRETTY_PRINT);
            exit;
        }
    } elseif ($entertainmentId) {
        $entertainment = Entertainment::find($entertainmentId, ['id', 'name', 'tmdb_id']);
        if ($entertainment) {
            $result['entertainment'] = [
                'id' => $entertainment->id,
                'name' => $entertainment->name,
                'tmdb_id' => $entertainment->tmdb_id
            ];
        }
    }

    switch ($action) {
        case 'test':
            // Tester les différentes clés de cache
            $result['cache_test'] = [];
            $result['profile_id'] = $profileId;
            
            $cacheKeys = [
                "tvshow_{$entertainmentId}_{$profileId}",
                "tvshow_{$entertainmentId}_{$profileId}_iptv",
                "tvshow_embed_seasons_{$entertainmentId}"
            ];
            
            foreach ($cacheKeys as $key) {
                $cacheData = Cache::get($key);
                $result['cache_test'][$key] = [
                    'exists' => $cacheData !== null,
                    'type' => $cacheData ? gettype($cacheData) : null,
                    'size' => $cacheData ? (is_string($cacheData) ? strlen($cacheData) : 'object/array') : null
                ];
                
                // Si c'est les données de l'API et qu'elles existent
                if ($cacheData && str_contains($key, 'tvshow_') && !str_contains($key, 'embed')) {
                    // Extraire les informations sur iptv_seasons
                    if (is_object($cacheData) && isset($cacheData->resource) && isset($cacheData->resource['iptv_seasons'])) {
                        $iptvSeasons = $cacheData->resource['iptv_seasons'];
                        $result['cache_test'][$key]['iptv_seasons_info'] = [
                            'count' => count($iptvSeasons),
                            'seasons' => array_column($iptvSeasons, 'season_number'),
                            'season_3_source' => null
                        ];
                        
                        // Vérifier la source de la saison 3
                        foreach ($iptvSeasons as $season) {
                            if ($season['season_number'] == 3) {
                                $result['cache_test'][$key]['iptv_seasons_info']['season_3_source'] = $season['iptv_source'];
                                break;
                            }
                        }
                    }
                }
            }
            break;
            
        case 'clear':
            // Vider le cache API
            $service = new MultiIptvService();
            
            if ($tmdbId) {
                // Vider par TMDB ID (méthode complète)
                $service->clearCache($tmdbId, 'series');
                $result['method'] = 'clearCache via TMDB ID';
            } else {
                // Vider par Entertainment ID (méthode directe)
                $cleared = $service->clearApiCacheByEntertainmentId($entertainmentId);
                $result['method'] = 'clearApiCacheByEntertainmentId';
                $result['cleared_count'] = $cleared;
            }
            
            $result['action_performed'] = 'Cache API vidé';
            $result['recommendation'] = 'Testez maintenant votre endpoint api/tvshow-details';
            break;
            
        case 'compare':
            // Comparer les données du service vs API cache
            $service = new MultiIptvService();
            
            // Données fraîches du service
            $freshData = $service->getTvShowDataNoCache($tmdbId);
            
            // Données du cache API
            $apiCacheKey = "tvshow_{$entertainmentId}_{$profileId}_iptv";
            $apiCacheData = Cache::get($apiCacheKey);
            
            $result['comparison'] = [
                'fresh_service_data' => [
                    'exists' => $freshData !== null,
                    'seasons_count' => $freshData ? count($freshData) : 0,
                    'seasons' => $freshData ? array_column($freshData, 'season_number') : []
                ],
                'api_cache_data' => [
                    'exists' => $apiCacheData !== null,
                    'cache_key' => $apiCacheKey
                ]
            ];
            
            if ($apiCacheData && isset($apiCacheData->resource['iptv_seasons'])) {
                $result['comparison']['api_cache_data']['seasons_count'] = count($apiCacheData->resource['iptv_seasons']);
                $result['comparison']['api_cache_data']['seasons'] = array_column($apiCacheData->resource['iptv_seasons'], 'season_number');
                
                // Vérifier la source de la saison 3 dans le cache API
                foreach ($apiCacheData->resource['iptv_seasons'] as $season) {
                    if ($season['season_number'] == 3) {
                        $result['comparison']['api_cache_data']['season_3_source'] = $season['iptv_source'];
                        break;
                    }
                }
            }
            
            // Vérifier la source de la saison 3 dans les données fraîches
            if ($freshData) {
                foreach ($freshData as $season) {
                    if ($season['season_number'] == 3) {
                        $result['comparison']['fresh_service_data']['season_3_source'] = $season['iptv_source'];
                        break;
                    }
                }
            }
            
            // Analyse
            $freshSeason3 = $result['comparison']['fresh_service_data']['season_3_source'] ?? 'absent';
            $cacheSeason3 = $result['comparison']['api_cache_data']['season_3_source'] ?? 'absent';
            
            if ($freshSeason3 !== $cacheSeason3) {
                $result['analysis'] = [
                    'status' => 'DIFFÉRENCE DÉTECTÉE',
                    'issue' => "Saison 3 - Service: {$freshSeason3} vs API Cache: {$cacheSeason3}",
                    'recommendation' => 'Utilisez action=clear pour vider le cache API'
                ];
            } else {
                $result['analysis'] = [
                    'status' => 'COHÉRENT',
                    'message' => "Saison 3 source identique: {$freshSeason3}"
                ];
            }
            break;
            
        default:
            $result['error'] = 'Action non reconnue. Utilisez: test, clear, compare';
    }

} catch (Exception $e) {
    $result = [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ];
}

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?> 