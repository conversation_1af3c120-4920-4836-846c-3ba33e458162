<?php
// Démarrer la session uniquement si pas encore active
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Fonction pour retourner une erreur JSON et arrêter l'exécution
function jsonError($message, $details = null) {
    if (ob_get_level()) {
        ob_clean();
    }
    header('Content-Type: application/json');
    $response = ['success' => false, 'error' => $message];
    if ($details) {
        $response['details'] = $details;
    }
    echo json_encode($response);
    exit;
}

// Fonction pour logger les erreurs
function logError($message) {
    $logFile = __DIR__ . '/logs/error.log';
    if (!is_dir(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[{$timestamp}] {$message}" . PHP_EOL, FILE_APPEND | LOCK_EX);
}

// Gestion d'erreur globale pour les requêtes AJAX
if (isset($_POST['action'])) {
    // Configuration des erreurs pour les requêtes AJAX
    error_reporting(E_ALL);
    ini_set('display_errors', 0); // Ne pas afficher les erreurs en HTML
    
    // Gestionnaire d'erreur personnalisé
    set_error_handler(function($severity, $message, $file, $line) {
        logError("PHP Error: {$message} in {$file}:{$line}");
        jsonError("Erreur PHP: {$message}");
    });
    
    // Gestionnaire d'exception personnalisé
    set_exception_handler(function($exception) {
        logError("PHP Exception: " . $exception->getMessage() . " in " . $exception->getFile() . ":" . $exception->getLine());
        jsonError("Erreur de configuration: " . $exception->getMessage());
    });
} else {
    // Configuration normale pour l'affichage HTML
error_reporting(E_ALL);
ini_set('display_errors', 1);
}

// Inclure la configuration avec gestion d'erreur
try {
require_once 'config.php';
} catch (Exception $e) {
    logError("Erreur configuration: " . $e->getMessage());
    if (isset($_POST['action'])) {
        jsonError("Erreur de configuration: " . $e->getMessage());
    } else {
        // Pour l'affichage HTML, on continue avec une configuration par défaut
        $GLOBALS['config_error'] = $e->getMessage();
    }
}

class M3UImportV2 {
    private $allowed_urls = [];
    private $temp_dir;
    private $log_file;
    private $config;
    
    public function __construct() {
        // Charger la configuration (une seule fois)
        static $config_cache = null;
        if ($config_cache === null) {
            try {
            $config_cache = require 'config.php';
            } catch (Exception $e) {
                // Configuration par défaut en cas d'erreur
                $config_cache = [
                    'update_iptv1_url' => '',
                    'update_iptv2_url' => ''
                ];
                logError("Erreur chargement config dans constructeur: " . $e->getMessage());
            }
        }
        $this->config = $config_cache;
        
        // Définir les URLs autorisées depuis la configuration
        $this->allowed_urls = [
            'iptv1' => $this->config['update_iptv1_url'] ?? '',
            'iptv2' => $this->config['update_iptv2_url'] ?? '',
            'test' => __DIR__ . '/test_playlist.m3u'
        ];
        
        $this->temp_dir = __DIR__ . '/temp/';
        $this->log_file = __DIR__ . '/logs/m3u_import.log';
        
        // Créer les répertoires nécessaires
        if (!is_dir($this->temp_dir)) {
            mkdir($this->temp_dir, 0755, true);
        }
        if (!is_dir(dirname($this->log_file))) {
            mkdir(dirname($this->log_file), 0755, true);
        }
    }
    
    public function getAllowedUrls() {
        return $this->allowed_urls;
    }
    
    public function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        file_put_contents($this->log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    public function downloadM3U($url, $filename = null) {
        // Validation de l'URL
        if (empty($url)) {
            throw new Exception("URL vide fournie");
        }
        
        // Nettoyer l'URL des espaces et caractères indésirables
        $url = trim($url);
        
        // Validation basique de l'URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new Exception("URL mal formée: '$url'");
        }
        
        // Vérifier que l'URL commence par http/https
        if (!preg_match('/^https?:\/\//', $url)) {
            throw new Exception("URL doit commencer par http:// ou https://: '$url'");
        }
        
        if (empty($filename)) {
            $filename = 'playlist_' . date('YmdHis') . '.m3u';
        }
        
        $output_path = $this->temp_dir . $filename;
        
        // Si c'est un fichier local (pour les tests)
        if (file_exists($url)) {
            $this->log("Copie du fichier local: {$url}");
            if (copy($url, $output_path)) {
                $file_size = filesize($output_path);
                $this->log("Copie terminée: {$file_size} octets");
                return [
                    'file' => $output_path,
                    'size' => $file_size,
                    'lines' => substr_count(file_get_contents($output_path), "\n"),
                    'url' => $url
                ];
            } else {
                $this->log("Erreur copie du fichier local", 'ERROR');
                return false;
            }
        }
        
        $this->log("Début téléchargement M3U depuis: {$url}");
        
        $userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300); // 5 minutes timeout
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 30 secondes pour la connexion
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // Limiter les redirections
        
        $content = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $curlInfo = curl_getinfo($ch);
        curl_close($ch);
        
        // Logs détaillés pour debug
        $this->log("Résultat cURL: HTTP $httpCode, Taille: " . strlen($content) . " octets");
        
        if ($error) {
            $this->log("Erreur cURL détaillée: $error", 'ERROR');
            $this->log("URL problématique: $url", 'ERROR');
            throw new Exception("Erreur cURL: $error");
        }
        
        if ($httpCode !== 200) {
            $this->log("Code HTTP non-200: $httpCode pour URL: $url", 'ERROR');
            throw new Exception("Erreur HTTP: Code $httpCode - " . ($curlInfo['url'] ?? 'URL inconnue'));
        }
        
        if (empty($content)) {
            throw new Exception("Contenu vide reçu du serveur");
        }
        
        // Vérifier que le contenu ressemble à un M3U
        if (!preg_match('/^#EXTM3U/i', $content) && !preg_match('/#EXTINF/i', $content)) {
            $this->log("Contenu ne ressemble pas à un M3U. Début: " . substr($content, 0, 200), 'WARN');
            // Ne pas lever d'exception, juste un warning
        }
        
        if (file_put_contents($output_path, $content) === false) {
            throw new Exception("Impossible d'écrire le fichier temporaire: $output_path");
        }
        
        $this->log("Téléchargement M3U réussi: " . strlen($content) . " octets sauvés dans $output_path");
        
        return [
            'file' => $output_path,
            'size' => strlen($content),
            'lines' => substr_count($content, "\n"),
            'url' => $url,
            'http_code' => $httpCode
        ];
    }
    
    private function updateDownloadProgress($downloaded, $total, $percent) {
        $progress_file = $this->temp_dir . 'download_progress.json';
        $data = [
            'status' => 'downloading',
            'downloaded' => $downloaded,
            'total' => $total,
            'percent' => $percent,
            'timestamp' => date('c')
        ];
        file_put_contents($progress_file, json_encode($data));
    }
    
    // Fonction pour détecter si une ligne EXTINF correspond à une chaîne française
    public function isFrenchChannel($line) {
        if (strpos($line, '#EXTINF') !== 0) {
            return false;
        }
        
        // Patterns pour détecter les chaînes françaises (basés sur le script Python)
        $frenchPatterns = [
            // Format avec tvg-name (format original)
            '/tvg-name="FR[^"]*"/i',
            
            // Format avec préfixe direct après la virgule
            '/,\s*FR\s*[-:|]\s*/i',  // ,FR - ou ,FR: ou ,FR|
            
            // Format avec crochets ou parenthèses
            '/,\s*\[FR\]\s*/i',      // [FR]
            '/,\s*\{FR\}\s*/i',      // {FR}
            
            // Format dans le titre (insensible à la casse)
            '/,.*?FR\s*[-:|]\s*/i',   // Quelque part dans le titre avec FR -
            
            // Format avec pipe
            '/FR\|\s*/i',            // FR|
        ];
        
        // Vérifier chaque pattern
        foreach ($frenchPatterns as $pattern) {
            if (preg_match($pattern, $line)) {
                return true;
            }
        }
        
        return false;
        }
        
    // Fonction pour filtrer les chaînes françaises d'un fichier M3U (optimisée pour gros fichiers)
    public function filterFrenchChannels($inputFile, $outputFile = null) {
        if (!$outputFile) {
            $outputFile = $this->temp_dir . 'filtered_playlist_' . uniqid() . '.m3u';
        }
        
        // Extensions vidéo courantes
        $videoExtensions = ['.mkv', '.mp4', '.m3u8', '.avi', '.mov', '.wmv', '.flv', '.webm'];
        
        // Ouvrir les fichiers pour lecture/écriture par blocs
        $inputHandle = fopen($inputFile, 'r');
        if ($inputHandle === false) {
            throw new Exception("Impossible d'ouvrir le fichier M3U en lecture");
        }
        
        $outputHandle = fopen($outputFile, 'w');
        if ($outputHandle === false) {
            fclose($inputHandle);
            throw new Exception("Impossible d'ouvrir le fichier de sortie en écriture");
        }
        
        $keepLine = false;
        $currentExtinf = null;
        $filteredCount = 0;
        $totalCount = 0;
        $originalSize = 0;
        $filteredSize = 0;
        $lineNumber = 0;
        
        while (($line = fgets($inputHandle)) !== false) {
            $lineNumber++;
            $originalSize += strlen($line);
            $line = trim($line);
        
            // Première ligne #EXTM3U
            if ($line === "#EXTM3U") {
                fwrite($outputHandle, $line . "\n");
                $filteredSize += strlen($line) + 1;
                continue;
            }
            
            // Ligne vide
            if (empty($line)) {
                continue;
            }
            
            // Utiliser la fonction de détection française
            if ($this->isFrenchChannel($line)) {
                $currentExtinf = $line;
                $keepLine = true;
                $totalCount++;
        }
            // Si c'est une URL et qu'on doit garder la ligne précédente
            elseif (strpos($line, 'http') === 0 && $keepLine) {
                // Vérifier si l'URL se termine par une extension vidéo
                $hasVideoExtension = false;
                foreach ($videoExtensions as $ext) {
                    if (stripos($line, $ext) !== false) {
                        $hasVideoExtension = true;
                        break;
                    }
                }
                
                if ($hasVideoExtension) {
                    fwrite($outputHandle, $currentExtinf . "\n");
                    fwrite($outputHandle, $line . "\n");
                    $filteredSize += strlen($currentExtinf) + strlen($line) + 2;
                    $filteredCount++;
        }
        
                $keepLine = false;
                $currentExtinf = null;
            }
            // Si c'est une autre ligne (commentaire, métadonnées, etc.)
            elseif (strpos($line, 'http') !== 0) {
                $keepLine = false;
                $currentExtinf = null;
            }
            
            // Log de progression tous les 10000 lignes
            if ($lineNumber % 10000 === 0) {
                $this->log("Filtrage en cours: ligne $lineNumber, français détectés: $totalCount, conservés: $filteredCount");
            }
        }
        
        fclose($inputHandle);
        fclose($outputHandle);
        
        $this->log("Filtrage terminé: $lineNumber lignes traitées, $filteredCount chaînes françaises conservées");
        
        return [
            'success' => true,
            'filtered_file' => $outputFile,
            'stats' => [
                'total_french_detected' => $totalCount,
                'french_kept' => $filteredCount,
                'filtered_size' => $filteredSize,
                'original_size' => $originalSize,
                'lines_processed' => $lineNumber,
                'filtered' => true
            ]
        ];
    }
    
    public function parseM3UContent($file_path, $progress_callback = null) {
        $this->log("Début parsing du fichier: {$file_path}");
        
        if (!file_exists($file_path)) {
            return ['error' => 'Fichier non trouvé'];
        }
        
        // Obtenir la taille du fichier pour le calcul de progression
        $file_size = filesize($file_path);
        $this->log("Taille du fichier: " . number_format($file_size) . " octets");
        
        $channels = [];
        $handle = fopen($file_path, 'r');
        if (!$handle) {
            return ['error' => 'Impossible d\'ouvrir le fichier'];
        }
        
        $line_count = 0;
        $current_extinf = null;
        $bytes_read = 0;
        $last_progress_update = 0;
        
        while (($line = fgets($handle)) !== false) {
            $line = trim($line);
            $line_count++;
            $bytes_read += strlen($line) + 1; // +1 pour le saut de ligne
            
            if (strpos($line, '#EXTINF') === 0) {
                $current_extinf = $line;
            } elseif (strpos($line, 'http') === 0 && $current_extinf) {
                // Parser les métadonnées
                $channel_data = $this->parseExtinf($current_extinf);
                $channel_data['url'] = $line;
                $channel_data['line_number'] = $line_count;
                
                $channels[] = $channel_data;
                $current_extinf = null;
                
                // Mise à jour de la progression plus fréquente pour analyser tous les contenus
                if (count($channels) % 500 == 0) {
                    $progress_percent = ($bytes_read / $file_size) * 100;
                    $this->updateParseProgress(count($channels), $progress_percent);
                    $this->log("Parsing en cours: " . count($channels) . " chaînes trouvées (" . round($progress_percent, 1) . "%)");
                }
            }
        }
        
        fclose($handle);
        
        // Mise à jour finale
        $this->updateParseProgress(count($channels), 100);
        $this->log("Parsing terminé: " . count($channels) . " chaînes trouvées sur " . $line_count . " lignes");
        
        return [
            'channels' => $channels, 
            'total_parsed' => count($channels),
            'total_lines' => $line_count,
            'file_size' => $file_size
        ];
    }
    
    private function updateParseProgress($channels_found, $progress_percent) {
        $progress_file = $this->temp_dir . 'parse_progress.json';
        $data = [
            'status' => 'parsing',
            'channels_found' => $channels_found,
            'progress_percent' => round($progress_percent, 1),
            'timestamp' => date('c')
        ];
        file_put_contents($progress_file, json_encode($data));
    }
    
    private function parseExtinf($extinf_line) {
        $data = [
            'title' => '',
            'tvg_name' => '',
            'tvg_logo' => '',
            'group_title' => '',
            'duration' => -1,
            'is_series' => false,
            'season' => null,
            'episode' => null,
            'year' => null,
            'type' => 'unknown'
        ];
        
        // Extraire la durée
        if (preg_match('/#EXTINF:([^,]+),/', $extinf_line, $matches)) {
            $data['duration'] = floatval($matches[1]);
        }
        
        // Extraire le titre (tout après la dernière virgule)
        if (preg_match('/,([^,]+)$/', $extinf_line, $matches)) {
            $data['title'] = trim($matches[1]);
        }
        
        // Extraire tvg-name
        if (preg_match('/tvg-name="([^"]*)"/', $extinf_line, $matches)) {
            $data['tvg_name'] = $matches[1];
        }
        
        // Extraire tvg-logo
        if (preg_match('/tvg-logo="([^"]*)"/', $extinf_line, $matches)) {
            $data['tvg_logo'] = $matches[1];
        }
        
        // Extraire group-title
        if (preg_match('/group-title="([^"]*)"/', $extinf_line, $matches)) {
            $data['group_title'] = $matches[1];
        }
        
        // Extraire l'année d'abord
        if (preg_match('/\((\d{4})\)/', $data['title'], $matches)) {
            $data['year'] = intval($matches[1]);
        }
        
        // Détecter les séries avec les patterns SXX EXX (inspiré du script Python)
        $series_patterns = [
            '/\sS(\d+)\s*E(\d+)/i',                    // S01 E01, S1 E1
            '/\sS(\d+)E(\d+)/i',                       // S01E01
            '/\s(\d+)x(\d+)/i',                        // 1x01, 01x01
            '/Season\s+(\d+).*?Episode\s+(\d+)/i',     // Season 1 Episode 1
            '/Saison\s+(\d+).*?Episode\s+(\d+)/i',     // Saison 1 Episode 1
        ];
        
        $is_series_detected = false;
        
        // Tester tous les patterns de série dans le titre ET tvg_name
        $search_text = $data['title'] . ' ' . $data['tvg_name'];
        
        foreach ($series_patterns as $pattern) {
            if (preg_match($pattern, $search_text, $matches)) {
                $data['is_series'] = true;
                $data['season'] = intval($matches[1]);
                $data['episode'] = intval($matches[2]);
                $data['type'] = 'series';
                $is_series_detected = true;
                break;
            }
        }
        
        // Si aucun pattern de série détecté, c'est un film par défaut
        // (logique simplifiée inspirée du script Python)
        if (!$is_series_detected) {
                $data['type'] = 'movie';
            $data['is_series'] = false;
        }
        
        return $data;
    }
    
    public function getProgress($type = 'filter') {
        // Simplifié : plus besoin de suivi de progression pour le filtrage PHP synchrone
        $progress_file = $this->temp_dir . $type . '_progress.json';
        if (file_exists($progress_file)) {
            return json_decode(file_get_contents($progress_file), true);
        }
        
        // Retourner un état par défaut si pas de fichier
        return [
            'status' => 'completed',
            'progress_percent' => 100,
            'timestamp' => date('c')
        ];
    }
    
    public function cleanupTempFiles() {
        $files = glob($this->temp_dir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        $this->log("Fichiers temporaires nettoyés");
    }
    
    public function searchInChannels($channels, $query = '', $type = 'all', $yearFrom = null, $yearTo = null, $group = '', $season = null, $episode = null, $urlFilter = '') {
        $results = [];
        $stats = [
            'total_searched' => count($channels),
            'matches_found' => 0,
            'by_type' => ['movie' => 0, 'series' => 0, 'tv' => 0, 'unknown' => 0],
            'by_year' => [],
            'by_group' => []
        ];
        
        foreach ($channels as $channel) {
            $match = true;
            
            // Filtrage par type
            if ($type !== 'all' && $channel['type'] !== $type) {
                $match = false;
            }
            
            // Filtrage par texte (titre, tvg_name, group_title)
            if (!empty($query) && $match) {
                $searchText = strtolower($channel['title'] . ' ' . $channel['tvg_name'] . ' ' . $channel['group_title']);
                $queryLower = strtolower($query);
                if (strpos($searchText, $queryLower) === false) {
                    $match = false;
                }
            }
            
            // Filtrage par année
            if ($match && ($yearFrom !== null || $yearTo !== null)) {
                $channelYear = $channel['year'];
                if ($channelYear === null) {
                    $match = false;
                } else {
                    if ($yearFrom !== null && $channelYear < $yearFrom) $match = false;
                    if ($yearTo !== null && $channelYear > $yearTo) $match = false;
                }
            }
            
            // Filtrage par groupe
            if ($match && !empty($group)) {
                if (stripos($channel['group_title'], $group) === false) {
                    $match = false;
                }
            }
            
            // Filtrage par saison
            if ($match && $season !== null) {
                if ($channel['season'] !== $season) {
                    $match = false;
                }
            }
            
            // Filtrage par épisode
            if ($match && $episode !== null) {
                if ($channel['episode'] !== $episode) {
                    $match = false;
                }
            }
            
            // Filtrage par URL
            if ($match && !empty($urlFilter)) {
                if (stripos($channel['url'], $urlFilter) === false) {
                    $match = false;
                }
            }
            
            if ($match) {
                $results[] = $channel;
                $stats['matches_found']++;
                
                // Statistiques par type
                $stats['by_type'][$channel['type']]++;
                
                // Statistiques par année
                if ($channel['year']) {
                    if (!isset($stats['by_year'][$channel['year']])) {
                        $stats['by_year'][$channel['year']] = 0;
                    }
                    $stats['by_year'][$channel['year']]++;
                }
                
                // Statistiques par groupe
                $groupName = $channel['group_title'] ?: 'Non défini';
                if (!isset($stats['by_group'][$groupName])) {
                    $stats['by_group'][$groupName] = 0;
                }
                $stats['by_group'][$groupName]++;
            }
        }
        
        // Trier les résultats par titre
        usort($results, function($a, $b) {
            return strcmp($a['title'], $b['title']);
        });
        
        // Trier les statistiques
        ksort($stats['by_year']);
        arsort($stats['by_group']);
        
        return [
            'results' => $results,
            'total' => count($results),
            'stats' => $stats
        ];
    }
    
    public function generateDetailedStats($channels) {
        $stats = [
            'total_channels' => count($channels),
            'by_type' => ['movie' => 0, 'series' => 0, 'tv' => 0, 'unknown' => 0],
            'by_year' => [],
            'by_group' => [],
            'series_details' => [
                'total_series' => 0,
                'total_seasons' => 0,
                'total_episodes' => 0,
                'by_season' => []
            ],
            'top_groups' => [],
            'recent_years' => [],
            'url_analysis' => [
                'extensions' => [],
                'domains' => []
            ]
        ];
        
        foreach ($channels as $channel) {
            // Comptage par type
            $stats['by_type'][$channel['type']]++;
            
            // Analyse des années
            if ($channel['year']) {
                if (!isset($stats['by_year'][$channel['year']])) {
                    $stats['by_year'][$channel['year']] = 0;
                }
                $stats['by_year'][$channel['year']]++;
            }
            
            // Analyse des groupes
            $groupName = $channel['group_title'] ?: 'Non défini';
            if (!isset($stats['by_group'][$groupName])) {
                $stats['by_group'][$groupName] = 0;
            }
            $stats['by_group'][$groupName]++;
            
            // Analyse des séries
            if ($channel['type'] === 'series') {
                $stats['series_details']['total_series']++;
                if ($channel['season']) {
                    if (!isset($stats['series_details']['by_season'][$channel['season']])) {
                        $stats['series_details']['by_season'][$channel['season']] = 0;
                    }
                    $stats['series_details']['by_season'][$channel['season']]++;
                    $stats['series_details']['total_episodes']++;
                }
            }
            
            // Analyse des URLs
            $url = $channel['url'];
            $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
            if ($extension) {
                if (!isset($stats['url_analysis']['extensions'][$extension])) {
                    $stats['url_analysis']['extensions'][$extension] = 0;
                }
                $stats['url_analysis']['extensions'][$extension]++;
            }
            
            $domain = parse_url($url, PHP_URL_HOST);
            if ($domain) {
                if (!isset($stats['url_analysis']['domains'][$domain])) {
                    $stats['url_analysis']['domains'][$domain] = 0;
                }
                $stats['url_analysis']['domains'][$domain]++;
            }
        }
        
        // Calculer les saisons uniques
        $stats['series_details']['total_seasons'] = count($stats['series_details']['by_season']);
        
        // Trier et limiter
        arsort($stats['by_group']);
        $stats['top_groups'] = array_slice($stats['by_group'], 0, 10, true);
        
        krsort($stats['by_year']);
        $stats['recent_years'] = array_slice($stats['by_year'], 0, 10, true);
        
        arsort($stats['url_analysis']['extensions']);
        arsort($stats['url_analysis']['domains']);
        
        return $stats;
    }
}

// Traiter les actions AJAX avec gestion d'erreur robuste
if (isset($_POST['action'])) {
    try {
    // Nettoyer le buffer de sortie pour éviter les erreurs de header
    if (ob_get_level()) {
        ob_clean();
    }
    header('Content-Type: application/json');
        
        // Initialiser l'importeur
        $importer = new M3UImportV2();
    
    switch ($_POST['action']) {
        case 'download':
            $source = $_POST['source'] ?? '';
            $filter_enabled = isset($_POST['enable_filter']) && $_POST['enable_filter'] === 'true';
            
            if (empty($source) || !isset($importer->getAllowedUrls()[$source])) {
                echo json_encode(['success' => false, 'error' => 'Source invalide']);
                exit;
            }
            
            $url = $importer->getAllowedUrls()[$source];
            if (empty($url)) {
                echo json_encode(['success' => false, 'error' => 'URL non configurée']);
                exit;
            }
            
            // Télécharger avec la nouvelle logique
            $download_result = $importer->downloadM3U($url);
            if (!$download_result) {
                echo json_encode(['success' => false, 'error' => 'Échec téléchargement']);
                exit;
            }
            
            // Filtrer si demandé avec PHP au lieu de Python
            $final_file = $download_result['file'];
            $filter_stats = ['filtered' => false];
            
            if ($filter_enabled) {
                $filter_result = $importer->filterFrenchChannels($download_result['file']);
                
                if ($filter_result['success']) {
                    $final_file = $filter_result['filtered_file'];
                    $filter_stats = $filter_result['stats'];
                    
                    // Nettoyer le fichier original après filtrage
                    unlink($download_result['file']);
                } else {
                    echo json_encode(['success' => false, 'error' => 'Échec filtrage français']);
                    exit;
                }
            }
            
            // Parser le contenu
            $parse_result = $importer->parseM3UContent($final_file);
            if (isset($parse_result['error'])) {
                echo json_encode(['success' => false, 'error' => $parse_result['error']]);
                exit;
            }
            
            // Nettoyer le fichier final après parsing
            if (file_exists($final_file)) {
                unlink($final_file);
            }
            
            // Stocker en session
            $_SESSION['m3u_data'] = [
                'source' => $source,
                'channels' => $parse_result['channels'],
                'total_channels' => $parse_result['total_parsed'],
                'filter_stats' => $filter_stats,
                'download_stats' => [
                    'original_size' => $download_result['size'],
                    'original_lines' => $download_result['lines']
                ],
                'timestamp' => time()
            ];
            
            echo json_encode([
                'success' => true,
                'channels_count' => count($parse_result['channels']),
                'filter_stats' => $filter_stats
            ]);
            exit;
            
        case 'get_progress':
            $type = $_POST['type'] ?? 'filter';
            $progress = $importer->getProgress($type);
            echo json_encode($progress);
            exit;
            
        case 'get_channels':
            if (isset($_SESSION['m3u_data'])) {
                echo json_encode([
                    'success' => true,
                    'channels' => $_SESSION['m3u_data']['channels'],
                    'total' => $_SESSION['m3u_data']['total_channels']
                ]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Pas de données en session']);
            }
            exit;
            
        case 'search':
            if (!isset($_SESSION['m3u_data'])) {
                echo json_encode(['success' => false, 'error' => 'Pas de données en session']);
                exit;
            }
            
            $query = $_POST['query'] ?? '';
            $type = $_POST['type'] ?? 'all';
            $yearFrom = !empty($_POST['yearFrom']) ? intval($_POST['yearFrom']) : null;
            $yearTo = !empty($_POST['yearTo']) ? intval($_POST['yearTo']) : null;
            $group = $_POST['group'] ?? '';
            $season = !empty($_POST['season']) ? intval($_POST['season']) : null;
            $episode = !empty($_POST['episode']) ? intval($_POST['episode']) : null;
            $urlFilter = $_POST['urlFilter'] ?? '';
            
            $results = $importer->searchInChannels(
                $_SESSION['m3u_data']['channels'],
                $query, $type, $yearFrom, $yearTo, $group, $season, $episode, $urlFilter
            );
            
            echo json_encode([
                'success' => true,
                'results' => $results['results'],
                'total' => $results['total'],
                'stats' => $results['stats']
            ]);
            exit;
            
        case 'get_stats':
            if (!isset($_SESSION['m3u_data'])) {
                echo json_encode(['success' => false, 'error' => 'Pas de données en session']);
                exit;
            }
            
            $stats = $importer->generateDetailedStats($_SESSION['m3u_data']['channels']);
            echo json_encode(['success' => true, 'stats' => $stats]);
            exit;
            
        case 'cleanup':
            $importer->cleanupTempFiles();
            unset($_SESSION['m3u_data']);
            echo json_encode(['success' => true]);
            exit;
            
        default:
            echo json_encode(['success' => false, 'error' => 'Action inconnue: ' . $_POST['action']]);
            exit;
    }
    
    } catch (Exception $e) {
        // Gestion des erreurs dans les actions AJAX
        logError("Erreur action AJAX {$_POST['action']}: " . $e->getMessage());
        jsonError("Erreur serveur: " . $e->getMessage());
    } catch (Error $e) {
        // Gestion des erreurs fatales PHP 7+
        logError("Erreur fatale action AJAX {$_POST['action']}: " . $e->getMessage());
        jsonError("Erreur fatale: " . $e->getMessage());
    }
}

// Initialiser l'importeur pour la partie HTML avec gestion d'erreur
try {
$importer = new M3UImportV2();
} catch (Exception $e) {
    // En cas d'erreur, créer une instance factice avec configuration minimale
    $importer = new class {
        public function getAllowedUrls() {
            return ['iptv1' => '', 'iptv2' => '', 'test' => ''];
        }
    };
    $GLOBALS['config_error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import M3U V2 - VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .progress-container {
            display: none;
        }
        .channel-badge {
            font-size: 0.8em;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .loading-spinner {
            display: none;
        }
    </style>
</head>
<body class="bg-light">

<nav class="navbar navbar-dark bg-dark">
    <div class="container-fluid">
        <span class="navbar-brand mb-0 h1">
            <i class="bi bi-download"></i> Import M3U V2 - Background Processing
        </span>
        <div class="d-flex">
            <a href="index.php" class="btn btn-outline-light btn-sm me-2">
                <i class="bi bi-house"></i> Accueil
            </a>
            <a href="import_m3u.php" class="btn btn-outline-light btn-sm">
                <i class="bi bi-arrow-left"></i> Version Simple
            </a>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <!-- Configuration Check -->
    <?php
    // Vérifier s'il y a une erreur de configuration critique
    if (isset($GLOBALS['config_error'])): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle-fill"></i>
        <strong>Erreur de configuration critique :</strong>
        <?= htmlspecialchars($GLOBALS['config_error']) ?>
        <br><small>Vérifiez que le fichier .env contient toutes les variables requises.</small>
    </div>
    <?php endif;
    
    $missing_config = [];
    if (empty($importer->getAllowedUrls()['iptv1'])) $missing_config[] = 'UPDATE_IPTV1_URL';
    if (empty($importer->getAllowedUrls()['iptv2'])) $missing_config[] = 'UPDATE_IPTV2_URL';
    
    if (!empty($missing_config)): ?>
    <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>Configuration manquante :</strong>
        Veuillez définir les variables d'environnement : <code><?= implode(', ', $missing_config) ?></code>
        <br><small>Vous pouvez utiliser l'option "Test" qui fonctionne sans configuration.</small>
    </div>
    <?php endif; ?>

    <!-- Étape 1: Sélection de la source -->
    <div class="card mb-4" id="step1">
        <div class="card-header">
            <h5><i class="bi bi-1-circle"></i> Sélection de la source M3U</h5>
        </div>
        <div class="card-body">
            <form id="downloadForm">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Source IPTV</label>
                        <select class="form-select" name="source" required>
                            <option value="">Sélectionner une source...</option>
                            <?php if (!empty($importer->getAllowedUrls()['iptv1'])): ?>
                            <option value="iptv1">IPTV 1 (Principal)</option>
                            <?php endif; ?>
                            <?php if (!empty($importer->getAllowedUrls()['iptv2'])): ?>
                            <option value="iptv2">IPTV 2 (Secondaire)</option>
                            <?php endif; ?>
                            <option value="test">Test (Démo)</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Options</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="enable_filter" id="enableFilter" checked>
                            <label class="form-check-label" for="enableFilter">
                                <span class="badge bg-primary">🇫🇷</span> Filtrer les chaînes françaises uniquement
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-download"></i> Télécharger et traiter
                    </button>
                    <div class="loading-spinner spinner-border spinner-border-sm ms-2" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Progression -->
    <div class="progress-container" id="progressContainer">
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-hourglass-split"></i> Progression du traitement</h5>
            </div>
            <div class="card-body">
                <!-- Téléchargement -->
                <div class="mb-3">
                    <h6>📥 Téléchargement</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar" id="downloadProgress" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="downloadStatus">En attente...</small>
                </div>
                
                <!-- Filtrage -->
                <div class="mb-3" id="filterSection" style="display: none;">
                    <h6>🇫🇷 Filtrage français (Python)</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-info" id="filterProgress" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="filterStatus">En attente...</small>
                </div>
                
                <!-- Parsing -->
                <div class="mb-3" id="parseSection" style="display: none;">
                    <h6>📝 Analyse du contenu (TOUS les éléments)</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-warning" id="parseProgress" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted" id="parseStatus">Analyse en cours...</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats -->
    <div id="resultsContainer" style="display: none;">
        <!-- Statistiques -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="bi bi-list-ul fs-1"></i>
                        <h4 class="mt-2" id="totalChannels">0</h4>
                        <p class="mb-0">Chaînes trouvées</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4" id="filterStatsCard" style="display: none;">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-funnel fs-1"></i>
                        <h4 class="mt-2" id="filteredChannels">0</h4>
                        <p class="mb-0">Chaînes françaises</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-hdd fs-1"></i>
                        <h4 class="mt-2" id="fileSize">0 MB</h4>
                        <p class="mb-0">Taille du fichier</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques détaillées -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-bar-chart"></i> Statistiques détaillées</h5>
            </div>
            <div class="card-body">
                <div id="detailedStats">
                    <!-- Sera rempli via JavaScript -->
                </div>
            </div>
        </div>

        <!-- Système de recherche approfondie -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-search"></i> Recherche approfondie</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-8">
                        <input type="text" class="form-control" id="searchQuery" placeholder="Rechercher un titre, année, genre...">
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="searchType">
                            <option value="all">Tout</option>
                            <option value="movie">Films uniquement</option>
                            <option value="series">Séries uniquement</option>
                            <option value="tv">TV uniquement</option>
                        </select>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Année</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="number" class="form-control" id="yearFrom" placeholder="De" min="1900" max="2030">
                            </div>
                            <div class="col-6">
                                <input type="number" class="form-control" id="yearTo" placeholder="À" min="1900" max="2030">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Groupe</label>
                        <select class="form-select" id="groupFilter">
                            <option value="">Tous les groupes</option>
                            <!-- Sera rempli dynamiquement -->
                        </select>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-primary" onclick="performSearch()">
                            <i class="bi bi-search"></i> Rechercher
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearSearch()">
                            <i class="bi bi-x-circle"></i> Effacer
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="showAdvancedFilters()">
                            <i class="bi bi-funnel"></i> Filtres avancés
                        </button>
                    </div>
                </div>
                
                <!-- Filtres avancés -->
                <div id="advancedFilters" style="display: none;">
                    <hr>
                    <h6>Filtres avancés</h6>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">Saison</label>
                            <input type="number" class="form-control" id="seasonFilter" placeholder="Numéro de saison" min="1">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Épisode</label>
                            <input type="number" class="form-control" id="episodeFilter" placeholder="Numéro d'épisode" min="1">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Recherche dans l'URL</label>
                            <input type="text" class="form-control" id="urlFilter" placeholder="Mots-clés dans l'URL">
                        </div>
                    </div>
                </div>
                
                <!-- Résultats de recherche -->
                <div id="searchResults">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        Utilisez les filtres ci-dessus pour rechercher dans le contenu M3U.
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="bi bi-gear"></i> Actions</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Phase 2 :</strong> Implémentation des actions d'importation en cours de développement.
                </div>
                
                <button class="btn btn-success me-2" disabled>
                    <i class="bi bi-plus-circle"></i> Importer comme nouveau contenu
                </button>
                <button class="btn btn-warning me-2" disabled>
                    <i class="bi bi-arrow-repeat"></i> Mettre à jour contenu existant
                </button>
                <button class="btn btn-danger" onclick="cleanup()">
                    <i class="bi bi-trash"></i> Nettoyer et recommencer
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="import_v2.js"></script>

</body>
</html> 