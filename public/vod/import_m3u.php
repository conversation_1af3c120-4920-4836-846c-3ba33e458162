<?php
// Système d'importation M3U pour VOD IPTV

// CRITIAL: Augmenter la mémoire AVANT TOUT autre opération
$currentMemory = ini_get('memory_limit');
$newMemoryLimit = '1536M'; // 1.5GB pour éviter les crashes
ini_set('memory_limit', $newMemoryLimit);
ini_set('max_execution_time', 600); // 10 minutes

// Démarrer la session APRÈS l'augmentation mémoire avec protection
try {
    session_start();
    
    // Vérifier si la session contient des données très volumineuses
    $sessionSize = strlen(serialize($_SESSION));
    if ($sessionSize > 50 * 1024 * 1024) { // Si > 50MB
        error_log("⚠️ Session trop volumineuse (" . round($sessionSize/1024/1024, 1) . "MB), nettoyage automatique");
        
        // Garder seulement les éléments essentiels
        $essentialData = [];
        if (isset($_SESSION['analysis_data']['statistics'])) {
            $essentialData['analysis_data']['statistics'] = $_SESSION['analysis_data']['statistics'];
            $essentialData['analysis_data']['metadata'] = $_SESSION['analysis_data']['metadata'] ?? [];
        }
        if (isset($_SESSION['m3u_source'])) {
            $essentialData['m3u_source'] = $_SESSION['m3u_source'];
        }
        
        // Nettoyer et remplacer
        session_destroy();
        session_start();
        $_SESSION = $essentialData;
        
        error_log("✅ Session nettoyée et recréée");
    }
    
    error_log("🚀 SESSION DÉMARRÉE - ID: " . session_id() . " (Taille: " . round($sessionSize/1024, 1) . "KB)");
    
} catch (Error $e) {
    // En cas d'erreur fatale lors du chargement de session
    error_log("💥 ERREUR SESSION: " . $e->getMessage());
    
    // Forcer une nouvelle session propre
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_destroy();
    }
    session_start();
    error_log("🆕 Nouvelle session créée après erreur");
}

error_log("💾 Limite mémoire: $currentMemory -> $newMemoryLimit");

// Charger la configuration
try {
    $config = require_once 'config.php';
} catch (Exception $e) {
    $error = 'Erreur de configuration: ' . $e->getMessage();
}

// Fonction de connexion aux bases de données
function getConnection($dbConfig) {
    try {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    } catch (PDOException $e) {
        throw new Exception("Erreur de connexion à {$dbConfig['dbname']}: " . $e->getMessage());
    }
}

// Fonction pour parser et formater les titres IPTV
// Fonction pour extraire le nom du fichier et ajouter le préfixe selon la base
function prepareSourceFilename($url, $targetDatabase) {
    // Extraire le nom du fichier de l'URL
    $filename = basename($url);
    
    // Ajouter le préfixe "mega-" si c'est iptv2
    if ($targetDatabase === 'iptv2') {
        $filename = 'mega-' . $filename;
    }
    
    return $filename;
}

function parseIptvTitle($title) {
    $result = [
        'clean_title' => $title,
        'year' => null,
        'quality' => 'HD',
        'language' => null,
        'country' => null
    ];
    
    // Nettoyer le titre initial
    $cleanTitle = trim($title);
    
    // Pattern pour extraire le pays au début (FR -, EN -, US -, etc.)
    if (preg_match('/^([A-Z]{2,3})\s*-\s*(.+)$/i', $cleanTitle, $matches)) {
        $result['country'] = strtoupper($matches[1]);
        $cleanTitle = trim($matches[2]);
    }
    
    // Pattern pour extraire l'année (1900-2099) entre parenthèses
    if (preg_match('/^(.*?)\s*\((\d{4})\)\s*(.*)$/i', $cleanTitle, $matches)) {
        $result['clean_title'] = trim($matches[1]);
        $result['year'] = (int)$matches[2];
        $afterYear = trim($matches[3]);
        
        // Vérifier s'il y a des infos de qualité après l'année
        if (!empty($afterYear)) {
            $cleanTitle = $result['clean_title'] . ' ' . $afterYear;
        } else {
            $cleanTitle = $result['clean_title'];
        }
    }
    
    // Pattern pour extraire les qualités (4K, UHD, FHD, HD, SD, etc.)
    $qualityPatterns = [
        '/\b(4K|UHD|ULTRA[\s\-]?HD)\b/i' => '4K',
        '/\b(FHD|1080P?|FULL[\s\-]?HD)\b/i' => 'FHD',
        '/\b(HD|720P?)\b/i' => 'HD',
        '/\b(SD|480P?|360P?)\b/i' => 'SD',
        '/\b(CAM|TS|TC|HDTS)\b/i' => 'CAM',
        '/\b(DVDRIP|DVD)\b/i' => 'DVD',
        '/\b(WEBRIP|WEB[\s\-]?DL)\b/i' => 'WEB',
        '/\b(BLURAY|BD|BLU[\s\-]?RAY)\b/i' => 'BluRay'
    ];
    
    foreach ($qualityPatterns as $pattern => $quality) {
        if (preg_match($pattern, $cleanTitle, $matches)) {
            $result['quality'] = $quality;
            // Retirer la qualité du titre
            $cleanTitle = preg_replace($pattern, '', $cleanTitle);
            break;
        }
    }
    
    // Pattern pour extraire la langue (VOSTFR, VF, VO, etc.)
    $languagePatterns = [
        '/\b(VOSTFR|VF|FRENCH|FR)\b/i' => 'fr',
        '/\b(VOST|VO|ENGLISH|EN)\b/i' => 'en',
        '/\b(GERMAN|DE)\b/i' => 'de',
        '/\b(SPANISH|ES)\b/i' => 'es',
        '/\b(ITALIAN|IT)\b/i' => 'it'
    ];
    
    foreach ($languagePatterns as $pattern => $lang) {
        if (preg_match($pattern, $cleanTitle, $matches)) {
            $result['language'] = $lang;
            // Retirer la langue du titre
            $cleanTitle = preg_replace($pattern, '', $cleanTitle);
            break;
        }
    }
    
    // Nettoyer le titre final (retirer les espaces multiples, caractères spéciaux en fin)
    $cleanTitle = preg_replace('/\s+/', ' ', $cleanTitle); // Espaces multiples
    $cleanTitle = preg_replace('/[\[\](){}]+$/', '', $cleanTitle); // Parenthèses/crochets vides en fin
    $cleanTitle = trim($cleanTitle, " \t\n\r\0\x0B.-_");
    
    $result['clean_title'] = $cleanTitle;
    
    // Log pour debug
    error_log("🔄 PARSING TITRE: '$title' → Titre: '{$result['clean_title']}', Année: {$result['year']}, Qualité: {$result['quality']}, Langue: {$result['language']}, Pays: {$result['country']}");
    
    return $result;
}

// Fonction pour parser les titres de séries avec saison/épisode
function parseSeriesTitle($title, $season = null, $episode = null) {
    $parsed = parseIptvTitle($title);
    
    // Pour les séries, extraire aussi les infos S/E si pas déjà fournies
    if ($season === null || $episode === null) {
        // Pattern pour S01E01, S01 E01, S1E1, 1x01, etc.
        $seriesPatterns = [
            '/\bS(\d+)\s+E(\d+)\b/i',      // S01 E01 (avec espace)
            '/\bS(\d+)\s*E(\d+)\b/i',      // S01E01 (sans espace)
            '/\bS(\d+)E(\d+)\b/i',         // S1E1
            '/\b(\d+)x(\d+)\b/i',          // 1x01
            '/\bSaison\s*(\d+)\s*Episode\s*(\d+)\b/i'  // Saison 1 Episode 1
        ];
        
        foreach ($seriesPatterns as $pattern) {
            if (preg_match($pattern, $title, $matches)) {
                $parsed['season'] = (int)$matches[1];
                $parsed['episode'] = (int)$matches[2];
                
                // Retirer l'info S/E du titre
                $parsed['clean_title'] = preg_replace($pattern, '', $parsed['clean_title']);
                $parsed['clean_title'] = trim($parsed['clean_title'], " \t\n\r\0\x0B.-_");
                break;
            }
        }
    } else {
        $parsed['season'] = $season;
        $parsed['episode'] = $episode;
    }
    
    return $parsed;
}

// Fonction pour insérer un film dans la base IPTV
function insertMovieToIptv($movieData, $connection, $databaseName, $targetDatabase) {
    try {
        // Parser le titre pour extraire les informations
        $parsed = parseIptvTitle($movieData['title']);
        
        // Utiliser l'année du parsing si elle n'est pas fournie
        $year = $movieData['year'] ?? $parsed['year'];
        $quality = $movieData['quality'] ?? $parsed['quality'];
        $language = $movieData['language'] ?? $parsed['language'] ?? 'fr';
        
        // Vérifier si le film existe déjà (par titre propre ET année)
        if ($year !== null) {
            // Si on a une année, vérifier titre + année
            $checkStmt = $connection->prepare("SELECT id FROM poster_iptv WHERE title = ? AND year = ? AND type = 'movie'");
            $checkStmt->execute([$parsed['clean_title'], $year]);
        } else {
            // Si pas d'année, vérifier seulement le titre
            $checkStmt = $connection->prepare("SELECT id FROM poster_iptv WHERE title = ? AND year IS NULL AND type = 'movie'");
            $checkStmt->execute([$parsed['clean_title']]);
        }
        
        if ($checkStmt->fetch()) {
            $movieInfo = $parsed['clean_title'] . ($year ? " ($year)" : "");
            error_log("🎬 Film déjà existant: " . $movieInfo);
            return ['status' => 'exists', 'message' => "Film déjà existant: $movieInfo"];
        }
        
        // Insérer le poster (film) avec le titre propre
        $posterStmt = $connection->prepare("
            INSERT INTO poster_iptv (title, type, tmdb_id, year, poster_image, created_at) 
            VALUES (?, 'movie', ?, ?, NULL, NOW())
        ");
        
        $posterStmt->execute([
            $parsed['clean_title'],
            $movieData['tmdb_id'] ?? null,
            $year
        ]);
        
        $posterId = $connection->lastInsertId();
        
        // Insérer la source vidéo avec la qualité détectée
        $sourceStmt = $connection->prepare("
            INSERT INTO source_iptv (poster_iptv_id, url, quality, language) 
            VALUES (?, ?, ?, ?)
        ");
        
        $sourceStmt->execute([
            $posterId,
            prepareSourceFilename($movieData['url'], $targetDatabase),
            $quality,
            $language
        ]);
        
        error_log("✅ Film inséré dans $databaseName: '{$parsed['clean_title']}' ({$year}) - Qualité: {$quality} (ID: $posterId)");
        return ['status' => 'success', 'id' => $posterId, 'message' => 'Film inséré avec succès'];
        
    } catch (Exception $e) {
        error_log("❌ Erreur insertion film dans $databaseName: " . $e->getMessage());
        throw new Exception("Erreur lors de l'insertion du film: " . $e->getMessage());
    }
}

// Fonction pour insérer une série dans la base IPTV avec workflow complet
function insertSeriesToIptv($seriesData, $connection, $databaseName, $targetDatabase) {
    try {
        $connection->beginTransaction();
        
        // ÉTAPE 1: Parser le titre de l'épisode pour extraire les informations
        $parsed = parseSeriesTitle($seriesData['title'], $seriesData['season'] ?? null, $seriesData['episode'] ?? null);
        
        // Valider les données nécessaires
        $seriesName = $seriesData['clean_name'] ?? $parsed['clean_title'];
        $season = $seriesData['season'] ?? $parsed['season'];
        $episode = $seriesData['episode'] ?? $parsed['episode'];
        $quality = $seriesData['quality'] ?? $parsed['quality'];
        $language = $seriesData['language'] ?? $parsed['language'] ?? 'fr';
        $year = $seriesData['year'] ?? $parsed['year'];
        
        // Vérification de la cohérence des données
        if (empty($seriesName)) {
            throw new Exception("Nom de série manquant ou invalide");
        }
        if ($season === null || $episode === null) {
            throw new Exception("Numéro de saison ou d'épisode manquant (S: $season, E: $episode)");
        }
        
        error_log("📺 WORKFLOW SÉRIE: '{$seriesName}' S{$season}E{$episode} vers $databaseName");
        
        // ÉTAPE 2: Vérifier/Créer la série
        if ($year !== null) {
            $checkStmt = $connection->prepare("SELECT id FROM poster_iptv WHERE title = ? AND year = ? AND type = 'tvshow'");
            $checkStmt->execute([$seriesName, $year]);
        } else {
            $checkStmt = $connection->prepare("SELECT id FROM poster_iptv WHERE title = ? AND type = 'tvshow'");
            $checkStmt->execute([$seriesName]);
        }
        
        $existingSeries = $checkStmt->fetch();
        
        if ($existingSeries) {
            $posterId = $existingSeries['id'];
            $seriesInfo = $seriesName . ($year ? " ($year)" : "");
            error_log("✅ ÉTAPE 2: Série existante trouvée - " . $seriesInfo . " (ID: $posterId)");
        } else {
            // Créer la nouvelle série
            $posterStmt = $connection->prepare("
                INSERT INTO poster_iptv (title, type, tmdb_id, year, poster_image, created_at) 
                VALUES (?, 'tvshow', ?, ?, NULL, NOW())
            ");
            
            $posterStmt->execute([
                $seriesName,
                $seriesData['tmdb_id'] ?? null,
                $year
            ]);
            
            $posterId = $connection->lastInsertId();
            $seriesInfo = $seriesName . ($year ? " ($year)" : "");
            error_log("✅ ÉTAPE 2: Nouvelle série créée - " . $seriesInfo . " (ID: $posterId)");
        }
        
        // ÉTAPE 3: Vérifier/Créer la saison
        $seasonStmt = $connection->prepare("SELECT id FROM season_iptv WHERE poster_iptv_id = ? AND season_number = ?");
        $seasonStmt->execute([$posterId, $season]);
        $existingSeason = $seasonStmt->fetch();
        
        if ($existingSeason) {
            $seasonId = $existingSeason['id'];
            error_log("✅ ÉTAPE 3: Saison existante trouvée - Saison $season (ID: $seasonId)");
        } else {
            // Créer la nouvelle saison
            $insertSeasonStmt = $connection->prepare("
                INSERT INTO season_iptv (poster_iptv_id, season_number, title, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            
            $seasonTitle = "Saison " . $season;
            $insertSeasonStmt->execute([$posterId, $season, $seasonTitle]);
            $seasonId = $connection->lastInsertId();
            error_log("✅ ÉTAPE 3: Nouvelle saison créée - Saison $season (ID: $seasonId)");
        }
        
        // ÉTAPE 4: Vérifier si l'épisode existe déjà
        $episodeStmt = $connection->prepare("SELECT id FROM episode_iptv WHERE season_iptv_id = ? AND episode_number = ?");
        $episodeStmt->execute([$seasonId, $episode]);
        $existingEpisode = $episodeStmt->fetch();
        
        if ($existingEpisode) {
            error_log("⚠️ ÉTAPE 4: Épisode déjà existant - S{$season}E{$episode}");
            $connection->rollback();
            return ['status' => 'exists', 'message' => "Épisode déjà existant: {$seriesName} S{$season}E{$episode}"];
        }
        
        // ÉTAPE 5: Créer l'épisode
        $insertEpisodeStmt = $connection->prepare("
            INSERT INTO episode_iptv (season_iptv_id, episode_number, title, description, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        $episodeTitle = "Episode " . $episode;
        
        $insertEpisodeStmt->execute([
            $seasonId,
            $episode,
            $episodeTitle,
            null
        ]);
        
        $episodeId = $connection->lastInsertId();
        error_log("✅ ÉTAPE 5: Nouvel épisode créé - S{$season}E{$episode} (ID: $episodeId)");
        
        // ÉTAPE 6: Créer la source de l'épisode
        $sourceEpisodeStmt = $connection->prepare("
            INSERT INTO source_episode_iptv (episode_iptv_id, url, quality, language, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        $sourceEpisodeStmt->execute([
            $episodeId,
            prepareSourceFilename($seriesData['url'], $targetDatabase),
            $quality,
            $language
        ]);
        
        $connection->commit();
        
        error_log("🎉 WORKFLOW TERMINÉ: '{$seriesName}' S{$season}E{$episode} - Qualité: {$quality} dans $databaseName");
        return ['status' => 'success', 'id' => $episodeId, 'message' => 'Épisode inséré avec succès'];
        
    } catch (Exception $e) {
        $connection->rollback();
        error_log("❌ ERREUR WORKFLOW SÉRIE dans $databaseName: " . $e->getMessage());
        error_log("   Données: " . json_encode($seriesData));
        throw new Exception("Erreur lors de l'insertion de l'épisode: " . $e->getMessage());
    }
}

// Fonction pour insérer des données sélectionnées
function insertSelectedItems($selectedItems, $targetDatabase, $config) {
    $results = [
        'success' => [],
        'errors' => [],
        'exists' => [],
        'statistics' => [
            'total_selected' => count($selectedItems),
            'movies_inserted' => 0,
            'episodes_inserted' => 0,
            'already_exists' => 0,
            'errors_count' => 0
        ]
    ];
    
    try {
        // Sélectionner la configuration de base selon la cible
        $dbConfig = $targetDatabase === 'iptv2' ? $config['iptv_db2'] : $config['iptv_db'];
        $databaseName = $dbConfig['dbname'];
        
        // Connexion à la base cible
        $connection = getConnection($dbConfig);
        error_log("🔗 Connexion établie vers: $databaseName");
        
        foreach ($selectedItems as $item) {
            try {
                if ($item['type'] === 'movie') {
                    $result = insertMovieToIptv($item, $connection, $databaseName, $targetDatabase);
                    
                    if ($result['status'] === 'success') {
                        $results['success'][] = $item['title'];
                        $results['statistics']['movies_inserted']++;
                    } elseif ($result['status'] === 'exists') {
                        $results['exists'][] = $item['title'];
                        $results['statistics']['already_exists']++;
                    }
                    
                } elseif ($item['type'] === 'tvshow' || $item['type'] === 'series') {
                    $result = insertSeriesToIptv($item, $connection, $databaseName, $targetDatabase);
                    
                    if ($result['status'] === 'success') {
                        $results['success'][] = $item['title'] . " (S" . $item['season'] . "E" . $item['episode'] . ")";
                        $results['statistics']['episodes_inserted']++;
                    } elseif ($result['status'] === 'exists') {
                        $results['exists'][] = $item['title'] . " (S" . $item['season'] . "E" . $item['episode'] . ")";
                        $results['statistics']['already_exists']++;
                    }
                }
                
            } catch (Exception $e) {
                $results['errors'][] = "Erreur pour '" . $item['title'] . "': " . $e->getMessage();
                $results['statistics']['errors_count']++;
                error_log("❌ Erreur insertion individuelle: " . $e->getMessage());
            }
        }
        
        error_log("📊 Insertion terminée - Succès: " . $results['statistics']['movies_inserted'] + $results['statistics']['episodes_inserted'] . 
                  ", Erreurs: " . $results['statistics']['errors_count'] . 
                  ", Déjà existants: " . $results['statistics']['already_exists']);
        
    } catch (Exception $e) {
        error_log("❌ Erreur générale insertion: " . $e->getMessage());
        throw new Exception("Erreur lors de l'insertion en base: " . $e->getMessage());
    }
    
    return $results;
}

// Fonction pour télécharger le fichier M3U
function downloadM3U($url, $userAgent = null) {
    // Validation de l'URL
    if (empty($url)) {
        throw new Exception("URL vide fournie");
    }
    
    // Nettoyer l'URL des espaces et caractères indésirables
    $url = trim($url);
    
    // Validation basique de l'URL
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        throw new Exception("URL mal formée: '$url'");
    }
    
    // Vérifier que l'URL commence par http/https
    if (!preg_match('/^https?:\/\//', $url)) {
        throw new Exception("URL doit commencer par http:// ou https://: '$url'");
    }
    
    if (!$userAgent) {
        $userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36";
    }
    
    $tempFile = sys_get_temp_dir() . '/playlist_' . uniqid() . '.m3u';
    
    // Log pour debug
    error_log("🌐 Tentative de téléchargement M3U depuis: " . substr($url, 0, 100) . "...");
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 300); // 5 minutes timeout
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 30 secondes pour la connexion
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // Limiter les redirections
    
    $content = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $curlInfo = curl_getinfo($ch);
    curl_close($ch);
    
    // Logs détaillés pour debug
    error_log("📊 Résultat cURL: HTTP $httpCode, Taille: " . strlen($content) . " octets");
    
    if ($error) {
        error_log("❌ Erreur cURL détaillée: $error");
        error_log("🔍 URL problématique: $url");
        throw new Exception("Erreur cURL: $error");
    }
    
    if ($httpCode !== 200) {
        error_log("❌ Code HTTP non-200: $httpCode pour URL: $url");
        throw new Exception("Erreur HTTP: Code $httpCode - " . ($curlInfo['url'] ?? 'URL inconnue'));
    }
    
    if (empty($content)) {
        throw new Exception("Contenu vide reçu du serveur");
    }
    
    // Vérifier que le contenu ressemble à un M3U
    if (!preg_match('/^#EXTM3U/i', $content) && !preg_match('/#EXTINF/i', $content)) {
        error_log("⚠️ Contenu ne ressemble pas à un M3U. Début: " . substr($content, 0, 200));
        // Ne pas lever d'exception, juste un warning
    }
    
    if (file_put_contents($tempFile, $content) === false) {
        throw new Exception("Impossible d'écrire le fichier temporaire: $tempFile");
    }
    
    error_log("✅ Téléchargement M3U réussi: " . strlen($content) . " octets sauvés dans $tempFile");
    
    return [
        'file' => $tempFile,
        'size' => strlen($content),
        'lines' => substr_count($content, "\n"),
        'url' => $url,
        'http_code' => $httpCode
    ];
}

// Fonctions de filtrage français supprimées - analyse de TOUT le contenu maintenant

// Fonction pour analyser un fichier M3U via le script Python
function analyzeM3UWithPython($filePath, $targetDatabase = 'iptv1') {
    // Déterminer le répertoire de stockage (même logique que pour le stockage des fichiers)
    $dataDir = __DIR__ . '/data_download/';
    if (!is_dir($dataDir) || !is_writable($dataDir)) {
        $dataDir = sys_get_temp_dir() . '/m3u_analysis/';
        if (!is_dir($dataDir)) {
            mkdir($dataDir, 0777, true);
        }
    }
    
    $timestamp = date('YmdHis');
    
    // Déterminer le préfixe selon la base cible
    $prefix = $targetDatabase === 'iptv2' ? 'mega-' : 'china-';
    $jsonFile = $dataDir . "{$prefix}analysis_{$timestamp}.json";
    $pythonScript = __DIR__ . '/analyze_m3u.py';
    
    // Vérifier que le script Python existe
    if (!file_exists($pythonScript)) {
        throw new Exception("Script Python non trouvé: $pythonScript");
    }
    
    // Augmenter la limite de mémoire PHP temporairement pour les gros fichiers
    $originalMemoryLimit = ini_get('memory_limit');
    ini_set('memory_limit', '512M');
    error_log("🧠 Augmentation mémoire PHP: $originalMemoryLimit -> 512M");
    
    try {
        // Exécuter le script Python
        $command = "cd " . escapeshellarg(__DIR__) . " && python3 " . escapeshellarg($pythonScript) . " " . escapeshellarg($filePath) . " " . escapeshellarg($jsonFile) . " 2>&1";
        
        error_log("🐍 Exécution du script Python: $command");
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            $errorOutput = implode("\n", $output);
            error_log("❌ Erreur Python: $errorOutput");
            throw new Exception("Erreur lors de l'exécution du script Python: $errorOutput");
        }
        
        // Vérifier que le fichier JSON a été créé
        if (!file_exists($jsonFile)) {
            throw new Exception("Le fichier JSON d'analyse n'a pas été créé: $jsonFile");
        }
        
        // Vérifier la taille du fichier JSON avant de le lire
        $jsonSize = filesize($jsonFile);
        error_log("📊 Taille du fichier JSON: " . number_format($jsonSize) . " octets");
        
        // Gestion des fichiers très volumineux
        if ($jsonSize > 200 * 1024 * 1024) { // 200MB
            throw new Exception("Fichier JSON trop volumineux (" . round($jsonSize/1024/1024, 1) . "MB). Limite: 200MB");
        }
        
        if ($jsonSize > 50 * 1024 * 1024) { // Si > 50MB
            error_log("⚠️ Fichier JSON volumineux, augmentation mémoire");
            ini_set('memory_limit', '1536M'); // 1.5GB temporaire
        }
        
        // Lire et décoder le JSON avec gestion mémoire optimisée
        $jsonContent = @file_get_contents($jsonFile);
        if ($jsonContent === false) {
            throw new Exception("Impossible de lire le fichier JSON: $jsonFile");
        }
        
        // Décoder par petits chunks si fichier très volumineux
        if ($jsonSize > 100 * 1024 * 1024) { // Si > 100MB
            // Forcer le garbage collector avant décoding
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
        }
        
        $analysisData = @json_decode($jsonContent, true);
        if ($analysisData === null) {
            $jsonError = json_last_error_msg();
            error_log("❌ Erreur JSON: $jsonError (Taille: " . round($jsonSize/1024/1024, 1) . "MB)");
            throw new Exception("Erreur lors du décodage JSON: $jsonError. Fichier possiblement trop volumineux.");
        }
        
        // Debug détaillé des données JSON décodées
        error_log("🔍 JSON DÉCODÉ - Clés principales: " . implode(', ', array_keys($analysisData)));
        error_log("🔍 JSON DÉCODÉ - Movies: " . (isset($analysisData['movies']) ? count($analysisData['movies']) : 'NON DÉFINI'));
        error_log("🔍 JSON DÉCODÉ - Series: " . (isset($analysisData['series']) ? count($analysisData['series']) : 'NON DÉFINI'));
        error_log("🔍 JSON DÉCODÉ - Statistics: " . (isset($analysisData['statistics']) ? 'OUI' : 'NON'));
        
        // Libérer immédiatement la mémoire du contenu JSON brut
        unset($jsonContent);
        
        // Forcer le nettoyage mémoire
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        // Ajouter des métadonnées pour l'historique
        $analysisData['metadata'] = [
            'analysis_date' => date('Y-m-d H:i:s'),
            'timestamp' => $timestamp,
            'source_file' => basename($filePath),
            'json_file' => basename($jsonFile),
            'analysis_size' => $jsonSize,
            'target_database' => $targetDatabase,
            'database_prefix' => $prefix
        ];
        
        // Sauvegarder avec optimisation : fichiers très volumineux = métadonnées seulement
        if ($jsonSize > 150 * 1024 * 1024) { // Si > 150MB
            // Créer une version allégée avec seulement les métadonnées et statistiques
            $lightData = [
                'metadata' => $analysisData['metadata'],
                'statistics' => $analysisData['statistics'],
                'sample_movies' => array_slice($analysisData['movies'], 0, 100), // 100 premiers films
                'sample_series' => array_slice($analysisData['series'], 0, 100), // 100 premiers épisodes
                'note' => 'Version allégée - fichier original trop volumineux (' . round($jsonSize/1024/1024, 1) . 'MB)'
            ];
            
            file_put_contents($jsonFile, json_encode($lightData, JSON_PRETTY_PRINT));
            error_log("💾 Sauvegarde allégée pour fichier volumineux: " . basename($jsonFile));
            
        } else {
            // Sauvegarde complète pour fichiers normaux
            error_log("💾 Sauvegarde complète: " . basename($jsonFile) . " (" . round($jsonSize/1024/1024, 1) . "MB)");
            file_put_contents($jsonFile, json_encode($analysisData, JSON_PRETTY_PRINT));
        }
        
        error_log("✅ Analyse Python terminée: " . $analysisData['statistics']['total_movies'] . " films, " . $analysisData['statistics']['total_series_episodes'] . " épisodes");
        
        return $analysisData;
        
    } finally {
        // Restaurer la limite de mémoire originale
        ini_set('memory_limit', $originalMemoryLimit);
        error_log("🧠 Restauration mémoire PHP: " . ini_get('memory_limit'));
    }
}

// Fonction pour récupérer l'historique des analyses
function getAnalysisHistory() {
    $history = [];
    
    // Chercher dans plusieurs répertoires possibles
    $searchDirs = [
        __DIR__ . '/data_download/',
        sys_get_temp_dir() . '/m3u_analysis/'
    ];
    
    foreach ($searchDirs as $dataDir) {
        if (!is_dir($dataDir)) {
            continue;
        }
        
        // Scanner tous les fichiers JSON d'analyse (avec et sans préfixes)
        $files = array_merge(
            glob($dataDir . 'analysis_*.json'),      // Anciens fichiers sans préfixe
            glob($dataDir . 'china-analysis_*.json'), // Fichiers IPTV1
            glob($dataDir . 'mega-analysis_*.json')   // Fichiers IPTV2
        );
        
        foreach ($files as $file) {
            try {
                // Optimisation mémoire : ne charger que les métadonnées nécessaires
                $fileSize = filesize($file);
                if ($fileSize > 200 * 1024 * 1024) { // Si fichier > 200MB
                    error_log("⚠️ Fichier historique trop volumineux ignoré: " . basename($file) . " (" . round($fileSize/1024/1024, 1) . "MB)");
                    continue;
                }
                
                // Lecture sécurisée avec gestion mémoire
                $jsonContent = @file_get_contents($file);
                if ($jsonContent === false) {
                    error_log("⚠️ Impossible de lire le fichier historique: " . basename($file));
                    continue;
                }
                
                // Décoder avec gestion d'erreur mémoire
                $data = @json_decode($jsonContent, true);
                
                // Libérer immédiatement la mémoire du contenu brut
                unset($jsonContent);
                
                if ($data === null || !isset($data['statistics'])) {
                    error_log("⚠️ Fichier JSON invalide: " . basename($file));
                    continue;
                }
                
                $basename = basename($file);
                $timestamp = '';
                $database_type = 'legacy';
                
                // Extraire le timestamp et détecter le type de base
                if (preg_match('/china-analysis_(\d{14})\.json/', $basename, $matches)) {
                    $timestamp = $matches[1];
                    $database_type = 'iptv1';
                } elseif (preg_match('/mega-analysis_(\d{14})\.json/', $basename, $matches)) {
                    $timestamp = $matches[1];
                    $database_type = 'iptv2';
                } elseif (preg_match('/analysis_(\d{14})\.json/', $basename, $matches)) {
                    $timestamp = $matches[1];
                    $database_type = 'legacy';
                }
                
                $history[] = [
                    'file' => $file,
                    'basename' => $basename,
                    'timestamp' => $timestamp,
                    'database_type' => $database_type,
                    'date' => isset($data['metadata']['analysis_date']) ? 
                        $data['metadata']['analysis_date'] : 
                        (is_file($file) ? date('Y-m-d H:i:s', filemtime($file)) : 'Inconnue'),
                    'source_file' => isset($data['metadata']['source_file']) ? 
                        $data['metadata']['source_file'] : 'Fichier M3U',
                    'statistics' => $data['statistics'],
                    'size' => filesize($file),
                    'preview' => [
                        'total_items' => $data['statistics']['total_movies'] + $data['statistics']['total_series_episodes'],
                        'movies' => $data['statistics']['total_movies'],
                        'episodes' => $data['statistics']['total_series_episodes'],
                        'series' => $data['statistics']['unique_series'],
                        'seasons' => $data['statistics']['total_seasons']
                    ]
                ];
                
            } catch (Exception $e) {
                error_log("⚠️ Erreur lecture historique pour $file: " . $e->getMessage());
                continue;
            }
        }
    }
    
    // Trier par date décroissante (plus récent en premier)
    usort($history, function($a, $b) {
        return strcmp($b['timestamp'], $a['timestamp']);
    });
    
    return $history;
}

// Fonction pour optimiser les données avant stockage en session
function optimizeDataForSession($analysisData) {
    $originalSize = strlen(serialize($analysisData));
    error_log("🔧 OPTIMIZE SESSION - Taille originale: " . round($originalSize/1024/1024, 1) . "MB");
    error_log("🔧 OPTIMIZE SESSION - Clés entrée: " . implode(', ', array_keys($analysisData)));
    
    // Si les données sont trop volumineuses, créer une version allégée
    if ($originalSize > 20 * 1024 * 1024) { // Si > 20MB
        error_log("⚠️ Données d'analyse volumineuses (" . round($originalSize/1024/1024, 1) . "MB), création version allégée");
        
        $lightData = [
            'metadata' => $analysisData['metadata'] ?? [],
            'statistics' => $analysisData['statistics'] ?? [],
            'movies' => array_slice($analysisData['movies'] ?? [], 0, 1000), // Max 1000 films
            'series' => array_slice($analysisData['series'] ?? [], 0, 2000), // Max 2000 épisodes
            'note' => 'Version allégée pour session - ' . count($analysisData['movies'] ?? []) . ' films, ' . count($analysisData['series'] ?? []) . ' épisodes au total',
            'original_counts' => [
                'total_movies' => count($analysisData['movies'] ?? []),
                'total_series' => count($analysisData['series'] ?? [])
            ]
        ];
        
        $newSize = strlen(serialize($lightData));
        error_log("✅ Version allégée créée: " . round($originalSize/1024/1024, 1) . "MB → " . round($newSize/1024/1024, 1) . "MB");
        error_log("✅ Version allégée - clés: " . implode(', ', array_keys($lightData)));
        
        return $lightData;
    }
    
    error_log("✅ OPTIMIZE SESSION - Pas d'optimisation nécessaire, retour données originales");
    return $analysisData;
}

// Fonction pour nettoyer automatiquement l'historique (éviter l'accumulation)
function cleanupAnalysisHistory() {
    $searchDirs = [
        __DIR__ . '/data_download/',
        sys_get_temp_dir() . '/m3u_analysis/'
    ];
    
    foreach ($searchDirs as $dataDir) {
        if (!is_dir($dataDir)) continue;
        
        $files = array_merge(
            glob($dataDir . 'analysis_*.json'),      // Anciens fichiers sans préfixe
            glob($dataDir . 'china-analysis_*.json'), // Fichiers IPTV1
            glob($dataDir . 'mega-analysis_*.json')   // Fichiers IPTV2
        );
        if (count($files) <= 20) continue; // Garder max 20 analyses
        
        // Trier par date de modification (plus ancien en premier)
        usort($files, function($a, $b) {
            return filemtime($a) - filemtime($b);
        });
        
        // Supprimer les plus anciens, garder seulement les 15 plus récents
        $filesToDelete = array_slice($files, 0, count($files) - 15);
        
        foreach ($filesToDelete as $file) {
            if (unlink($file)) {
                error_log("🧹 Nettoyage historique: supprimé " . basename($file));
            }
        }
    }
}

// Fonction pour supprimer un historique spécifique
function deleteAnalysisHistory($historyFile) {
    if (empty($historyFile)) {
        throw new Exception("Aucun fichier d'historique spécifié");
    }
    
    if (!file_exists($historyFile)) {
        throw new Exception("Fichier d'historique non trouvé: " . basename($historyFile));
    }
    
    // Vérifier que le fichier est dans le bon répertoire pour des raisons de sécurité
    $allowedDirs = [
        realpath(__DIR__ . '/data_download/'),
        realpath(sys_get_temp_dir() . '/m3u_analysis/')
    ];
    
    $realFile = realpath($historyFile);
    $isAllowed = false;
    
    foreach ($allowedDirs as $allowedDir) {
        if ($allowedDir && $realFile && strpos($realFile, $allowedDir) === 0) {
            $isAllowed = true;
            break;
        }
    }
    
    if (!$isAllowed) {
        throw new Exception("Accès non autorisé au fichier");
    }
    
    if (!unlink($historyFile)) {
        throw new Exception("Impossible de supprimer le fichier d'historique");
    }
    
    error_log("🗑️ Historique supprimé: " . basename($historyFile));
    return true;
}

// Fonction pour supprimer tout l'historique
function deleteAllAnalysisHistory() {
    $searchDirs = [
        __DIR__ . '/data_download/',
        sys_get_temp_dir() . '/m3u_analysis/'
    ];
    
    $deletedCount = 0;
    $errors = [];
    
    foreach ($searchDirs as $dataDir) {
        if (!is_dir($dataDir)) continue;
        
        $files = array_merge(
            glob($dataDir . 'analysis_*.json'),      // Anciens fichiers sans préfixe
            glob($dataDir . 'china-analysis_*.json'), // Fichiers IPTV1
            glob($dataDir . 'mega-analysis_*.json')   // Fichiers IPTV2
        );
        
        foreach ($files as $file) {
            try {
                if (unlink($file)) {
                    $deletedCount++;
                    error_log("🗑️ Historique supprimé: " . basename($file));
                }
            } catch (Exception $e) {
                $errors[] = basename($file) . ": " . $e->getMessage();
            }
        }
    }
    
    if (!empty($errors)) {
        throw new Exception("Erreurs lors de la suppression: " . implode(", ", $errors));
    }
    
    return $deletedCount;
}

// Fonction pour charger une analyse depuis l'historique avec gestion mémoire optimisée
function loadAnalysisFromHistory($analysisFile) {
    if (!file_exists($analysisFile)) {
        throw new Exception("Fichier d'analyse non trouvé: $analysisFile");
    }
    
    // Vérifier la taille du fichier avant de le charger
    $fileSize = filesize($analysisFile);
    error_log("📁 Chargement fichier historique: " . basename($analysisFile) . " (" . round($fileSize/1024/1024, 1) . "MB)");
    
    if ($fileSize > 100 * 1024 * 1024) { // Si > 100MB
        throw new Exception("Fichier d'analyse trop volumineux (" . round($fileSize/1024/1024, 1) . "MB). Limite: 100MB");
    }
    
    // Augmenter temporairement la mémoire si nécessaire
    $originalMemoryLimit = ini_get('memory_limit');
    if ($fileSize > 50 * 1024 * 1024) { // Si > 50MB
        ini_set('memory_limit', '1536M'); // 1.5GB temporaire
        error_log("💾 Augmentation mémoire temporaire: $originalMemoryLimit -> 1536M");
    }
    
    try {
        // Lecture avec gestion d'erreur mémoire
        $jsonContent = @file_get_contents($analysisFile);
        if ($jsonContent === false) {
            throw new Exception("Impossible de lire le fichier d'analyse: $analysisFile");
        }
        
        // Décoder avec gestion d'erreur
        $analysisData = @json_decode($jsonContent, true);
        
        // Libérer immédiatement la mémoire du contenu brut
        unset($jsonContent);
        
        if ($analysisData === null) {
            $jsonError = json_last_error_msg();
            throw new Exception("Erreur lors du décodage JSON: $jsonError");
        }
        
        if (!isset($analysisData['statistics'])) {
            throw new Exception("Fichier d'analyse invalide (pas de statistiques)");
        }
        
        return $analysisData;
        
    } finally {
        // Restaurer la limite de mémoire originale
        if ($fileSize > 50 * 1024 * 1024) {
            ini_set('memory_limit', $originalMemoryLimit);
            error_log("💾 Restauration mémoire: " . ini_get('memory_limit'));
        }
    }
}

// Fonction pour rechercher des correspondances dans la base de données principale
function searchExistingSeries($cleanTitle, $mainPdo) {
    $results = [];
    
    // Recherche exacte
    $stmt = $mainPdo->prepare("
        SELECT id, name, type, tmdb_id, poster_url, 
               (SELECT COUNT(*) FROM seasons WHERE entertainment_id = entertainments.id) as season_count,
               (SELECT COUNT(*) FROM episodes WHERE entertainment_id = entertainments.id) as episode_count
        FROM entertainments 
                        WHERE name = ? AND type IN ('movie', 'tvshow')
        ORDER BY type DESC
    ");
    $stmt->execute([$cleanTitle]);
    $exactMatches = $stmt->fetchAll();
    
    if (count($exactMatches) > 0) {
        $results['exact'] = $exactMatches;
    }
    
    // Recherche approximative (LIKE)
    $stmt = $mainPdo->prepare("
        SELECT id, name, type, tmdb_id, poster_url,
               (SELECT COUNT(*) FROM seasons WHERE entertainment_id = entertainments.id) as season_count,
               (SELECT COUNT(*) FROM episodes WHERE entertainment_id = entertainments.id) as episode_count
        FROM entertainments 
                        WHERE name LIKE ? AND type IN ('movie', 'tvshow')
        ORDER BY type DESC
        LIMIT 10
    ");
    $stmt->execute(['%' . $cleanTitle . '%']);
    $approximateMatches = $stmt->fetchAll();
    
    if (count($approximateMatches) > 0) {
        $results['approximate'] = $approximateMatches;
    }
    
    return $results;
}

// Fonction de recherche avancée dans les données Python (session actuelle uniquement)
function searchInAnalysisData($searchTerm = '', $type = 'all', $yearFrom = null, $yearTo = null) {
    if (!isset($_SESSION['analysis_data'])) {
        error_log("❌ RECHERCHE: Aucune donnée analysis_data en session");
        return ['error' => 'Aucune donnée d\'analyse disponible'];
    }
    
    $data = $_SESSION['analysis_data'];
    
    // Debug détaillé de la structure des données
    error_log("🔍 RECHERCHE DEBUG:");
    error_log("  - Clés disponibles: " . implode(', ', array_keys($data)));
    error_log("  - Nombre de films: " . (isset($data['movies']) ? count($data['movies']) : 'NON DÉFINI'));
    error_log("  - Nombre de séries: " . (isset($data['series']) ? count($data['series']) : 'NON DÉFINI'));
    error_log("  - Terme recherche: '$searchTerm'");
    error_log("  - Type recherche: '$type'");
    
    // Vérifier la structure des données
    if (!isset($data['movies']) && !isset($data['series'])) {
        error_log("❌ RECHERCHE: Structure données invalide - clés movies/series manquantes");
        return ['error' => 'Structure de données invalide - clés movies/series manquantes'];
    }
    
    // Initialiser les tableaux s'ils n'existent pas
    if (!isset($data['movies'])) {
        $data['movies'] = [];
        error_log("⚠️ RECHERCHE: Aucune donnée films disponible");
    }
    if (!isset($data['series'])) {
        $data['series'] = [];
        error_log("⚠️ RECHERCHE: Aucune donnée séries disponible");
    }
    
    // Debug des premiers éléments pour vérifier la structure
    if (count($data['movies']) > 0) {
        $firstMovie = $data['movies'][0];
        error_log("🔍 Premier film - clés: " . implode(', ', array_keys($firstMovie)));
        error_log("🔍 Premier film - titre: " . ($firstMovie['title'] ?? 'NON DÉFINI'));
    }
    if (count($data['series']) > 0) {
        $firstSeries = $data['series'][0];
        error_log("🔍 Premier épisode - clés: " . implode(', ', array_keys($firstSeries)));
        error_log("🔍 Premier épisode - titre: " . ($firstSeries['title'] ?? 'NON DÉFINI'));
    }
    
    $results = [
        'movies' => [],
        'series' => [],
        'series_grouped' => [],
        'statistics' => [
            'total_found' => 0,
            'movies_found' => 0,
            'series_episodes_found' => 0,
            'unique_series_found' => 0
        ]
    ];
    
    $searchLower = strtolower(trim($searchTerm));
    
    // Recherche dans les films
    if ($type === 'all' || $type === 'movie') {
        foreach ($data['movies'] as $movie) {
            $match = true;
            
            // Filtrage par texte
            if (!empty($searchLower)) {
                $movieText = strtolower($movie['title'] . ' ' . $movie['clean_name'] . ' ' . $movie['group_title']);
                if (strpos($movieText, $searchLower) === false) {
                    $match = false;
                }
            }
            
            // Filtrage par année
            if ($match && ($yearFrom !== null || $yearTo !== null)) {
                if ($movie['year'] === null) {
                    $match = false;
                } else {
                    if ($yearFrom !== null && $movie['year'] < $yearFrom) $match = false;
                    if ($yearTo !== null && $movie['year'] > $yearTo) $match = false;
                }
            }
            
            if ($match) {
                $results['movies'][] = $movie;
                $results['statistics']['movies_found']++;
            }
        }
    }
    
    // Recherche dans les séries
    if ($type === 'all' || $type === 'series') {
        $seriesTemp = [];
        
        foreach ($data['series'] as $episode) {
            $match = true;
            
            // Filtrage par texte
            if (!empty($searchLower)) {
                $episodeText = strtolower($episode['title'] . ' ' . $episode['clean_name'] . ' ' . $episode['group_title']);
                if (strpos($episodeText, $searchLower) === false) {
                    $match = false;
                }
            }
            
            if ($match) {
                $results['series'][] = $episode;
                $results['statistics']['series_episodes_found']++;
                
                // Grouper par nom de série
                $seriesName = $episode['clean_name'];
                if (!isset($seriesTemp[$seriesName])) {
                    $seriesTemp[$seriesName] = [
                        'name' => $seriesName,
                        'episodes' => [],
                        'seasons' => [],
                        'total_episodes' => 0,
                        'group_title' => $episode['group_title'],
                        'sample_title' => $episode['title']
                    ];
                }
                
                $seriesTemp[$seriesName]['episodes'][] = $episode;
                $seriesTemp[$seriesName]['total_episodes']++;
                
                // Grouper par saison
                $seasonNum = $episode['season'];
                if (!isset($seriesTemp[$seriesName]['seasons'][$seasonNum])) {
                    $seriesTemp[$seriesName]['seasons'][$seasonNum] = [
                        'season_number' => $seasonNum,
                        'episodes' => [],
                        'episode_count' => 0
                    ];
                }
                
                $seriesTemp[$seriesName]['seasons'][$seasonNum]['episodes'][] = $episode;
                $seriesTemp[$seriesName]['seasons'][$seasonNum]['episode_count']++;
            }
        }
        
        // Finaliser les données de séries groupées
        foreach ($seriesTemp as $seriesName => $seriesData) {
            // Trier les saisons par numéro
            ksort($seriesData['seasons']);
            
            $seriesData['seasons_count'] = count($seriesData['seasons']);
            $seriesData['seasons_list'] = array_keys($seriesData['seasons']);
            
            $results['series_grouped'][] = $seriesData;
            $results['statistics']['unique_series_found']++;
        }
        
        // Trier par nom de série
        usort($results['series_grouped'], function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
    }
    
    $results['statistics']['total_found'] = $results['statistics']['movies_found'] + $results['statistics']['series_episodes_found'];
    
    // Debug final des résultats
    error_log("📊 RECHERCHE TERMINÉE:");
    error_log("  - Films trouvés: " . $results['statistics']['movies_found']);
    error_log("  - Épisodes trouvés: " . $results['statistics']['series_episodes_found']);
    error_log("  - Séries uniques: " . $results['statistics']['unique_series_found']);
    error_log("  - Total trouvé: " . $results['statistics']['total_found']);
    
    return $results;
}

// Fonction de recherche avancée dans les données Python avec historique
function searchInAnalysisDataWithHistory($searchTerm = '', $type = 'all', $yearFrom = null, $yearTo = null, $searchInHistory = false, $historyFiles = []) {
    $allResults = [
        'movies' => [],
        'series' => [],
        'series_grouped' => [],
        'statistics' => [
            'total_found' => 0,
            'movies_found' => 0,
            'series_episodes_found' => 0,
            'unique_series_found' => 0
        ],
        'sources' => []
    ];
    
    $dataSources = [];
    
    // Recherche dans la session actuelle
    if (isset($_SESSION['analysis_data'])) {
        $dataSources[] = [
            'data' => $_SESSION['analysis_data'],
            'source_name' => 'Analyse actuelle',
            'source_date' => date('Y-m-d H:i:s')
        ];
    }
    
    // Recherche dans l'historique si demandé
    if ($searchInHistory) {
        foreach ($historyFiles as $historyFile) {
            try {
                $historyData = loadAnalysisFromHistory($historyFile);
                $dataSources[] = [
                    'data' => $historyData,
                    'source_name' => 'Historique: ' . (isset($historyData['metadata']['source_file']) ? 
                        $historyData['metadata']['source_file'] : basename($historyFile)),
                    'source_date' => isset($historyData['metadata']['analysis_date']) ? 
                        $historyData['metadata']['analysis_date'] : date('Y-m-d H:i:s', filemtime($historyFile))
                ];
            } catch (Exception $e) {
                error_log("⚠️ Erreur chargement historique $historyFile: " . $e->getMessage());
            }
        }
    }
    
    if (empty($dataSources)) {
        return ['error' => 'Aucune donnée d\'analyse disponible'];
    }
    
    $searchLower = strtolower(trim($searchTerm));
    $seriesTemp = [];
    
    // Rechercher dans toutes les sources de données
    foreach ($dataSources as $source) {
        $data = $source['data'];
        
        // Recherche dans les films
        if ($type === 'all' || $type === 'movie') {
            foreach ($data['movies'] as $movie) {
                $match = true;
                
                // Filtrage par texte
                if (!empty($searchLower)) {
                    $movieText = strtolower($movie['title'] . ' ' . $movie['clean_name'] . ' ' . $movie['group_title']);
                    if (strpos($movieText, $searchLower) === false) {
                        $match = false;
                    }
                }
                
                // Filtrage par année
                if ($match && ($yearFrom !== null || $yearTo !== null)) {
                    if ($movie['year'] === null) {
                        $match = false;
                    } else {
                        if ($yearFrom !== null && $movie['year'] < $yearFrom) $match = false;
                        if ($yearTo !== null && $movie['year'] > $yearTo) $match = false;
                    }
                }
                
                if ($match) {
                    $movie['_source'] = $source['source_name'];
                    $movie['_source_date'] = $source['source_date'];
                    $allResults['movies'][] = $movie;
                    $allResults['statistics']['movies_found']++;
                }
            }
        }
        
        // Recherche dans les séries
        if ($type === 'all' || $type === 'series') {
            foreach ($data['series'] as $episode) {
                $match = true;
                
                // Filtrage par texte
                if (!empty($searchLower)) {
                    $episodeText = strtolower($episode['title'] . ' ' . $episode['clean_name'] . ' ' . $episode['group_title']);
                    if (strpos($episodeText, $searchLower) === false) {
                        $match = false;
                    }
                }
                
                if ($match) {
                    $episode['_source'] = $source['source_name'];
                    $episode['_source_date'] = $source['source_date'];
                    $allResults['series'][] = $episode;
                    $allResults['statistics']['series_episodes_found']++;
                    
                    // Grouper par nom de série avec source
                    $seriesKey = $episode['clean_name'] . '_' . $source['source_name'];
                    if (!isset($seriesTemp[$seriesKey])) {
                        $seriesTemp[$seriesKey] = [
                            'name' => $episode['clean_name'],
                            'episodes' => [],
                            'seasons' => [],
                            'total_episodes' => 0,
                            'group_title' => $episode['group_title'],
                            'sample_title' => $episode['title'],
                            '_source' => $source['source_name'],
                            '_source_date' => $source['source_date']
                        ];
                    }
                    
                    $seriesTemp[$seriesKey]['episodes'][] = $episode;
                    $seriesTemp[$seriesKey]['total_episodes']++;
                    
                    // Grouper par saison
                    $seasonNum = $episode['season'];
                    if (!isset($seriesTemp[$seriesKey]['seasons'][$seasonNum])) {
                        $seriesTemp[$seriesKey]['seasons'][$seasonNum] = [
                            'season_number' => $seasonNum,
                            'episodes' => [],
                            'episode_count' => 0
                        ];
                    }
                    
                    $seriesTemp[$seriesKey]['seasons'][$seasonNum]['episodes'][] = $episode;
                    $seriesTemp[$seriesKey]['seasons'][$seasonNum]['episode_count']++;
                }
            }
        }
        
        // Ajouter la source aux statistiques
        $allResults['sources'][] = $source['source_name'];
    }
    
    // Finaliser les données de séries groupées
    foreach ($seriesTemp as $seriesKey => $seriesData) {
        // Trier les saisons par numéro
        ksort($seriesData['seasons']);
        
        $seriesData['seasons_count'] = count($seriesData['seasons']);
        $seriesData['seasons_list'] = array_keys($seriesData['seasons']);
        
        $allResults['series_grouped'][] = $seriesData;
        $allResults['statistics']['unique_series_found']++;
    }
    
    // Trier par nom de série
    usort($allResults['series_grouped'], function($a, $b) {
        return strcmp($a['name'], $b['name']);
    });
    
    $allResults['statistics']['total_found'] = $allResults['statistics']['movies_found'] + $allResults['statistics']['series_episodes_found'];
    
    return $allResults;
}

// Traitement des actions POST
$action = $_POST['action'] ?? '';
$message = '';
$error = '';
$downloadResult = null;
$parsedItems = [];
$selectedSource = $_POST['source'] ?? 'iptv1';

// Nouvelle action pour charger une analyse de l'historique
if ($action === 'load_history') {
    $historyFile = $_POST['history_file'] ?? '';
    
    try {
        if (empty($historyFile)) {
            throw new Exception("Aucun fichier d'historique sélectionné");
        }
        
        if (!file_exists($historyFile)) {
            throw new Exception("Fichier d'historique non trouvé: $historyFile");
        }
        
        // Charger l'analyse depuis l'historique
        $analysisData = loadAnalysisFromHistory($historyFile);
        
        // Convertir les données Python en format compatible avec l'affichage existant
        $parsedItems = [];
        
        // Ajouter les films
        foreach ($analysisData['movies'] as $movie) {
            $parsedItems[] = [
                'title' => $movie['title'],
                'clean_title' => $movie['clean_name'],
                'type' => 'movie',
                'year' => $movie['year'],
                'season' => null,
                'episode' => null,
                'tvg_name' => $movie['tvg_name'],
                'group_title' => $movie['group_title'],
                'url' => $movie['url'] ?? ''
            ];
        }
        
        // Ajouter les séries
        foreach ($analysisData['series'] as $series) {
            $parsedItems[] = [
                'title' => $series['title'],
                'clean_title' => $series['clean_name'],
                'type' => 'tvshow',
                'year' => null,
                'season' => $series['season'],
                'episode' => $series['episode'],
                'tvg_name' => $series['tvg_name'],
                'group_title' => $series['group_title'],
                'url' => $series['url'] ?? ''
            ];
        }
        
        // Gestion intelligente de la mémoire (cohérence avec flux direct)
        $dataSize = strlen(serialize($analysisData));
        $sizeMB = round($dataSize/1024/1024, 1);
        
        // Si les données sont vraiment énormes (>50MB), on optimise pour éviter les crashes
        if ($dataSize > 50 * 1024 * 1024) {
            error_log("⚠️ Historique volumineux ($sizeMB MB), optimisation nécessaire");
            $optimizedData = optimizeDataForSession($analysisData);
            $_SESSION['analysis_data'] = $optimizedData;
            $message .= "<br>⚠️ <span class='badge bg-warning'>Historique optimisé</span> Dataset très volumineux ($sizeMB MB) - version allégée chargée";
        } else {
            // Stocker les données COMPLÈTES pour que la recherche fonctionne
            $_SESSION['analysis_data'] = $analysisData;
            error_log("✅ Historique complet stocké en session ($sizeMB MB)");
        }
        
        // Debug du stockage en session (load_history)
        error_log("💾 STOCKAGE SESSION après load_history:");
        error_log("  - Taille dataset: $sizeMB MB");
        error_log("  - Type stockage: " . ($dataSize > 50 * 1024 * 1024 ? 'OPTIMISÉ' : 'COMPLET'));
        error_log("  - SESSION analysis_data existe: " . (isset($_SESSION['analysis_data']) ? 'OUI' : 'NON'));
        if (isset($_SESSION['analysis_data'])) {
            error_log("  - Films en session: " . (isset($_SESSION['analysis_data']['movies']) ? count($_SESSION['analysis_data']['movies']) : 'NON DÉFINI'));
            error_log("  - Séries en session: " . (isset($_SESSION['analysis_data']['series']) ? count($_SESSION['analysis_data']['series']) : 'NON DÉFINI'));
        }
        
        // Limiter aussi parsedItems si trop volumineux
        if (count($parsedItems) > 3000) {
            $parsedItems = array_slice($parsedItems, 0, 3000);
            error_log("⚠️ Liste d'éléments historique tronquée à 3000 items pour session");
        }
        $_SESSION['parsed_m3u_items'] = $parsedItems;
        $_SESSION['m3u_source'] = 'history';
        
        $message = "Analyse historique chargée avec succès: " . 
                   $analysisData['statistics']['total_movies'] . " films, " . 
                   $analysisData['statistics']['total_series_episodes'] . " épisodes, " . 
                   $analysisData['statistics']['unique_series'] . " séries uniques";
        
        if (isset($analysisData['metadata']['analysis_date'])) {
            $message .= " (analysé le " . $analysisData['metadata']['analysis_date'] . ")";
        }
        
    } catch (Exception $e) {
        $error = "Erreur lors du chargement de l'historique: " . $e->getMessage();
        error_log("❌ ERREUR LOAD HISTORY: " . $e->getMessage());
    }
}

// Action pour supprimer un historique spécifique
if ($action === 'delete_history') {
    $historyFile = $_POST['history_file'] ?? '';
    
    try {
        deleteAnalysisHistory($historyFile);
        $message = "Historique supprimé avec succès: " . basename($historyFile);
    } catch (Exception $e) {
        $error = "Erreur lors de la suppression: " . $e->getMessage();
        error_log("❌ ERREUR DELETE HISTORY: " . $e->getMessage());
    }
}

// Action pour supprimer tout l'historique
if ($action === 'delete_all_history') {
    try {
        $deletedCount = deleteAllAnalysisHistory();
        if ($deletedCount > 0) {
            $message = "Tout l'historique a été supprimé avec succès ($deletedCount fichier" . ($deletedCount > 1 ? 's' : '') . " supprimé" . ($deletedCount > 1 ? 's' : '') . ")";
        } else {
            $message = "Aucun historique à supprimer";
        }
    } catch (Exception $e) {
        $error = "Erreur lors de la suppression de l'historique: " . $e->getMessage();
        error_log("❌ ERREUR DELETE ALL HISTORY: " . $e->getMessage());
    }
}

// Action pour tester le parsing des titres
if ($action === 'test_parsing') {
    $testTitles = [
        'FR - Mulan (2020) 4K',
        'FR - Mulan (1998) HD',
        'EN - Breaking Bad S01E01 VOSTFR HD',
        'FR - Breaking Bad S01 E07 FRENCH HD',
        'Avatar (2009) BLURAY FHD',
        'The Walking Dead S10E16 FRENCH',
        'DE - Matrix (1999) WEB-DL',
        'US - Matrix (2021) UHD',
        'Game of Thrones 1x01 VOSTFR 720P',
        'FR - Avengers Endgame (2019) CAM',
        'Better Call Saul Saison 5 Episode 10 HD'
    ];
    
    $parseResults = [];
    foreach ($testTitles as $title) {
        $parseResults[] = [
            'original' => $title,
            'parsed' => parseIptvTitle($title)
        ];
    }
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'results' => $parseResults
    ]);
    exit;
}

// Action pour insérer les données sélectionnées en base
if ($action === 'insert_selected') {
    try {
        $selectedItems = json_decode($_POST['selected_items'] ?? '[]', true);
        $targetDatabase = $_POST['target_database'] ?? 'iptv1';
        
        if (empty($selectedItems)) {
            throw new Exception("Aucun élément sélectionné pour l'insertion");
        }
        
        error_log("🔄 INSERTION: " . count($selectedItems) . " éléments vers $targetDatabase");
        
        $insertResults = insertSelectedItems($selectedItems, $targetDatabase, $config);
        
        $message = "Insertion terminée avec succès !<br>";
        $message .= "• Films insérés: " . $insertResults['statistics']['movies_inserted'] . "<br>";
        $message .= "• Épisodes insérés: " . $insertResults['statistics']['episodes_inserted'] . "<br>";
        $message .= "• Déjà existants: " . $insertResults['statistics']['already_exists'] . "<br>";
        
        if ($insertResults['statistics']['errors_count'] > 0) {
            $message .= "• Erreurs: " . $insertResults['statistics']['errors_count'] . "<br>";
            $message .= "<div class='mt-2'><strong>Détails des erreurs:</strong><br>";
            foreach ($insertResults['errors'] as $error) {
                $message .= "- " . htmlspecialchars($error) . "<br>";
            }
            $message .= "</div>";
        }
        
        // Stocker les résultats en session pour l'affichage
        $_SESSION['last_insert_results'] = $insertResults;
        
    } catch (Exception $e) {
        $error = "Erreur lors de l'insertion: " . $e->getMessage();
        error_log("❌ ERREUR INSERTION: " . $e->getMessage());
    }
}

if ($action === 'download_m3u') {
    try {
        // Gérer l'option test
        if ($selectedSource === 'test') {
            // Fichier de test local
            $m3uUrl = __DIR__ . '/test_playlist.m3u';
            $sourceKey = 'test_file';
        } else {
            $sourceKey = $selectedSource === 'iptv2' ? 'update_iptv2_url' : 'update_iptv1_url';
            $m3uUrl = $config[$sourceKey];
        }
        
        // Debug des URLs configurées
        error_log("🔍 Debug URLs configurées:");
        error_log("   IPTV1: " . ($config['update_iptv1_url'] ?? 'NON CONFIGURÉ'));
        error_log("   IPTV2: " . ($config['update_iptv2_url'] ?? 'NON CONFIGURÉ'));
        error_log("   Source sélectionnée: $selectedSource -> $sourceKey");
        error_log("   URL utilisée: " . ($m3uUrl ?? 'VIDE'));
        
        if (empty($m3uUrl)) {
            throw new Exception("URL M3U non configurée pour la source $selectedSource. Veuillez configurer $sourceKey dans votre fichier .env");
        }
        
        // Nouveau workflow : télécharger -> stocker -> analyser avec Python
        $dataDir = __DIR__ . '/data_download/';
        
        // Créer le répertoire de stockage s'il n'existe pas avec gestion d'erreurs améliorée
        if (!is_dir($dataDir)) {
            // Essayer de créer avec permissions larges pour les serveurs partagés
            if (!mkdir($dataDir, 0777, true)) {
                // Si échec, essayer avec le répertoire temp comme fallback
                $dataDir = sys_get_temp_dir() . '/m3u_analysis/';
                if (!is_dir($dataDir) && !mkdir($dataDir, 0777, true)) {
                    throw new Exception("Impossible de créer le répertoire de stockage: $dataDir");
                }
                error_log("⚠️ Utilisation du répertoire temporaire: $dataDir");
            } else {
                error_log("📁 Répertoire créé: $dataDir");
            }
        }
        
        // Vérifier les permissions d'écriture
        if (!is_writable($dataDir)) {
            // Essayer de corriger les permissions
            if (!chmod($dataDir, 0777)) {
                // Si échec, utiliser le répertoire temporaire système
                $dataDir = sys_get_temp_dir() . '/m3u_analysis/';
                if (!is_dir($dataDir)) {
                    mkdir($dataDir, 0777, true);
                }
                error_log("⚠️ Permissions insuffisantes, utilisation du répertoire temp: $dataDir");
            }
        }
        
        $timestamp = date('YmdHis');
        $storedFile = $dataDir . "playlist_{$timestamp}.m3u";
        
        if ($selectedSource === 'test') {
            // Pour les tests, copier le fichier local
            if (!file_exists($m3uUrl)) {
                throw new Exception("Fichier de test non trouvé: $m3uUrl");
            }
            
            if (!copy($m3uUrl, $storedFile)) {
                // Gestion d'erreur améliorée avec fallback
                $error = error_get_last();
                error_log("❌ Erreur copy(): " . ($error['message'] ?? 'Erreur inconnue'));
                
                // Essayer avec un fichier temporaire
                $tempFile = tempnam(sys_get_temp_dir(), 'playlist_');
                if ($tempFile && copy($m3uUrl, $tempFile)) {
                    $storedFile = $tempFile;
                    error_log("✅ Fallback réussi vers fichier temporaire: $storedFile");
        } else {
                    throw new Exception("Impossible de copier le fichier de test. Vérifiez les permissions du dossier: " . dirname($storedFile));
                }
            }
            
            // Compter les lignes de manière efficace
            $lineCount = 0;
            $handle = fopen($storedFile, 'r');
            if ($handle) {
                while (($line = fgets($handle)) !== false) {
                    $lineCount++;
                }
                fclose($handle);
            }
            
            $downloadResult = [
                'file' => $storedFile,
                'size' => filesize($storedFile),
                'lines' => $lineCount,
                'url' => $m3uUrl,
                'stored_file' => $storedFile
            ];
            $message = "Fichier M3U de test copié avec succès: {$downloadResult['size']} octets, {$downloadResult['lines']} lignes";
            
        } else {
            // Pour les URLs, télécharger puis stocker
            $downloadResult = downloadM3U($m3uUrl);
            
            // Copier vers le dossier de stockage avec gestion d'erreur
            if (!copy($downloadResult['file'], $storedFile)) {
                // Gestion d'erreur améliorée
                $error = error_get_last();
                error_log("❌ Erreur copy() vers $storedFile: " . ($error['message'] ?? 'Erreur inconnue'));
                
                // Utiliser directement le fichier temporaire téléchargé
                $storedFile = $downloadResult['file'];
                error_log("⚠️ Utilisation du fichier temporaire original: $storedFile");
            } else {
                // Nettoyer le fichier temporaire original
            unlink($downloadResult['file']);
        }
        
            // Mettre à jour les informations
            $downloadResult['file'] = $storedFile;
            $downloadResult['stored_file'] = $storedFile;
            
            $message = "Fichier M3U téléchargé et stocké avec succès: {$downloadResult['size']} octets, {$downloadResult['lines']} lignes";
        }
        
        // Augmenter la mémoire PHP avant l'analyse pour les gros fichiers
        $originalMemoryLimit = ini_get('memory_limit');
        ini_set('memory_limit', '512M');
        error_log("🧠 Augmentation mémoire PHP globale: $originalMemoryLimit -> 512M");
        
        try {
            // Analyser avec Python
            $analysisData = analyzeM3UWithPython($storedFile, $selectedSource);
        } finally {
            // Restaurer la limite de mémoire originale
            ini_set('memory_limit', $originalMemoryLimit);
            error_log("🧠 Restauration mémoire PHP globale: " . ini_get('memory_limit'));
        }
        
        // Convertir les données Python en format compatible avec l'affichage existant
        $parsedItems = [];
        
        // Ajouter les films
        foreach ($analysisData['movies'] as $movie) {
            $parsedItems[] = [
                'title' => $movie['title'],
                'clean_title' => $movie['clean_name'],
                'type' => 'movie',
                'year' => $movie['year'],
                'season' => null,
                'episode' => null,
                'tvg_name' => $movie['tvg_name'],
                'group_title' => $movie['group_title'],
                'url' => $movie['url'] ?? ''
            ];
        }
        
        // Ajouter les séries
        foreach ($analysisData['series'] as $series) {
            $parsedItems[] = [
                'title' => $series['title'],
                'clean_title' => $series['clean_name'],
                'type' => 'tvshow',
                'year' => null,
                'season' => $series['season'],
                'episode' => $series['episode'],
                'tvg_name' => $series['tvg_name'],
                'group_title' => $series['group_title'],
                'url' => $series['url'] ?? ''
            ];
        }
        
        $message .= "<br>🐍 Analyse Python terminée: " . $analysisData['statistics']['total_movies'] . " films, " . $analysisData['statistics']['total_series_episodes'] . " épisodes, " . $analysisData['statistics']['unique_series'] . " séries uniques";
        
        // Debug détaillé des données avant stockage
        error_log("🔍 DEBUG AVANT STOCKAGE:");
        error_log("  - analysisData existe: " . (isset($analysisData) ? 'OUI' : 'NON'));
        if (isset($analysisData)) {
            error_log("  - Clés analysisData: " . implode(', ', array_keys($analysisData)));
            error_log("  - analysisData movies: " . (isset($analysisData['movies']) ? count($analysisData['movies']) : 'NON DÉFINI'));
            error_log("  - analysisData series: " . (isset($analysisData['series']) ? count($analysisData['series']) : 'NON DÉFINI'));
            error_log("  - analysisData statistics: " . (isset($analysisData['statistics']) ? 'OUI' : 'NON'));
        }
        
        // Gestion intelligente de la mémoire : données complètes par défaut, optimisation si nécessaire
        $dataSize = strlen(serialize($analysisData));
        $sizeMB = round($dataSize/1024/1024, 1);
        
        // Si les données sont vraiment énormes (>50MB), on optimise pour éviter les crashes
        if ($dataSize > 50 * 1024 * 1024) {
            error_log("⚠️ Données très volumineuses ($sizeMB MB), optimisation nécessaire");
            $optimizedData = optimizeDataForSession($analysisData);
            $_SESSION['analysis_data'] = $optimizedData;
            $message .= "<br>⚠️ <span class='badge bg-warning'>Données optimisées</span> Dataset très volumineux ($sizeMB MB) - version allégée chargée en session";
        } else {
            // Stocker les données COMPLÈTES pour que la recherche fonctionne
            $_SESSION['analysis_data'] = $analysisData;
            error_log("✅ Données complètes stockées en session ($sizeMB MB)");
        }
        
        // Debug détaillé après stockage
        error_log("🔍 DEBUG APRÈS STOCKAGE:");
        error_log("  - Taille dataset: $sizeMB MB");
        error_log("  - Type stockage: " . ($dataSize > 50 * 1024 * 1024 ? 'OPTIMISÉ' : 'COMPLET'));
        error_log("  - SESSION analysis_data existe: " . (isset($_SESSION['analysis_data']) ? 'OUI' : 'NON'));
        if (isset($_SESSION['analysis_data'])) {
            error_log("  - Films en session: " . (isset($_SESSION['analysis_data']['movies']) ? count($_SESSION['analysis_data']['movies']) : 'NON DÉFINI'));
            error_log("  - Séries en session: " . (isset($_SESSION['analysis_data']['series']) ? count($_SESSION['analysis_data']['series']) : 'NON DÉFINI'));
        }
        
        // Limiter aussi parsedItems si trop volumineux
        if (count($parsedItems) > 3000) {
            $parsedItems = array_slice($parsedItems, 0, 3000);
            error_log("⚠️ Liste d'éléments tronquée à 3000 items pour session");
        }
        $_SESSION['parsed_m3u_items'] = $parsedItems;
        $_SESSION['m3u_source'] = $selectedSource;
        
        // Debug détaillé du stockage en session (download_m3u)
        error_log("💾 STOCKAGE SESSION après download_m3u:");
        error_log("  - Données analysis_data stockées: " . (isset($_SESSION['analysis_data']) ? 'OUI' : 'NON'));
        if (isset($_SESSION['analysis_data'])) {
            error_log("  - Clés stockées: " . implode(', ', array_keys($_SESSION['analysis_data'])));
            error_log("  - Films stockés: " . (isset($_SESSION['analysis_data']['movies']) ? count($_SESSION['analysis_data']['movies']) : 'NON DÉFINI'));
            error_log("  - Séries stockées: " . (isset($_SESSION['analysis_data']['series']) ? count($_SESSION['analysis_data']['series']) : 'NON DÉFINI'));
        }
        error_log("  - Session ID: " . session_id());
        error_log("  - Total parsedItems: " . count($parsedItems));

        
    } catch (Exception $e) {
        $error = "Erreur lors du téléchargement: " . $e->getMessage();
        error_log("❌ ERREUR DOWNLOAD: " . $e->getMessage());
        error_log("❌ TRACE: " . $e->getTraceAsString());
    } catch (Error $e) {
        $error = "Erreur fatale PHP: " . $e->getMessage();
        error_log("💥 ERREUR FATALE: " . $e->getMessage());
        error_log("💥 TRACE: " . $e->getTraceAsString());
    }
}

if ($action === 'analyze_items') {
    if (isset($_SESSION['parsed_m3u_items'])) {
        $parsedItems = $_SESSION['parsed_m3u_items'];
        
        // Connecter à la base principale pour rechercher des correspondances
        try {
            $mainPdo = getConnection($config['main_db']);
            
            // Analyser chaque item
            foreach ($parsedItems as &$item) {
                if ($item['type'] === 'tvshow' && !empty($item['clean_title'])) {
                    $item['existing_matches'] = searchExistingSeries($item['clean_title'], $mainPdo);
                }
            }
            
        } catch (Exception $e) {
            $error = "Erreur lors de l'analyse: " . $e->getMessage();
        }
    }
}

if ($action === 'advanced_search') {
    $searchTerm = $_POST['search_term'] ?? '';
    $searchType = $_POST['search_type'] ?? 'all';
    $yearFrom = !empty($_POST['year_from']) ? intval($_POST['year_from']) : null;
    $yearTo = !empty($_POST['year_to']) ? intval($_POST['year_to']) : null;
    
    error_log("🔍 ADVANCED_SEARCH APPELÉ:");
    error_log("  - Terme: '$searchTerm'");
    error_log("  - Type: '$searchType'");
    error_log("  - Session analysis_data existe: " . (isset($_SESSION['analysis_data']) ? 'OUI' : 'NON'));
    
    // Nouvelle option pour rechercher dans l'historique
    $searchInHistory = isset($_POST['search_in_history']) && $_POST['search_in_history'] === '1';
    $selectedHistoryFiles = $_POST['history_files'] ?? [];
    
    if ($searchInHistory && !empty($selectedHistoryFiles)) {
        error_log("  - Recherche avec historique: " . count($selectedHistoryFiles) . " fichiers");
        $searchResults = searchInAnalysisDataWithHistory($searchTerm, $searchType, $yearFrom, $yearTo, true, $selectedHistoryFiles);
    } else {
        error_log("  - Recherche session actuelle seulement");
        $searchResults = searchInAnalysisData($searchTerm, $searchType, $yearFrom, $yearTo);
    }
}

// Debug session (session déjà démarrée au début du script)
error_log("🚀 APRÈS ACTIONS - session ID: " . session_id());
error_log("🚀 APRÈS ACTIONS - analysis_data existe: " . (isset($_SESSION['analysis_data']) ? 'OUI' : 'NON'));

// Récupérer les données de session si elles existent, sinon charger le dernier historique
if (empty($parsedItems)) {
    if (isset($_SESSION['parsed_m3u_items'])) {
        $parsedItems = $_SESSION['parsed_m3u_items'];
        $selectedSource = $_SESSION['m3u_source'] ?? 'iptv1';
        error_log("🔄 RÉCUPÉRATION - Données récupérées de session: " . count($parsedItems) . " items");
    } else {
        error_log("🔄 RÉCUPÉRATION - Aucune donnée en session");
        
        // Auto-charger le dernier historique disponible pour améliorer l'UX
        if (!isset($_SESSION['analysis_data'])) {
            $analysisHistory = getAnalysisHistory();
            if (!empty($analysisHistory)) {
                $latestHistory = reset($analysisHistory); // Premier = plus récent
                try {
                    $analysisData = loadAnalysisFromHistory($latestHistory['file']);
                    
                    // Appliquer la même logique de stockage que pour les autres flux
                    $dataSize = strlen(serialize($analysisData));
                    $sizeMB = round($dataSize/1024/1024, 1);
                    
                    if ($dataSize > 50 * 1024 * 1024) {
                        $optimizedData = optimizeDataForSession($analysisData);
                        $_SESSION['analysis_data'] = $optimizedData;
                        $message = "📁 <span class='badge bg-info'>Auto-chargé</span> Dernière analyse (" . $latestHistory['metadata']['source'] . ") - <span class='badge bg-warning'>Version optimisée</span>";
                    } else {
                        $_SESSION['analysis_data'] = $analysisData;
                        $message = "📁 <span class='badge bg-info'>Auto-chargé</span> Dernière analyse: " . $latestHistory['metadata']['source'] . " ($sizeMB MB)";
                    }
                    
                    error_log("🔄 AUTO-CHARGEMENT - Dernière analyse chargée: " . $latestHistory['metadata']['source']);
                } catch (Exception $e) {
                    error_log("❌ AUTO-CHARGEMENT - Erreur: " . $e->getMessage());
                }
            }
        }
    }
}

// Nettoyer automatiquement l'historique pour éviter l'accumulation
cleanupAnalysisHistory();

// Récupérer l'historique des analyses
$analysisHistory = getAnalysisHistory();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Importation M3U - VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .series-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .series-item.tv-show {
            border-left: 4px solid #0d6efd;
        }
        .series-item.movie {
            border-left: 4px solid #dc3545;
        }
        .match-badge {
            font-size: 0.8em;
        }
        .action-buttons {
            margin-top: 10px;
        }
        .existing-match {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 8px;
            margin: 5px 0;
        }
        
        /* Styles pour la recherche avancée */
        .search-card {
            border: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
        }
        .search-card .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }
        .movie-card, .series-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .movie-card:hover, .series-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .season-box {
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .season-box:hover {
            background-color: #e9ecef !important;
            transform: scale(1.02);
        }
        .search-form {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .tab-content {
            min-height: 300px;
        }
        .episode-list {
            max-height: 200px;
            overflow-y: auto;
        }
        .badge-film {
            background: linear-gradient(45deg, #dc3545, #fd7e14) !important;
        }
        .badge-series {
            background: linear-gradient(45deg, #007bff, #17a2b8) !important;
        }
        .search-stats {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 8px;
        }
        
        /* Styles pour l'interface d'insertion */
        .item-checkbox {
            transform: scale(1.2);
        }
        .movie-card .form-check, .series-card .form-check {
            margin-left: 10px;
        }
        .episode-list .form-check {
            min-width: 20px;
        }
        .episode-list .d-flex {
            transition: background-color 0.2s ease;
        }
        .episode-list .d-flex:hover {
            background-color: #f8f9fa;
        }
        .form-check-sm .form-check-input {
            transform: scale(0.9);
        }
        #selection_summary {
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .border-success {
            border-width: 2px !important;
        }
        .card-header.bg-success {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-download"></i> Importation M3U</h1>
                    <div>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Retour
                        </a>
                    </div>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                        
                        <?php if (strpos($error, 'URL M3U non configurée') !== false): ?>
                        <hr>
                        <h6><i class="bi bi-info-circle"></i> Configuration requise</h6>
                        <p class="mb-1">Pour utiliser l'importation M3U, ajoutez ces variables dans votre fichier <code>.env</code> :</p>
                        <pre class="bg-dark text-light p-2 rounded mt-2"><code>UPDATE_IPTV1_URL="http://votre-serveur.com/get.php?username=XXX&password=YYY&type=m3u_plus"
UPDATE_IPTV2_URL="http://votre-autre-serveur.com/playlist.m3u"</code></pre>
                        <p class="small mt-2 mb-0">💡 En attendant, vous pouvez tester avec l'option "Test (URL d'exemple)" pour vérifier que le système fonctionne.</p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Étape 1: Téléchargement du fichier M3U -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-1-circle"></i> Téléchargement du fichier M3U</h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="source" class="form-label">Source IPTV</label>
                                    <select name="source" id="source" class="form-select">
                                        <option value="iptv1" <?php echo $selectedSource === 'iptv1' ? 'selected' : ''; ?>>
                                            IPTV 1 (<?php echo !empty($config['update_iptv1_url']) ? 'Configuré' : 'Non configuré'; ?>)
                                        </option>
                                        <option value="iptv2" <?php echo $selectedSource === 'iptv2' ? 'selected' : ''; ?>>
                                            IPTV 2 (<?php echo !empty($config['update_iptv2_url']) ? 'Configuré' : 'Non configuré'; ?>)
                                        </option>
                                        <option value="test" <?php echo $selectedSource === 'test' ? 'selected' : ''; ?>>
                                            Test (Fichier local - Démo)
                                        </option>
                                    </select>
                                    
                                    <!-- Info de debug -->
                                    <div class="mt-2">
                                        <small class="text-muted">

                                        <small class="text-muted">
                                            <strong>URLs configurées:</strong><br>
                                            IPTV1: <?php echo !empty($config['update_iptv1_url']) ? 
                                                htmlspecialchars(substr($config['update_iptv1_url'], 0, 50)) . '...' : 
                                                '<span class="text-danger">Non configuré</span>'; ?><br>
                                            IPTV2: <?php echo !empty($config['update_iptv2_url']) ? 
                                                htmlspecialchars(substr($config['update_iptv2_url'], 0, 50)) . '...' : 
                                                '<span class="text-danger">Non configuré</span>'; ?>
                                        </small><br>

                                            💡 <strong>En cas de problème :</strong> 
                                            <a href="logs/error.log" target="_blank" class="text-decoration-none">Voir les logs d'erreur</a>
                                            <br>💾 <strong>Mémoire optimisée :</strong> Limite <?php echo ini_get('memory_limit'); ?> 


        
                                            | <a href="fix_permissions.php" target="_blank" class="text-decoration-none">Diagnostic système</a>
                                        </small>
                                        <br>
                                        <br>
                                        <button type="submit" name="action" value="download_m3u" class="btn btn-primary w-100">
                                        <i class="bi bi-download"></i> Télécharger et analyser
                                    </button>
                                    <br>
                                    <br>
                                    <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" name="action" value="download_m3u" class="btn btn-primary w-100">
                                        <i class="bi bi-download"></i> Télécharger et analyser
                                    </button>
                                </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="alert alert-success">
                                        <i class="bi bi-python"></i>
                                        <strong>Workflow hybride :</strong> 
                                        <br>1. PHP télécharge et stocke le fichier
                                        <br>2. Python analyse le contenu (performance optimale)
                                        <br>3. PHP affiche les résultats JSON
                                        <br><small class="text-muted">Classification automatique : série si SXX EXX détecté, sinon film.</small>
                                        <br><small class="badge bg-warning text-dark mt-1">⏱️ Gros fichiers : jusqu'à 2-3 minutes</small>
                                        <br><small class="badge bg-info text-dark mt-1">🧠 Mémoire auto-ajustée 128M → 512M</small>
                                    </div>
                                </div>
                                
                                
                                
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Section Historique des analyses -->
                <?php if (!empty($analysisHistory)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-clock-history"></i> Historique des analyses (<?php echo count($analysisHistory); ?> analyses sauvegardées)</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">
                            <i class="bi bi-info-circle"></i> Vous pouvez consulter et rechercher dans vos analyses précédentes. 
                            Chaque analyse est automatiquement sauvegardée avec ses métadonnées.
                        </p>
                        
                        <form method="post" class="mb-3">
                            <input type="hidden" name="action" value="load_history">
                            <div class="row align-items-end">
                                <div class="col-md-6">
                                    <label for="history_file" class="form-label">Sélectionner une analyse à charger</label>
                                    <select name="history_file" id="history_file" class="form-select" required>
                                        <option value="">Choisir une analyse...</option>
                                        <?php foreach ($analysisHistory as $history): ?>
                                            <option value="<?php echo htmlspecialchars($history['file']); ?>">
                                                <?php 
                                                $dbBadge = '';
                                                if ($history['database_type'] === 'iptv1') {
                                                    $dbBadge = '[CHINA] ';
                                                } elseif ($history['database_type'] === 'iptv2') {
                                                    $dbBadge = '[MEGA] ';
                                                }
                                                ?>
                                                <?php echo $dbBadge; ?>📅 <?php echo $history['date']; ?> - 
                                                📁 <?php echo htmlspecialchars($history['source_file']); ?> 
                                                (🎬 <?php echo number_format($history['preview']['movies']); ?> films, 
                                                📺 <?php echo number_format($history['preview']['episodes']); ?> épisodes, 
                                                🏷️ <?php echo number_format($history['preview']['series']); ?> séries)
                                                - <?php echo round($history['size'] / 1024 / 1024, 1); ?>MB
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="btn-group w-100" role="group">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="bi bi-upload"></i> Charger
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteSelectedHistory()">
                                            <i class="bi bi-trash"></i> Supprimer
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" onclick="deleteAllHistory()">
                                            <i class="bi bi-trash-fill"></i> Tout supprimer
                                    </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        
                        <!-- Aperçu détaillé de l'historique -->
                        <div class="row">
                            <?php foreach (array_slice($analysisHistory, 0, 6) as $index => $history): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100 border-secondary">
                                    <div class="card-body p-3">
                                        <h6 class="card-title text-primary mb-2">
                                            <i class="bi bi-file-earmark-text"></i> 
                                            <?php echo htmlspecialchars($history['source_file']); ?>
                                            <?php if ($history['database_type'] === 'iptv1'): ?>
                                                <span class="badge bg-warning text-dark">CHINA</span>
                                            <?php elseif ($history['database_type'] === 'iptv2'): ?>
                                                <span class="badge bg-success">MEGA</span>
                                            <?php elseif ($history['database_type'] === 'legacy'): ?>
                                                <span class="badge bg-secondary">LEGACY</span>
                                            <?php endif; ?>
                                        </h6>
                                        
                                        <div class="small mb-2">
                                            <div class="text-muted">
                                                <i class="bi bi-calendar3"></i> <?php echo $history['date']; ?>
                                            </div>
                                            <div class="text-muted">
                                                <i class="bi bi-hdd"></i> <?php echo round($history['size'] / 1024 / 1024, 1); ?> MB
                    </div>
                </div>
                                        
                                        <div class="d-flex flex-wrap gap-1 mb-2">
                                            <span class="badge bg-danger"><?php echo number_format($history['preview']['movies']); ?> films</span>
                                            <span class="badge bg-primary"><?php echo number_format($history['preview']['episodes']); ?> épisodes</span>
                                            <span class="badge bg-info"><?php echo number_format($history['preview']['series']); ?> séries</span>
                                            <span class="badge bg-success"><?php echo number_format($history['preview']['seasons']); ?> saisons</span>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-secondary btn-sm" 
                                                    onclick="loadHistoryAnalysis('<?php echo htmlspecialchars($history['file']); ?>')">
                                                <i class="bi bi-eye"></i> Charger
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" 
                                                    onclick="deleteSpecificHistory('<?php echo htmlspecialchars($history['file']); ?>', '<?php echo htmlspecialchars($history['source_file']); ?>')">
                                                <i class="bi bi-trash"></i> Supprimer
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if (count($analysisHistory) > 6): ?>
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                ... et <?php echo count($analysisHistory) - 6; ?> autres analyses. 
                                Utilisez la liste déroulante ci-dessus pour accéder à toutes les analyses.
                            </small>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Étape 2: Résultats du parsing -->
                <?php if (!empty($parsedItems)): ?>
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <?php 
                        // Debug final : vérifier la cohérence des données
                        $hasAnalysisData = isset($_SESSION['analysis_data']) && isset($_SESSION['analysis_data']['statistics']);
                        $parsedCount = count($parsedItems);
                        
                        echo "<!-- SESSION: analysis_data=" . ($hasAnalysisData ? 'OUI' : 'NON') . ", items=" . $parsedCount . " -->";
                        
                        if ($hasAnalysisData) {
                            $pythonTotal = $_SESSION['analysis_data']['statistics']['total_movies'] + $_SESSION['analysis_data']['statistics']['total_series_episodes'];
                            echo "<!-- PYTHON: total=" . $pythonTotal . ", movies=" . $_SESSION['analysis_data']['statistics']['total_movies'] . " -->";
                            
                            // Vérifier si c'est une version allégée
                            if (isset($_SESSION['analysis_data']['note']) && strpos($_SESSION['analysis_data']['note'], 'Version allégée') !== false) {
                                echo "<!-- VERSION ALLÉGÉE DÉTECTÉE -->";
                            }
                        }
                        ?>
                        
                        <?php if (isset($_SESSION['analysis_data']) && isset($_SESSION['analysis_data']['statistics'])): ?>
                            <?php 
                            $pythonStats = $_SESSION['analysis_data']['statistics']; 
                            $isHistoryAnalysis = ($_SESSION['m3u_source'] ?? '') === 'history';
                            $historyDate = '';
                            if ($isHistoryAnalysis && isset($_SESSION['analysis_data']['metadata']['analysis_date'])) {
                                $historyDate = $_SESSION['analysis_data']['metadata']['analysis_date'];
                            }
                            ?>
                            <div>
                                <h5>
                                    <i class="bi bi-2-circle"></i> 
                                    <?php if ($isHistoryAnalysis): ?>
                                        <i class="bi bi-clock-history text-info"></i> Analyse historique 
                                        <?php if ($historyDate): ?>
                                            <small class="text-muted">(<?php echo $historyDate; ?>)</small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        Contenu analysé
                                    <?php endif; ?>
                                    (<?php echo number_format($pythonStats['total_movies'] + $pythonStats['total_series_episodes']); ?> éléments)
                                </h5>
                                <small class="text-muted">
                                    🎬 <?php echo number_format($pythonStats['total_movies']); ?> films • 
                                    📺 <?php echo number_format($pythonStats['total_series_episodes']); ?> épisodes • 
                                    🏷️ <?php echo number_format($pythonStats['unique_series']); ?> séries uniques • 
                                    📊 <?php echo number_format($pythonStats['total_seasons']); ?> saisons
                                    <?php if ($isHistoryAnalysis && isset($_SESSION['analysis_data']['metadata']['source_file'])): ?>
                                        <br>📁 Source: <?php echo htmlspecialchars($_SESSION['analysis_data']['metadata']['source_file']); ?>
                                    <?php endif; ?>
                                    <?php if (isset($_SESSION['analysis_data']['note']) && strpos($_SESSION['analysis_data']['note'], 'Version allégée') !== false): ?>
                                        <br><span class="badge bg-warning text-dark">⚡ <?php echo htmlspecialchars($_SESSION['analysis_data']['note']); ?></span>
                                    <?php endif; ?>
                                </small>
                            </div>
                        <?php else: ?>
                        <h5><i class="bi bi-2-circle"></i> Contenu analysé (<?php echo count($parsedItems); ?> éléments)</h5>
                            <?php if (isset($_SESSION['analysis_data'])): ?>
                                <small class="text-danger">
                                    ❌ Données Python présentes mais structure manquante
                                    <?php if (isset($_SESSION['analysis_data']['statistics'])): ?>
                                        (statistics OK)
                                    <?php else: ?>
                                        (pas de statistics)
                                    <?php endif; ?>
                                </small>
                            <?php else: ?>
                                <small class="text-warning">⚠️ Pas de données Python trouvées en session</small>
                            <?php endif; ?>
                        <?php endif; ?>
                        <form method="post" style="display: inline;">
                            <button type="submit" name="action" value="analyze_items" class="btn btn-info btn-sm">
                                <i class="bi bi-search"></i> Rechercher correspondances
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        <!-- Plus de filtrage français - analyse de TOUT le contenu -->
                        
                        <!-- Statistiques -->
                        <?php
                        // Utiliser les statistiques Python si disponibles, sinon calculer depuis PHP
                        if (isset($_SESSION['analysis_data']) && isset($_SESSION['analysis_data']['statistics'])) {
                            $pythonStats = $_SESSION['analysis_data']['statistics']; 
                            $stats = [
                                'movies' => $pythonStats['total_movies'],
                                'tvshows' => $pythonStats['total_series_episodes'],
                                'unknown' => 0,
                                'with_season_episode' => $pythonStats['total_series_episodes'], // Tous les épisodes ont S/E
                                'unique_series' => $pythonStats['unique_series'],
                                'total_seasons' => $pythonStats['total_seasons']
                            ];
                        } else {
                            // Fallback : calculer depuis les éléments PHP
                        $stats = [
                            'movies' => 0,
                                'tvshows' => 0,
                            'unknown' => 0,
                            'with_season_episode' => 0
                        ];
                        foreach ($parsedItems as $item) {
                                $stats[$item['type'] === 'tvshow' ? 'tvshows' : ($item['type'] === 'movie' ? 'movies' : 'unknown')]++;
                            if ($item['season'] !== null && $item['episode'] !== null) {
                                $stats['with_season_episode']++;
                                }
                            }
                        }
                        ?>
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-danger"><?php echo number_format($stats['movies']); ?></h5>
                                        <p class="card-text">Films</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-primary"><?php echo number_format($stats['tvshows']); ?></h5>
                                        <p class="card-text">Épisodes</p>
                                    </div>
                                </div>
                            </div>
                            <?php if (isset($_SESSION['analysis_data']) && isset($_SESSION['analysis_data']['statistics'])): ?>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-info"><?php echo number_format($stats['unique_series']); ?></h5>
                                        <p class="card-text">Séries uniques</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-success"><?php echo number_format($stats['total_seasons']); ?></h5>
                                        <p class="card-text">Saisons</p>
                                    </div>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning"><?php echo $stats['unknown']; ?></h5>
                                        <p class="card-text">Inconnus</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-success"><?php echo $stats['with_season_episode']; ?></h5>
                                        <p class="card-text">Avec S/E</p>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Liste des éléments trouvés (premiers 20) -->
                        
                        

                    </div>
                </div>
                
                <!-- Section de recherche avancée -->
                <?php if (isset($_SESSION['analysis_data']) && isset($_SESSION['analysis_data']['statistics'])): ?>
                <div class="card search-card mt-4">
                    <div class="card-header">
                        <h5><i class="bi bi-search"></i> Recherche avancée dans le contenu analysé</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" class="search-form">
                            <input type="hidden" name="action" value="advanced_search">
                            
                        <div class="row">
                                <div class="col-md-4">
                                    <label for="search_term" class="form-label">Recherche par titre</label>
                                    <input type="text" class="form-control" name="search_term" id="search_term" 
                                           placeholder="Ex: Breaking Bad, John Wick..." 
                                           value="<?php echo htmlspecialchars($_POST['search_term'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="search_type" class="form-label">Type</label>
                                    <select class="form-select" name="search_type" id="search_type">
                                        <option value="all" <?php echo ($_POST['search_type'] ?? 'all') === 'all' ? 'selected' : ''; ?>>Tout</option>
                                        <option value="movie" <?php echo ($_POST['search_type'] ?? '') === 'movie' ? 'selected' : ''; ?>>Films</option>
                                        <option value="series" <?php echo ($_POST['search_type'] ?? '') === 'series' ? 'selected' : ''; ?>>Séries</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="year_from" class="form-label">Année (de)</label>
                                    <input type="number" class="form-control" name="year_from" id="year_from" 
                                           min="1900" max="2030" placeholder="2020"
                                           value="<?php echo htmlspecialchars($_POST['year_from'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="year_to" class="form-label">Année (à)</label>
                                    <input type="number" class="form-control" name="year_to" id="year_to" 
                                           min="1900" max="2030" placeholder="2024"
                                           value="<?php echo htmlspecialchars($_POST['year_to'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-search"></i> Rechercher
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Options de recherche dans l'historique -->
                            <?php if (!empty($analysisHistory)): ?>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-3">
                                                <i class="bi bi-clock-history text-info"></i> 
                                                Recherche étendue dans l'historique
                                            </h6>
                                            
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" name="search_in_history" 
                                                       id="search_in_history" value="1"
                                                       <?php echo isset($_POST['search_in_history']) && $_POST['search_in_history'] === '1' ? 'checked' : ''; ?>
                                                       onchange="toggleHistorySelection()">
                                                <label class="form-check-label" for="search_in_history">
                                                    <strong>Inclure les analyses de l'historique</strong>
                                                    <small class="text-muted d-block">Rechercher dans vos analyses précédentes en plus de l'analyse actuelle</small>
                                                </label>
                                            </div>
                                            
                                            <div id="history_selection" style="display: <?php echo isset($_POST['search_in_history']) && $_POST['search_in_history'] === '1' ? 'block' : 'none'; ?>;">
                                                <label class="form-label">Sélectionner les analyses à inclure :</label>
                                                <div class="row">
                                                    <?php foreach ($analysisHistory as $index => $history): ?>
                                                    <div class="col-md-6 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" 
                                                                   name="history_files[]" value="<?php echo htmlspecialchars($history['file']); ?>"
                                                                   id="history_<?php echo $index; ?>"
                                                                   <?php echo isset($_POST['history_files']) && in_array($history['file'], $_POST['history_files']) ? 'checked' : ''; ?>>
                                                            <label class="form-check-label small" for="history_<?php echo $index; ?>">
                                                                <strong><?php echo htmlspecialchars($history['source_file']); ?></strong>
                                                                <br><span class="text-muted">
                                                                    📅 <?php echo $history['date']; ?> 
                                                                    (🎬 <?php echo number_format($history['preview']['movies']); ?> films, 
                                                                    📺 <?php echo number_format($history['preview']['episodes']); ?> épisodes)
                                                </span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <?php endforeach; ?>
                                                </div>
                                                
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="selectAllHistory()">
                                                        <i class="bi bi-check-all"></i> Tout sélectionner
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectNoneHistory()">
                                                        <i class="bi bi-x-square"></i> Tout désélectionner
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                                                <?php endif; ?>
                        </form>
                        
                        <!-- Affichage des résultats de recherche -->
                        <?php if (isset($searchResults)): ?>
                            <?php if (isset($searchResults['error'])): ?>
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($searchResults['error']); ?>
                                </div>
                            <?php else: ?>
                                <!-- Statistiques de recherche -->
                                <div class="alert search-stats">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-graph-up-arrow fs-4 me-3"></i>
                                        <div>
                                            <h6 class="mb-1">🎯 Résultats de recherche</h6>
                                            <p class="mb-0">
                                                <strong><?php echo number_format($searchResults['statistics']['total_found']); ?> éléments trouvés</strong>
                                                → <?php echo number_format($searchResults['statistics']['movies_found']); ?> films, 
                                                <?php echo number_format($searchResults['statistics']['series_episodes_found']); ?> épisodes de 
                                                <?php echo number_format($searchResults['statistics']['unique_series_found']); ?> séries
                                            </p>
                                            <?php if (isset($searchResults['sources']) && count($searchResults['sources']) > 1): ?>
                                                <small class="text-light">
                                                    📊 Sources consultées: <?php echo implode(', ', $searchResults['sources']); ?>
                                                </small>
                                                <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Interface d'insertion en base de données -->
                                <div class="card mt-3 border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="bi bi-database-add"></i> Insertion en base de données
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label for="target_database" class="form-label">Base de données cible</label>
                                                <select class="form-select" id="target_database" name="target_database">
                                                    <option value="iptv1" <?php echo ($_SESSION['m3u_source'] ?? 'iptv1') === 'iptv1' ? 'selected' : ''; ?>>
                                                        IPTV 1 (Principal) <?php echo !empty($config['iptv_db']['host']) ? '✅' : '❌'; ?>
                                                    </option>
                                                    <option value="iptv2" <?php echo ($_SESSION['m3u_source'] ?? '') === 'iptv2' ? 'selected' : ''; ?>>
                                                        IPTV 2 (Secondaire) <?php echo !empty($config['iptv_db2']['host']) ? '✅' : '❌'; ?>
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="col-md-4 d-flex align-items-end">
                                                <div class="btn-group w-100" role="group">
                                                    <button type="button" class="btn btn-outline-primary" onclick="selectAllItems()">
                                                        <i class="bi bi-check-all"></i> Tout sélectionner
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                                                        <i class="bi bi-x-square"></i> Tout déselectionner
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-md-4 d-flex align-items-end">
                                                <button type="button" class="btn btn-success w-100" onclick="insertSelectedItems()" id="insertBtn" disabled>
                                                    <i class="bi bi-database-add"></i> Insérer les éléments sélectionnés
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <div id="selection_summary" class="alert alert-info" style="display: none;">
                                                <i class="bi bi-info-circle"></i> 
                                                <span id="selection_text">Aucun élément sélectionné</span>
                                            </div>
                                            
                                            <!-- Bouton de test du parsing -->
                                            <div class="mt-2">
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="testTitleParsing()">
                                                    <i class="bi bi-gear"></i> Tester le parsing des titres
                                                </button>
                                                <small class="text-muted ms-2">
                                                    Voir comment les titres sont formatés avant insertion
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Onglets pour séparer films et séries -->
                                <ul class="nav nav-tabs" id="searchTabs" role="tablist">
                                    <?php if ($searchResults['statistics']['movies_found'] > 0): ?>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="movies-tab" data-bs-toggle="tab" 
                                                data-bs-target="#movies-content" type="button" role="tab">
                                            <i class="bi bi-film"></i> Films (<?php echo number_format($searchResults['statistics']['movies_found']); ?>)
                                        </button>
                                    </li>
                                            <?php endif; ?>
                                            
                                    <?php if ($searchResults['statistics']['unique_series_found'] > 0): ?>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link <?php echo $searchResults['statistics']['movies_found'] === 0 ? 'active' : ''; ?>" 
                                                id="series-tab" data-bs-toggle="tab" data-bs-target="#series-content" type="button" role="tab">
                                            <i class="bi bi-tv"></i> Séries (<?php echo number_format($searchResults['statistics']['unique_series_found']); ?>)
                                        </button>
                                    </li>
                                                                <?php endif; ?>
                                </ul>
                                
                                <div class="tab-content mt-3" id="searchTabsContent">
                                    <!-- Onglet Films -->
                                    <?php if ($searchResults['statistics']['movies_found'] > 0): ?>
                                    <div class="tab-pane fade show active" id="movies-content" role="tabpanel">
                                        <h6 class="mb-3">Films trouvés (<?php echo number_format($searchResults['statistics']['movies_found']); ?>)</h6>
                                        
                                        <div class="row">
                                            <?php foreach ($searchResults['movies'] as $index => $movie): ?>
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="card movie-card h-100">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <h6 class="card-title text-primary mb-0 flex-grow-1">
                                                                <?php echo htmlspecialchars($movie['clean_name']); ?>
                                                                <?php if ($movie['year']): ?>
                                                                    <small class="text-muted">(<?php echo $movie['year']; ?>)</small>
                                                                <?php endif; ?>
                                                            </h6>
                                                            <div class="form-check">
                                                                <input class="form-check-input item-checkbox" type="checkbox" 
                                                                       data-type="movie"
                                                                       data-item='<?php echo htmlspecialchars(json_encode([
                                                                           'title' => $movie['title'],
                                                                           'clean_name' => $movie['clean_name'],
                                                                           'type' => 'movie',
                                                                           'year' => $movie['year'],
                                                                           'url' => $movie['url'] ?? '',
                                                                           'group_title' => $movie['group_title'] ?? '',
                                                                           'tmdb_id' => $movie['tmdb_id'] ?? null
                                                                       ]), ENT_QUOTES); ?>'
                                                                       id="movie_<?php echo $index; ?>"
                                                                       onchange="updateSelection()">
                                                                <label class="form-check-label" for="movie_<?php echo $index; ?>"></label>
                                                            </div>
                                                    </div>
                                                        
                                                        <div class="mb-2">
                                                            <span class="badge badge-film">🎬 Film</span>
                                                            <?php if (!empty($movie['group_title'])): ?>
                                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($movie['group_title']); ?></span>
                                                            <?php endif; ?>
                                                            <?php if (isset($movie['_source'])): ?>
                                                                <span class="badge bg-info">📁 <?php echo htmlspecialchars($movie['_source']); ?></span>
                                                                <?php endif; ?>
                                                        </div>
                                                        
                                                        <?php if ($movie['title'] !== $movie['clean_name']): ?>
                                                            <small class="text-muted">
                                                                Titre complet: <?php echo htmlspecialchars($movie['title']); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                            </div>
                                                        <?php endforeach; ?>
                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                    
                                    <!-- Onglet Séries -->
                                    <?php if ($searchResults['statistics']['unique_series_found'] > 0): ?>
                                    <div class="tab-pane fade <?php echo $searchResults['statistics']['movies_found'] === 0 ? 'show active' : ''; ?>" 
                                         id="series-content" role="tabpanel">
                                        <h6 class="mb-3">Séries trouvées (<?php echo number_format($searchResults['statistics']['unique_series_found']); ?>)</h6>
                                        
                                        <?php foreach ($searchResults['series_grouped'] as $series): ?>
                                        <div class="card series-card mb-4">
                                            <div class="card-header">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0 text-primary">
                                                        <i class="bi bi-tv"></i> <?php echo htmlspecialchars($series['name']); ?>
                                                    </h6>
                                                    <div>
                                                        <span class="badge badge-series">
                                                            📺 <?php echo $series['seasons_count']; ?> saison<?php echo $series['seasons_count'] > 1 ? 's' : ''; ?>
                                                        </span>
                                                        <span class="badge bg-primary">
                                                            <?php echo number_format($series['total_episodes']); ?> épisodes
                                                        </span>
                                                        <?php if (!empty($series['group_title'])): ?>
                                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($series['group_title']); ?></span>
                                                        <?php endif; ?>
                                                        <?php if (isset($series['_source'])): ?>
                                                            <span class="badge bg-info">📁 <?php echo htmlspecialchars($series['_source']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <?php foreach ($series['seasons'] as $season): ?>
                                                    <div class="col-md-4 col-lg-3 mb-3">
                                                        <div class="border rounded p-3 text-center bg-light season-box">
                                                            <h6 class="text-success mb-1">
                                                                <i class="bi bi-collection"></i> Saison <?php echo $season['season_number']; ?>
                                                            </h6>
                                                            <p class="mb-0">
                                                                <strong><?php echo $season['episode_count']; ?></strong> épisode<?php echo $season['episode_count'] > 1 ? 's' : ''; ?>
                                                            </p>
                                                            
                                                            <!-- Détail des épisodes (collapsible) -->
                                                            <button class="btn btn-sm btn-outline-secondary mt-2" type="button" 
                                                                    data-bs-toggle="collapse" 
                                                                    data-bs-target="#episodes-<?php echo preg_replace('/[^a-zA-Z0-9]/', '', $series['name']); ?>-<?php echo $season['season_number']; ?>" 
                                                                    aria-expanded="false">
                                                                <i class="bi bi-eye"></i> Voir épisodes
                                        </button>
                                                            
                                                            <div class="collapse mt-2" id="episodes-<?php echo preg_replace('/[^a-zA-Z0-9]/', '', $series['name']); ?>-<?php echo $season['season_number']; ?>">
                                                                <div class="text-start episode-list">
                                                                    <?php foreach ($season['episodes'] as $episodeIndex => $episode): ?>
                                                                        <div class="d-flex justify-content-between align-items-center p-1 border-bottom">
                                                                            <small class="text-muted flex-grow-1">
                                                                                <i class="bi bi-play-circle text-success me-1"></i>
                                                                                E<?php echo sprintf('%02d', $episode['episode']); ?>: 
                                                                                <?php echo htmlspecialchars($episode['title']); ?>
                                                                            </small>
                                                                            <div class="form-check form-check-sm">
                                                                                <input class="form-check-input item-checkbox" type="checkbox" 
                                                                                       data-type="series"
                                                                                       data-item='<?php echo htmlspecialchars(json_encode([
                                                                                           'title' => $episode['title'],
                                                                                           'clean_name' => $episode['clean_name'],
                                                                                           'type' => 'tvshow',
                                                                                           'season' => $episode['season'],
                                                                                           'episode' => $episode['episode'],
                                                                                           'url' => $episode['url'] ?? '',
                                                                                           'group_title' => $episode['group_title'] ?? '',
                                                                                           'tmdb_id' => $episode['tmdb_id'] ?? null
                                                                                       ]), ENT_QUOTES); ?>'
                                                                                       id="episode_<?php echo preg_replace('/[^a-zA-Z0-9]/', '', $series['name']); ?>_<?php echo $season['season_number']; ?>_<?php echo $episodeIndex; ?>"
                                                                                       onchange="updateSelection()">
                                                                                <label class="form-check-label" for="episode_<?php echo preg_replace('/[^a-zA-Z0-9]/', '', $series['name']); ?>_<?php echo $season['season_number']; ?>_<?php echo $episodeIndex; ?>"></label>
                                                                            </div>
                                                                        </div>
                                                                    <?php endforeach; ?>
                                                                </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-muted text-center py-4">
                                <i class="bi bi-search fs-1"></i>
                                <p class="mt-2">Utilisez le formulaire ci-dessus pour rechercher dans les 
                                <?php echo number_format($_SESSION['analysis_data']['statistics']['total_movies'] + $_SESSION['analysis_data']['statistics']['total_series_episodes']); ?> 
                                éléments analysés.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
                <?php endif; ?>
                
    </div>
        </div>
    </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Scripts pour l'interface utilisateur
        document.addEventListener('DOMContentLoaded', function() {
            // Animation des cartes de statistiques
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
            
                                        // Amélioration de l'interface de recherche
            const searchForm = document.querySelector('form[method="post"]');
            if (searchForm) {
                const submitBtn = searchForm.querySelector('button[type="submit"]');
                searchForm.addEventListener('submit', function() {
                    if (submitBtn) {
                        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Recherche...';
                        submitBtn.disabled = true;
                    }
                });
            }
            
            // Amélioration de l'interface de téléchargement
            const downloadForm = document.querySelector('form button[name="action"][value="download_m3u"]');
            if (downloadForm) {
                const form = downloadForm.closest('form');
                form.addEventListener('submit', function(e) {
                    const sourceSelect = form.querySelector('select[name="source"]');
                    const selectedSource = sourceSelect.value;
                    
                    // Désactiver le bouton et changer le texte
                    downloadForm.innerHTML = '<i class="bi bi-hourglass-split"></i> Traitement en cours...';
                    downloadForm.disabled = true;
                    
                    // Ajouter un message d'information
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-info mt-3';
                    alertDiv.innerHTML = `
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                            <div>
                                <strong>Traitement en cours...</strong><br>
                                <small>📥 Téléchargement → 🐍 Analyse Python → 📊 Affichage des résultats</small>
                                ${selectedSource !== 'test' ? '<br><small class="text-warning">⏰ Gros fichiers: 2-3 minutes</small>' : ''}
                            </div>
                        </div>
                    `;
                    
                    // Insérer après le formulaire
                    form.parentNode.insertBefore(alertDiv, form.nextSibling);
                });
            }
            
            console.log('Interface de recherche M3U chargée');
        });
        
        // Variables globales pour la sélection
        let selectedItems = [];
        
        // Fonction pour mettre à jour la sélection
        function updateSelection() {
            selectedItems = [];
            const checkboxes = document.querySelectorAll('.item-checkbox:checked');
            
            checkboxes.forEach(checkbox => {
                try {
                    const itemData = JSON.parse(checkbox.dataset.item);
                    selectedItems.push(itemData);
                } catch (e) {
                    console.error('Erreur parsing données item:', e);
                }
            });
            
            updateSelectionUI();
        }
        
        // Fonction pour mettre à jour l'interface de sélection
        function updateSelectionUI() {
            const summaryDiv = document.getElementById('selection_summary');
            const summaryText = document.getElementById('selection_text');
            const insertBtn = document.getElementById('insertBtn');
            
            if (selectedItems.length === 0) {
                summaryDiv.style.display = 'none';
                insertBtn.disabled = true;
                insertBtn.innerHTML = '<i class="bi bi-database-add"></i> Insérer les éléments sélectionnés';
            } else {
                const movies = selectedItems.filter(item => item.type === 'movie').length;
                const episodes = selectedItems.filter(item => item.type === 'tvshow').length;
                
                summaryDiv.style.display = 'block';
                summaryText.innerHTML = `${selectedItems.length} éléments sélectionnés : 
                    ${movies} films, ${episodes} épisodes`;
                
                insertBtn.disabled = false;
                insertBtn.innerHTML = `<i class="bi bi-database-add"></i> Insérer ${selectedItems.length} éléments`;
            }
        }
        
        // Fonction pour sélectionner tous les éléments
        function selectAllItems() {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSelection();
        }
        
        // Fonction pour déselectionner tous les éléments
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelection();
        }
        
        // Fonction pour insérer les éléments sélectionnés
        function insertSelectedItems() {
            if (selectedItems.length === 0) {
                alert('Aucun élément sélectionné pour l\'insertion.');
                return;
            }
            
            const targetDatabase = document.getElementById('target_database').value;
            const insertBtn = document.getElementById('insertBtn');
            
            // Confirmation
            const movies = selectedItems.filter(item => item.type === 'movie').length;
            const episodes = selectedItems.filter(item => item.type === 'tvshow').length;
            
            if (!confirm(`Confirmer l'insertion de ${selectedItems.length} éléments (${movies} films, ${episodes} épisodes) dans la base ${targetDatabase.toUpperCase()} ?`)) {
                return;
            }
            
            // Désactiver le bouton et changer le texte
            insertBtn.disabled = true;
            insertBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Insertion en cours...';
            
            // Créer et soumettre le formulaire
            const form = document.createElement('form');
            form.method = 'post';
            form.style.display = 'none';
            
            // Action
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'insert_selected';
            form.appendChild(actionInput);
            
            // Base de données cible
            const dbInput = document.createElement('input');
            dbInput.type = 'hidden';
            dbInput.name = 'target_database';
            dbInput.value = targetDatabase;
            form.appendChild(dbInput);
            
            // Données sélectionnées
            const itemsInput = document.createElement('input');
            itemsInput.type = 'hidden';
            itemsInput.name = 'selected_items';
            itemsInput.value = JSON.stringify(selectedItems);
            form.appendChild(itemsInput);
            
            // Ajouter au document et soumettre
            document.body.appendChild(form);
            form.submit();
        }
        
        // Fonction pour tester le parsing des titres
        function testTitleParsing() {
            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=test_parsing'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showParsingModal(data.results);
                } else {
                    alert('Erreur lors du test de parsing');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Erreur de communication avec le serveur');
            });
        }
        
        // Fonction pour afficher la modal de test de parsing
        function showParsingModal(results) {
            // Créer la modal
            const modalHtml = `
                <div class="modal fade" id="parsingTestModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-info text-white">
                                <h5 class="modal-title">
                                    <i class="bi bi-gear"></i> Test du parsing des titres IPTV
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p class="text-muted mb-3">
                                    <i class="bi bi-info-circle"></i> 
                                    Voici comment le système analyse et formate les titres avant insertion en base de données :
                                </p>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Titre original</th>
                                                <th>Titre nettoyé</th>
                                                <th>Année</th>
                                                <th>Qualité</th>
                                                <th>Langue</th>
                                                <th>Pays</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${results.map(result => `
                                                <tr>
                                                    <td><strong>${result.original}</strong></td>
                                                    <td><span class="text-primary">${result.parsed.clean_title}</span></td>
                                                    <td><span class="badge bg-secondary">${result.parsed.year || '-'}</span></td>
                                                    <td><span class="badge bg-success">${result.parsed.quality}</span></td>
                                                    <td><span class="badge bg-info">${result.parsed.language || '-'}</span></td>
                                                    <td><span class="badge bg-warning">${result.parsed.country || '-'}</span></td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                                                                 <div class="alert alert-success mt-3">
                                     <h6><i class="bi bi-check-circle"></i> Avantages du parsing automatique :</h6>
                                     <ul class="mb-0">
                                         <li>Titres propres sans prefixes/suffixes</li>
                                         <li>Années extraites automatiquement</li>
                                         <li>Qualités détectées (4K, FHD, HD, etc.)</li>
                                         <li>Langues identifiées (FR, EN, VOSTFR, etc.)</li>
                                         <li>Pays d'origine extraits</li>
                                         <li><strong>Gestion des remakes :</strong> Mulan (1998) ≠ Mulan (2020)</li>
                                     </ul>
                                 </div>
                                 <div class="alert alert-info mt-2">
                                     <h6><i class="bi bi-info-circle"></i> Logique de détection des doublons :</h6>
                                     <p class="mb-0">
                                         Le système vérifie l'existence par <strong>titre + année</strong> pour éviter les faux positifs.
                                         <br><small class="text-muted">
                                             Exemple : "Mulan (1998)" et "Mulan (2020)" sont deux films distincts.
                                         </small>
                                     </p>
                                 </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Ajouter la modal au DOM
            if (document.getElementById('parsingTestModal')) {
                document.getElementById('parsingTestModal').remove();
            }
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // Afficher la modal
            const modal = new bootstrap.Modal(document.getElementById('parsingTestModal'));
            modal.show();
        }
        
        // Fonctions pour la gestion de l'historique
        function loadHistoryAnalysis(filePath) {
            const form = document.createElement('form');
            form.method = 'post';
            form.style.display = 'none';
            
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'load_history';
            
            const fileInput = document.createElement('input');
            fileInput.type = 'hidden';
            fileInput.name = 'history_file';
            fileInput.value = filePath;
            
            form.appendChild(actionInput);
            form.appendChild(fileInput);
            document.body.appendChild(form);
            form.submit();
        }
        
        function toggleHistorySelection() {
            const checkbox = document.getElementById('search_in_history');
            const selectionDiv = document.getElementById('history_selection');
            
            if (checkbox.checked) {
                selectionDiv.style.display = 'block';
            } else {
                selectionDiv.style.display = 'none';
                // Décocher toutes les analyses
                const historyCheckboxes = document.querySelectorAll('input[name="history_files[]"]');
                historyCheckboxes.forEach(cb => cb.checked = false);
            }
        }
        
        function selectAllHistory() {
            const historyCheckboxes = document.querySelectorAll('input[name="history_files[]"]');
            historyCheckboxes.forEach(cb => cb.checked = true);
        }
        
        function selectNoneHistory() {
            const historyCheckboxes = document.querySelectorAll('input[name="history_files[]"]');
            historyCheckboxes.forEach(cb => cb.checked = false);
        }
        
        // Fonctions pour la suppression de l'historique
        function deleteSelectedHistory() {
            const select = document.getElementById('history_file');
            const selectedFile = select.value;
            const selectedText = select.options[select.selectedIndex].text;
            
            if (!selectedFile) {
                alert('Veuillez sélectionner un historique à supprimer');
                return;
            }
            
            if (confirm(`Êtes-vous sûr de vouloir supprimer cet historique ?\n\n${selectedText}\n\nCette action est irréversible.`)) {
                const form = document.createElement('form');
                form.method = 'post';
                form.style.display = 'none';
                
                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_history';
                
                const fileInput = document.createElement('input');
                fileInput.type = 'hidden';
                fileInput.name = 'history_file';
                fileInput.value = selectedFile;
                
                form.appendChild(actionInput);
                form.appendChild(fileInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function deleteSpecificHistory(filePath, fileName) {
            if (confirm(`Êtes-vous sûr de vouloir supprimer cet historique ?\n\n📁 ${fileName}\n\nCette action est irréversible.`)) {
                const form = document.createElement('form');
                form.method = 'post';
                form.style.display = 'none';
                
                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_history';
                
                const fileInput = document.createElement('input');
                fileInput.type = 'hidden';
                fileInput.name = 'history_file';
                fileInput.value = filePath;
                
                form.appendChild(actionInput);
                form.appendChild(fileInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function deleteAllHistory() {
            if (confirm('⚠️ ATTENTION !\n\nÊtes-vous sûr de vouloir supprimer TOUT l\'historique des analyses ?\n\nTous les fichiers d\'historique seront définitivement supprimés.\nCette action est irréversible !')) {
                const form = document.createElement('form');
                form.method = 'post';
                form.style.display = 'none';
                
                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete_all_history';
                
                form.appendChild(actionInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>