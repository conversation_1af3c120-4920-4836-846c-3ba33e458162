<?php
// Système de contrôle qualité VOD IPTV
$config = require_once 'config.php';

class QualityController {
    private $config;
    private $issues = [];
    
    public function __construct($config) {
        $this->config = $config;
    }
    
    private function getConnection($dbConfig) {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    }
    
    public function runQualityChecks() {
        $this->issues = [];
        
        $this->checkDataConsistency();
        $this->checkMissingMetadata();
        $this->checkImageUrls();
        $this->checkDuplicates();
        $this->checkOrphanGenres();
        
        return $this->issues;
    }
    
    private function checkDataConsistency() {
        $mainPdo = $this->getConnection($this->config['main_db']);
        
        // Vérifier les types incohérents
        $stmt = $mainPdo->query("SELECT id, name, type FROM entertainments 
                                WHERE type NOT IN ('movie', 'tvshow') AND deleted_at IS NULL");
        $invalidTypes = $stmt->fetchAll();
        
        if (!empty($invalidTypes)) {
            $this->issues[] = [
                'type' => 'error',
                'category' => 'Cohérence des données',
                'title' => 'Types invalides détectés',
                'count' => count($invalidTypes),
                'description' => 'Éléments avec des types autres que "movie" ou "tvshow"',
                'items' => $invalidTypes,
                'fixable' => true,
                'fix_action' => 'fix_invalid_types'
            ];
        }
        
        // Vérifier les ratings invalides
        $stmt = $mainPdo->query("SELECT id, name, IMDb_rating FROM entertainments 
                                WHERE IMDb_rating IS NOT NULL 
                                AND (IMDb_rating < 0 OR IMDb_rating > 10 OR IMDb_rating NOT REGEXP '^[0-9]+\\.?[0-9]*$') 
                                AND deleted_at IS NULL");
        $invalidRatings = $stmt->fetchAll();
        
        if (!empty($invalidRatings)) {
            $this->issues[] = [
                'type' => 'warning',
                'category' => 'Cohérence des données',
                'title' => 'Ratings invalides',
                'count' => count($invalidRatings),
                'description' => 'Ratings IMDb non conformes (doit être entre 0 et 10)',
                'items' => $invalidRatings,
                'fixable' => true,
                'fix_action' => 'fix_invalid_ratings'
            ];
        }
    }
    
    private function checkMissingMetadata() {
        $mainPdo = $this->getConnection($this->config['main_db']);
        
        // Contenus sans description
        $stmt = $mainPdo->query("SELECT COUNT(*) as count FROM entertainments 
                                WHERE (description IS NULL OR description = '') AND deleted_at IS NULL");
        $noDescription = $stmt->fetch()['count'];
        
        if ($noDescription > 0) {
            $this->issues[] = [
                'type' => 'info',
                'category' => 'Métadonnées manquantes',
                'title' => 'Descriptions manquantes',
                'count' => $noDescription,
                'description' => 'Contenus sans description depuis TMDB',
                'fixable' => true,
                'fix_action' => 'enrich_metadata'
            ];
        }
        
        // Contenus sans date de sortie
        $stmt = $mainPdo->query("SELECT COUNT(*) as count FROM entertainments 
                                WHERE release_date IS NULL AND deleted_at IS NULL");
        $noReleaseDate = $stmt->fetch()['count'];
        
        if ($noReleaseDate > 0) {
            $this->issues[] = [
                'type' => 'info',
                'category' => 'Métadonnées manquantes',
                'title' => 'Dates de sortie manquantes',
                'count' => $noReleaseDate,
                'description' => 'Contenus sans date de sortie',
                'fixable' => true,
                'fix_action' => 'enrich_metadata'
            ];
        }
    }
    
    private function checkImageUrls() {
        $mainPdo = $this->getConnection($this->config['main_db']);
        
        // URLs avec des domaines non fiables
        $stmt = $mainPdo->query("SELECT id, name, poster_url FROM entertainments 
                                WHERE poster_url IS NOT NULL 
                                AND poster_url NOT LIKE '%image.tmdb.org%' 
                                AND poster_url NOT LIKE '%amazonaws.com%'
                                AND deleted_at IS NULL
                                LIMIT 20");
        $suspiciousUrls = $stmt->fetchAll();
        
        if (!empty($suspiciousUrls)) {
            $this->issues[] = [
                'type' => 'warning',
                'category' => 'Qualité des images',
                'title' => 'URLs de posters suspects',
                'count' => count($suspiciousUrls),
                'description' => 'Posters hébergés sur des domaines non fiables',
                'items' => $suspiciousUrls,
                'fixable' => false
            ];
        }
    }
    
    private function checkDuplicates() {
        $mainPdo = $this->getConnection($this->config['main_db']);
        
        // Doublons exacts par nom
        $stmt = $mainPdo->query("SELECT name, COUNT(*) as count, GROUP_CONCAT(id) as ids 
                                FROM entertainments 
                                WHERE deleted_at IS NULL
                                GROUP BY name 
                                HAVING count > 1 
                                LIMIT 10");
        $nameDuplicates = $stmt->fetchAll();
        
        if (!empty($nameDuplicates)) {
            $this->issues[] = [
                'type' => 'warning',
                'category' => 'Doublons',
                'title' => 'Doublons par nom',
                'count' => count($nameDuplicates),
                'description' => 'Contenus avec exactement le même nom',
                'items' => $nameDuplicates,
                'fixable' => true,
                'fix_action' => 'merge_duplicates'
            ];
        }
    }
    
    private function checkOrphanGenres() {
        $mainPdo = $this->getConnection($this->config['main_db']);
        
        // Mappings de genres orphelins
        $stmt = $mainPdo->query("SELECT COUNT(*) as count 
                                FROM entertainment_gener_mapping egm 
                                LEFT JOIN entertainments e ON egm.entertainment_id = e.id 
                                WHERE e.id IS NULL OR e.deleted_at IS NOT NULL");
        $orphanGenres = $stmt->fetch()['count'];
        
        if ($orphanGenres > 0) {
            $this->issues[] = [
                'type' => 'warning',
                'category' => 'Intégrité référentielle',
                'title' => 'Genres orphelins',
                'count' => $orphanGenres,
                'description' => 'Mappings de genres pointant vers des contenus supprimés',
                'fixable' => true,
                'fix_action' => 'cleanup_orphan_genres'
            ];
        }
    }
    
    public function fixIssue($fixAction, $params = []) {
        $mainPdo = $this->getConnection($this->config['main_db']);
        
        switch ($fixAction) {
            case 'fix_invalid_types':
                // Corriger les types en analysant le nom
                $stmt = $mainPdo->query("SELECT id, name FROM entertainments 
                                        WHERE type NOT IN ('movie', 'tvshow') AND deleted_at IS NULL");
                $items = $stmt->fetchAll();
                
                $fixed = 0;
                foreach ($items as $item) {
                    $type = 'movie'; // Par défaut
                    $name = strtolower($item['name']);
                    
                    if (preg_match('/\b(series?|saison|season|episode|s\d+|épisode)\b/i', $name)) {
                        $type = 'tvshow';
                    }
                    
                    $updateStmt = $mainPdo->prepare("UPDATE entertainments SET type = ? WHERE id = ?");
                    $updateStmt->execute([$type, $item['id']]);
                    $fixed++;
                }
                
                return ['success' => true, 'fixed' => $fixed];
                
            case 'cleanup_orphan_genres':
                $stmt = $mainPdo->query("DELETE egm FROM entertainment_gener_mapping egm 
                                        LEFT JOIN entertainments e ON egm.entertainment_id = e.id 
                                        WHERE e.id IS NULL OR e.deleted_at IS NOT NULL");
                $deleted = $stmt->rowCount();
                
                return ['success' => true, 'fixed' => $deleted];
                
            default:
                return ['success' => false, 'message' => 'Action de correction inconnue'];
        }
    }
}

// Traitement AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    $quality = new QualityController($config);
    
    if ($action === 'run_quality_checks') {
        $issues = $quality->runQualityChecks();
        echo json_encode(['success' => true, 'issues' => $issues]);
        exit;
    }
    
    if ($action === 'fix_issue') {
        $fixAction = $_POST['fix_action'] ?? '';
        $result = $quality->fixIssue($fixAction);
        echo json_encode($result);
        exit;
    }
}

?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contrôle Qualité - VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/phosphor-icons@2.0.0/src/css/icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="ph ph-shield-check me-2"></i>
                Contrôle Qualité
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="sync.php">Sync & Monitoring</a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="ph ph-magnifying-glass me-2"></i>
                Analyse de la qualité des données
            </h2>
            <button onclick="runQualityChecks()" id="checkBtn" class="btn btn-primary">
                <i class="ph ph-play me-2"></i>
                Lancer l'analyse
            </button>
        </div>
        
        <div id="checkProgress" class="mb-4" style="display: none;">
            <div class="card">
                <div class="card-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Analyse en cours...</span>
                    </div>
                    <h5>Analyse en cours...</h5>
                    <p class="text-muted">Vérification de la cohérence des données</p>
                </div>
            </div>
        </div>
        
        <div id="qualityResults"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function runQualityChecks() {
            const btn = document.getElementById('checkBtn');
            const progress = document.getElementById('checkProgress');
            const results = document.getElementById('qualityResults');
            
            btn.disabled = true;
            progress.style.display = 'block';
            results.innerHTML = '';
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=run_quality_checks'
            })
            .then(response => response.json())
            .then(data => {
                progress.style.display = 'none';
                btn.disabled = false;
                
                if (data.success) {
                    displayQualityResults(data.issues);
                } else {
                    results.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="ph ph-x-circle me-2"></i>
                            Erreur lors de l'analyse
                        </div>
                    `;
                }
            })
            .catch(error => {
                progress.style.display = 'none';
                btn.disabled = false;
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="ph ph-x-circle me-2"></i>
                        Erreur réseau lors de l'analyse
                    </div>
                `;
            });
        }
        
        function displayQualityResults(issues) {
            const results = document.getElementById('qualityResults');
            
            if (issues.length === 0) {
                results.innerHTML = `
                    <div class="alert alert-success">
                        <i class="ph ph-check-circle me-2"></i>
                        <strong>Excellent !</strong> Aucun problème de qualité détecté.
                    </div>
                `;
                return;
            }
            
            let html = '<div class="row">';
            
            issues.forEach((issue, index) => {
                const badgeClass = issue.type === 'error' ? 'bg-danger' : 
                                 issue.type === 'warning' ? 'bg-warning' : 'bg-info';
                const iconClass = issue.type === 'error' ? 'ph-x-circle' : 
                                issue.type === 'warning' ? 'ph-warning' : 'ph-info';
                
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="ph ${iconClass} me-2"></i>
                                    ${issue.title}
                                </h6>
                                <span class="badge ${badgeClass}">${issue.count}</span>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small mb-2">${issue.description}</p>
                                <p class="mb-2"><strong>Catégorie:</strong> ${issue.category}</p>
                                
                                ${issue.fixable ? `
                                    <button onclick="fixIssue('${issue.fix_action}')" 
                                            class="btn btn-sm btn-outline-success">
                                        <i class="ph ph-wrench me-1"></i>
                                        Corriger automatiquement
                                    </button>
                                ` : `
                                    <small class="text-muted">
                                        <i class="ph ph-info me-1"></i>
                                        Correction manuelle requise
                                    </small>
                                `}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            results.innerHTML = html;
        }
        
        function fixIssue(fixAction) {
            if (!confirm('Êtes-vous sûr de vouloir appliquer cette correction automatique ?')) {
                return;
            }
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=fix_issue&fix_action=${fixAction}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Correction appliquée avec succès ! ${data.fixed} éléments corrigés.`);
                    runQualityChecks(); // Relancer l'analyse
                } else {
                    alert(`Erreur lors de la correction : ${data.message}`);
                }
            })
            .catch(error => {
                alert('Erreur réseau lors de la correction');
            });
        }
    </script>
</body>
</html> 