<?php
// Système de synchronisation et monitoring VOD IPTV
$config = require_once 'config.php';

class SyncManager {
    private $config;
    private $logFile;
    
    public function __construct($config) {
        $this->config = $config;
        $this->logFile = __DIR__ . '/logs/sync_' . date('Y-m-d') . '.log';
        
        // <PERSON><PERSON>er le dossier logs s'il n'existe pas
        if (!is_dir(__DIR__ . '/logs')) {
            mkdir(__DIR__ . '/logs', 0755, true);
        }
    }
    
    private function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    private function getConnection($dbConfig) {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    }
    
    public function cleanupDuplicates() {
        try {
            $mainPdo = $this->getConnection($this->config['main_db']);
            
            // Trouver les doublons basés sur tmdb_id
            $sql = "SELECT tmdb_id, COUNT(*) as count, GROUP_CONCAT(id) as ids 
                    FROM entertainments 
                    WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND deleted_at IS NULL
                    GROUP BY tmdb_id 
                    HAVING count > 1";
            
            $stmt = $mainPdo->query($sql);
            $duplicates = $stmt->fetchAll();
            
            $cleaned = 0;
            foreach ($duplicates as $duplicate) {
                $ids = explode(',', $duplicate['ids']);
                // Garder le premier, supprimer les autres (soft delete)
                array_shift($ids);
                
                foreach ($ids as $id) {
                    $deleteStmt = $mainPdo->prepare("UPDATE entertainments SET deleted_at = NOW() WHERE id = ?");
                    $deleteStmt->execute([$id]);
                    $cleaned++;
                }
                
                $this->log("Nettoyé {$cleaned} doublons pour TMDB ID {$duplicate['tmdb_id']}");
            }
            
            return ['success' => true, 'cleaned' => $cleaned, 'groups' => count($duplicates)];
            
        } catch (Exception $e) {
            $this->log("Erreur nettoyage doublons: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    public function validatePosters() {
        try {
            $mainPdo = $this->getConnection($this->config['main_db']);
            
            // Vérifier les URLs de posters cassées
            $sql = "SELECT id, name, poster_url FROM entertainments 
                    WHERE poster_url IS NOT NULL AND poster_url != '' AND deleted_at IS NULL 
                    LIMIT 50";
            
            $stmt = $mainPdo->query($sql);
            $items = $stmt->fetchAll();
            
            $broken = 0;
            $fixed = 0;
            
            foreach ($items as $item) {
                // Vérifier si l'URL est accessible
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $item['poster_url']);
                curl_setopt($ch, CURLOPT_NOBODY, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                
                curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode >= 400) {
                    $broken++;
                    
                    // Essayer de récupérer une nouvelle image depuis TMDB
                    $updateStmt = $mainPdo->prepare("UPDATE entertainments SET poster_url = NULL WHERE id = ?");
                    $updateStmt->execute([$item['id']]);
                    
                    $this->log("Poster cassé supprimé pour: {$item['name']} (HTTP {$httpCode})");
                }
                
                usleep(200000); // 0.2 seconde entre les vérifications
            }
            
            return ['success' => true, 'checked' => count($items), 'broken' => $broken];
            
        } catch (Exception $e) {
            $this->log("Erreur validation posters: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    public function syncStats() {
        try {
            $stats = [];
            
            // Stats base principale
            $mainPdo = $this->getConnection($this->config['main_db']);
            $stmt = $mainPdo->query("SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN tmdb_id IS NULL OR tmdb_id = '' THEN 1 END) as no_tmdb,
                COUNT(CASE WHEN poster_url IS NULL OR poster_url = '' THEN 1 END) as no_poster,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as recent_24h
            FROM entertainments WHERE deleted_at IS NULL");
            $stats['main'] = $stmt->fetch();
            
            // Stats IPTV
            foreach (['iptv_db', 'iptv_db2'] as $db) {
                $pdo = $this->getConnection($this->config[$db]);
                $stmt = $pdo->query("SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN tmdb_id IS NULL OR tmdb_id = '' OR tmdb_id = '0' THEN 1 END) as no_tmdb,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as recent_24h
                FROM poster_iptv");
                $stats[$db] = $stmt->fetch();
            }
            
            // Sauvegarder les stats avec timestamp
            $statsFile = __DIR__ . '/logs/stats_' . date('Y-m-d_H') . '.json';
            file_put_contents($statsFile, json_encode([
                'timestamp' => date('Y-m-d H:i:s'),
                'stats' => $stats
            ], JSON_PRETTY_PRINT));
            
            $this->log("Stats synchronisées: " . json_encode($stats));
            return ['success' => true, 'stats' => $stats];
            
        } catch (Exception $e) {
            $this->log("Erreur sync stats: " . $e->getMessage(), 'ERROR');
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    public function getLogs($lines = 100) {
        if (!file_exists($this->logFile)) {
            return [];
        }
        
        $logContent = file($this->logFile);
        return array_slice(array_reverse($logContent), 0, $lines);
    }
}

// Traitement AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    $sync = new SyncManager($config);
    
    switch ($action) {
        case 'cleanup_duplicates':
            echo json_encode($sync->cleanupDuplicates());
            break;
            
        case 'validate_posters':
            echo json_encode($sync->validatePosters());
            break;
            
        case 'sync_stats':
            echo json_encode($sync->syncStats());
            break;
            
        case 'get_logs':
            $lines = (int) ($_POST['lines'] ?? 50);
            echo json_encode(['success' => true, 'logs' => $sync->getLogs($lines)]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Action inconnue']);
    }
    exit;
}

$sync = new SyncManager($config);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synchronisation & Monitoring - VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/phosphor-icons@2.0.0/src/css/icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="ph ph-arrows-clockwise me-2"></i>
                Sync & Monitoring
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="batch.php">Traitement en lot</a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <div class="row">
            <!-- Outils de maintenance -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ph ph-wrench me-2"></i>
                            Outils de maintenance
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <button onclick="runTask('cleanup_duplicates')" class="btn btn-warning w-100">
                                    <i class="ph ph-trash me-2"></i>
                                    Nettoyer doublons
                                </button>
                                <small class="text-muted d-block mt-1">
                                    Supprime les doublons basés sur TMDB ID
                                </small>
                            </div>
                            <div class="col-md-4">
                                <button onclick="runTask('validate_posters')" class="btn btn-info w-100">
                                    <i class="ph ph-image me-2"></i>
                                    Valider posters
                                </button>
                                <small class="text-muted d-block mt-1">
                                    Vérifie la validité des URLs d'images
                                </small>
                            </div>
                            <div class="col-md-4">
                                <button onclick="runTask('sync_stats')" class="btn btn-success w-100">
                                    <i class="ph ph-chart-bar me-2"></i>
                                    Sync statistiques
                                </button>
                                <small class="text-muted d-block mt-1">
                                    Met à jour les stats système
                                </small>
                            </div>
                        </div>
                        
                        <div id="taskProgress" class="mt-3" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                            </div>
                            <small class="text-muted">Tâche en cours...</small>
                        </div>
                        
                        <div id="taskResult" class="mt-3"></div>
                    </div>
                </div>
                
                <!-- Planification automatique -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ph ph-calendar me-2"></i>
                            Planification automatique
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">
                            Pour automatiser ces tâches, ajoutez ces commandes à votre crontab :
                        </p>
                        <pre class="bg-light p-3 rounded"><code># Nettoyage quotidien à 2h du matin
0 2 * * * curl -X POST <?= $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] ?> -d "action=cleanup_duplicates"

# Validation des posters toutes les 6h
0 */6 * * * curl -X POST <?= $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] ?> -d "action=validate_posters"

# Sync des stats toutes les heures
0 * * * * curl -X POST <?= $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] ?> -d "action=sync_stats"</code></pre>
                    </div>
                </div>
            </div>
            
            <!-- Logs système -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="ph ph-list me-2"></i>
                            Logs système
                        </h6>
                        <button onclick="refreshLogs()" class="btn btn-sm btn-outline-primary">
                            <i class="ph ph-arrow-clockwise"></i>
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div id="systemLogs" style="height: 400px; overflow-y: auto; font-family: monospace; font-size: 0.8rem;">
                            <div class="p-3 text-center text-muted">
                                <i class="ph ph-circle-notch ph-spin"></i>
                                Chargement des logs...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function runTask(taskName) {
            const progress = document.getElementById('taskProgress');
            const result = document.getElementById('taskResult');
            
            progress.style.display = 'block';
            result.innerHTML = '';
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=${taskName}`
            })
            .then(response => response.json())
            .then(data => {
                progress.style.display = 'none';
                
                if (data.success) {
                    let message = '';
                    
                    switch (taskName) {
                        case 'cleanup_duplicates':
                            message = `Nettoyage terminé: ${data.cleaned} doublons supprimés dans ${data.groups} groupes`;
                            break;
                        case 'validate_posters':
                            message = `Validation terminée: ${data.checked} vérifiés, ${data.broken} cassés trouvés`;
                            break;
                        case 'sync_stats':
                            message = `Statistiques synchronisées avec succès`;
                            break;
                    }
                    
                    result.innerHTML = `
                        <div class="alert alert-success">
                            <i class="ph ph-check-circle me-2"></i>
                            ${message}
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="ph ph-x-circle me-2"></i>
                            Erreur: ${data.error}
                        </div>
                    `;
                }
                
                // Rafraîchir les logs après chaque tâche
                refreshLogs();
            })
            .catch(error => {
                progress.style.display = 'none';
                result.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="ph ph-x-circle me-2"></i>
                        Erreur lors de l'exécution de la tâche
                    </div>
                `;
            });
        }
        
        function refreshLogs() {
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=get_logs&lines=50'
            })
            .then(response => response.json())
            .then(data => {
                const logsContainer = document.getElementById('systemLogs');
                
                if (data.success && data.logs.length > 0) {
                    const logHtml = data.logs.map(log => {
                        const trimmedLog = log.trim();
                        let className = 'text-muted';
                        
                        if (trimmedLog.includes('[ERROR]')) {
                            className = 'text-danger';
                        } else if (trimmedLog.includes('[WARN]')) {
                            className = 'text-warning';
                        } else if (trimmedLog.includes('[INFO]')) {
                            className = 'text-info';
                        }
                        
                        return `<div class="px-3 py-1 border-bottom ${className}">${trimmedLog}</div>`;
                    }).join('');
                    
                    logsContainer.innerHTML = logHtml;
                } else {
                    logsContainer.innerHTML = '<div class="p-3 text-center text-muted">Aucun log disponible</div>';
                }
                
                // Faire défiler vers le bas
                logsContainer.scrollTop = logsContainer.scrollHeight;
            })
            .catch(error => {
                document.getElementById('systemLogs').innerHTML = '<div class="p-3 text-center text-danger">Erreur lors du chargement des logs</div>';
            });
        }
        
        // Charger les logs au démarrage
        document.addEventListener('DOMContentLoaded', refreshLogs);
        
        // Rafraîchir les logs toutes les 30 secondes
        setInterval(refreshLogs, 30000);
    </script>
</body>
</html> 