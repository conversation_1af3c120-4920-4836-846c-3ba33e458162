<?php
// Script de diagnostic pour les URLs M3U

require_once 'config.php';

function testUrl($url, $verbose = true) {
    if ($verbose) echo "🧪 Test de l'URL: " . substr($url, 0, 100) . "...\n";
    
    // 1. Validation de base
    if (empty($url)) {
        echo "❌ URL vide\n";
        return false;
    }
    
    $url = trim($url);
    
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        echo "❌ URL mal formée selon filter_var: '$url'\n";
        return false;
    }
    
    if (!preg_match('/^https?:\/\//', $url)) {
        echo "❌ URL ne commence pas par http/https: '$url'\n";
        return false;
    }
    
    if ($verbose) echo "✅ Validation de base réussie\n";
    
    // 2. Test de résolution DNS
    $parsedUrl = parse_url($url);
    $host = $parsedUrl['host'] ?? null;
    
    if (!$host) {
        echo "❌ Impossible d'extraire le hostname de l'URL\n";
        return false;
    }
    
    if ($verbose) echo "🌐 Test de résolution DNS pour: $host\n";
    $ip = gethostbyname($host);
    if ($ip === $host) {
        echo "❌ Résolution DNS échouée pour: $host\n";
        return false;
    }
    
    if ($verbose) echo "✅ DNS résolu: $host -> $ip\n";
    
    // 3. Test cURL simple
    if ($verbose) echo "🔄 Test cURL...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (compatible; M3U-Tester/1.0)");
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request pour tester sans télécharger
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ Erreur cURL: $error\n";
        if ($verbose) {
            echo "🔍 Info cURL:\n";
            echo "   - URL effective: " . ($info['url'] ?? 'N/A') . "\n";
            echo "   - Code HTTP: " . ($info['http_code'] ?? 'N/A') . "\n";
            echo "   - Temps total: " . ($info['total_time'] ?? 'N/A') . "s\n";
        }
        return false;
    }
    
    if ($httpCode !== 200) {
        echo "❌ Code HTTP non-200: $httpCode\n";
        if ($verbose) {
            echo "🔍 Informations de la réponse:\n";
            echo "   - URL finale: " . ($info['url'] ?? 'N/A') . "\n";
            echo "   - Content-Type: " . ($info['content_type'] ?? 'N/A') . "\n";
            echo "   - Taille: " . ($info['download_content_length'] ?? 'N/A') . " octets\n";
        }
        return false;
    }
    
    if ($verbose) {
        echo "✅ Test cURL réussi (HTTP $httpCode)\n";
        echo "🔍 Informations détaillées:\n";
        echo "   - URL finale: " . ($info['url'] ?? 'N/A') . "\n";
        echo "   - Content-Type: " . ($info['content_type'] ?? 'N/A') . "\n";
        echo "   - Taille annoncée: " . ($info['download_content_length'] ?? 'N/A') . " octets\n";
        echo "   - Temps de connexion: " . ($info['connect_time'] ?? 'N/A') . "s\n";
        echo "   - Temps total: " . ($info['total_time'] ?? 'N/A') . "s\n";
    }
    
    // 4. Test de téléchargement d'un échantillon
    if ($verbose) echo "📥 Test de téléchargement d'un échantillon...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (compatible; M3U-Tester/1.0)");
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
    curl_setopt($ch, CURLOPT_RANGE, "0-1023"); // Premier 1KB seulement
    
    $content = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ Erreur lors du téléchargement d'échantillon: $error\n";
        return false;
    }
    
    if ($httpCode !== 200 && $httpCode !== 206) { // 206 = Partial Content
        echo "❌ Code HTTP inattendu lors du téléchargement: $httpCode\n";
        return false;
    }
    
    if (empty($content)) {
        echo "❌ Contenu vide reçu\n";
        return false;
    }
    
    if ($verbose) {
        echo "✅ Échantillon téléchargé: " . strlen($content) . " octets\n";
        echo "🔍 Aperçu du contenu:\n";
        echo "---\n";
        echo substr($content, 0, 300) . "\n";
        echo "---\n";
        
        // Vérifier si ça ressemble à du M3U
        if (preg_match('/^#EXTM3U/i', $content)) {
            echo "✅ Format M3U détecté (#EXTM3U trouvé)\n";
        } elseif (preg_match('/#EXTINF/i', $content)) {
            echo "⚠️ Contenu M3U possible (#EXTINF trouvé mais pas de #EXTM3U)\n";
        } else {
            echo "❌ Ne ressemble pas à du M3U\n";
        }
    }
    
    return true;
}

echo "🧪 Diagnostic des URLs M3U\n";
echo "==========================\n\n";

try {
    $config = require_once 'config.php';
    
    // Test des URLs configurées
    $urls = [
        'IPTV1' => $config['update_iptv1_url'] ?? null,
        'IPTV2' => $config['update_iptv2_url'] ?? null,
        'Test' => 'https://raw.githubusercontent.com/iptv-org/iptv/master/streams/fr.m3u'
    ];
    
    foreach ($urls as $name => $url) {
        echo "📡 Test de $name:\n";
        echo "================\n";
        
        if (empty($url)) {
            echo "❌ URL non configurée pour $name\n\n";
            continue;
        }
        
        $success = testUrl($url, true);
        echo ($success ? "✅ Test global réussi" : "❌ Test global échoué") . "\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur lors du test: " . $e->getMessage() . "\n";
}

echo "💡 Conseils de dépannage:\n";
echo "- Vérifiez que les URLs sont correctement formatées\n";
echo "- Testez les URLs dans un navigateur\n";
echo "- Vérifiez les paramètres réseau/firewall\n";
echo "- Contactez le fournisseur IPTV si les URLs ne fonctionnent pas\n";
?> 