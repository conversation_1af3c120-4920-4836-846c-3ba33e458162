<?php
// Configuration du système VOD IPTV

// Fonction pour charger les variables d'environnement du fichier .env
if (!function_exists('loadEnvVars')) {
function loadEnvVars($envFile = null) {
    if ($envFile === null) {
        $envFile = dirname(__DIR__, 2) . '/.env'; // Remonte de 2 niveaux depuis public/vod/
    }
    
    if (!file_exists($envFile)) {
        throw new Exception("Fichier .env non trouvé : {$envFile}");
    }
    
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $envVars = [];
    
    foreach ($lines as $line) {
        // Ignorer les commentaires
        if (strpos($line, '#') === 0) {
            continue;
        }
        
        // Diviser la ligne en clé=valeur
        $parts = explode('=', $line, 2);
        if (count($parts) === 2) {
            $key = trim($parts[0]);
            $value = trim($parts[1]);
            
            // Enlever les guillemets si présents
            if (preg_match('/^([\'"])(.*)\1$/', $value, $matches)) {
                $value = $matches[2];
            }
            
            $envVars[$key] = $value;
        }
    }
    
    return $envVars;
}
}

// Charger les variables d'environnement
$env = loadEnvVars();

// Fonction helper pour récupérer une variable d'environnement OBLIGATOIRE
if (!function_exists('envRequired')) {
function envRequired($key) {
    global $env;
    if (!isset($env[$key]) || empty($env[$key])) {
        throw new Exception("Variable d'environnement obligatoire manquante : {$key}");
    }
    return $env[$key];
}
}

// Fonction helper pour récupérer une variable d'environnement avec valeur par défaut
if (!function_exists('env')) {
function env($key, $default = null) {
    global $env;
    return isset($env[$key]) && !empty($env[$key]) ? $env[$key] : $default;
}
}

return [
    // Base de données principale - Variables OBLIGATOIRES
    'main_db' => [
        'host' => envRequired('DB_HOST'),
        'dbname' => envRequired('DB_DATABASE'),
        'username' => envRequired('DB_USERNAME'),
        'password' => env('DB_PASSWORD', '') // Mot de passe peut être vide
    ],
    
    // Base de données IPTV principale - Variables OBLIGATOIRES
    'iptv_db' => [
        'host' => envRequired('IPTV_DB_HOST'),
        'dbname' => envRequired('IPTV_DB_DATABASE'),
        'username' => envRequired('IPTV_DB_USERNAME'),
        'password' => env('IPTV_DB_PASSWORD', '') // Mot de passe peut être vide
    ],
    
    // Base de données IPTV secondaire - Variables OBLIGATOIRES
    'iptv_db2' => [
        'host' => envRequired('IPTV2_DB_HOST'),
        'dbname' => envRequired('IPTV2_DB_DATABASE'),
        'username' => envRequired('IPTV2_DB_USERNAME'),
        'password' => env('IPTV2_DB_PASSWORD', '') // Mot de passe peut être vide
    ],
    
    // Clé API TMDB - Optionnelle avec avertissement
    'tmdb_api_key' => env('TMDB_API_KEY', ''),
    
    // Bearer Token TMDB - Pour les nouvelles API
    'tmdb_bearer_token' => env('TMDB_BEARER_TOKEN', ''),
    
    // Options de recherche
    'search_limit' => (int) env('SEARCH_LIMIT', 50),
    
    // Options d'affichage
    'results_per_row' => 3, // lg-4 = 3 par ligne
    'show_debug_info' => env('APP_DEBUG', false),

    'app_url' => env('APP_URL', 'http://localhost:8000'),

    'iptv_stream_url_base' => env('IPTV_STREAM_URL_BASE', ''),
    
    // URLs de base
    'tmdb_image_base' => env('TMDB_IMAGE_BASE', 'https://image.tmdb.org/t/p/w500'),
    'tmdb_api_base' => env('TMDB_API_BASE', 'https://api.themoviedb.org/3'),

    'update_iptv1_url' => env('UPDATE_IPTV1_URL', ''),
    'update_iptv2_url' => env('UPDATE_IPTV2_URL', ''),
]; 