<?php
// Endpoint dédié pour les requêtes AJAX du système VOD IPTV

// Headers pour les réponses JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Charger la configuration
try {
    $config = require_once 'config.php';
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erreur de configuration: ' . $e->getMessage()]);
    exit;
}

// Fonction de connexion (copie de search.php)
function getConnection($dbConfig) {
    try {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    } catch (PDOException $e) {
        throw new Exception("Erreur de connexion à {$dbConfig['dbname']}: " . $e->getMessage());
    }
}

// Traitement des requêtes POST uniquement
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
    exit;
}

$action = $_POST['action'] ?? '';

if ($action === 'get_movie_source') {
    $movieId = $_POST['movie_id'] ?? '';
    $sourceDb = $_POST['source_db'] ?? '';
    
    // Debug: Log de la requête reçue
    error_log("🎬 AJAX get_movie_source appelé - movieId: $movieId, sourceDb: $sourceDb");
    
    if (!$movieId || !$sourceDb) {
        error_log("❌ Paramètres manquants - movieId: '$movieId', sourceDb: '$sourceDb'");
        echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
        exit;
    }
    
    try {
        error_log("🔍 Tentative de connexion à la base: $sourceDb");
        $iptvPdo = getConnection($config[$sourceDb]);
        error_log("✅ Connexion réussie à $sourceDb");
        
        // Chercher l'URL dans source_iptv
        error_log("🔍 Recherche URL pour poster_iptv_id: $movieId");
        $stmt = $iptvPdo->prepare("SELECT url FROM source_iptv WHERE poster_iptv_id = ?");
        $stmt->execute([$movieId]);
        $source = $stmt->fetch();
        error_log("📊 Résultat requête: " . ($source ? "URL trouvée: " . substr($source['url'], 0, 50) . "..." : "Aucune URL"));
        
        // Debug supplémentaire : vérifier si l'ID existe dans la base
        $stmt = $iptvPdo->prepare("SELECT COUNT(*) as count FROM source_iptv WHERE poster_iptv_id = ?");
        $stmt->execute([$movieId]);
        $count = $stmt->fetch()['count'];
        error_log("🔢 Nombre d'enregistrements trouvés pour poster_iptv_id $movieId: $count");
        
        if ($source && !empty($source['url'])) {
            // Reconstruire l'URL avec le préfixe du config
            $originalUrl = $source['url'];
            
            // Extraire le nom du fichier de l'URL originale
            $pathParts = explode('/', $originalUrl);
            $fileName = end($pathParts); // Dernier élément = nom du fichier
            
            // Construire la nouvelle URL
            $streamUrlBase = $config['iptv_stream_url_base'] ?? 'https://server.flashfilms-box.top/private.php?file=';
            $newUrl = $streamUrlBase . $fileName;
            
            error_log("✅ URL reconstruite avec succès: $newUrl");
            
            echo json_encode([
                'success' => true, 
                'url' => $newUrl,
                'original_url' => $originalUrl, // Pour debug
                'filename' => $fileName, // Pour debug
                'message' => 'Source trouvée et URL reconstruite'
            ]);
        } else {
            error_log("❌ Aucune source trouvée pour poster_iptv_id: $movieId");
            echo json_encode([
                'success' => false, 
                'message' => 'Aucune source vidéo trouvée pour ce film'
            ]);
        }
    } catch (Exception $e) {
        error_log("❌ Exception dans AJAX get_movie_source: " . $e->getMessage());
        error_log("🔍 Stack trace: " . $e->getTraceAsString());
        echo json_encode([
            'success' => false, 
            'message' => 'Erreur lors de la recherche de la source : ' . $e->getMessage()
        ]);
    }
    exit;
}

// Action non reconnue
echo json_encode(['success' => false, 'message' => 'Action non reconnue: ' . $action]);
exit;
?> 