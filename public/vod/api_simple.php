<?php
// API simple pour Import M3U V2 - sans conflits
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

error_reporting(E_ALL);
ini_set('display_errors', 0);

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

function jsonResponse($data, $httpCode = 200) {
    http_response_code($httpCode);
    echo json_encode($data);
    exit;
}

function jsonError($message, $httpCode = 400) {
    jsonResponse(['success' => false, 'error' => $message], $httpCode);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonError('Méthode non autorisée', 405);
}

if (!isset($_POST['action'])) {
    jsonError('Action manquante');
}

$action = $_POST['action'];

switch ($action) {
    case 'get_stats':
        if (!isset($_SESSION['m3u_data'])) {
            jsonError('Pas de données en session');
        }
        
        $channels = $_SESSION['m3u_data']['channels'];
        $stats = [
            'total_channels' => count($channels),
            'by_type' => ['movie' => 0, 'series' => 0, 'tv' => 0, 'unknown' => 0],
            'by_year' => [],
            'by_group' => [],
            'series_details' => ['total_series' => 0, 'total_seasons' => 0, 'total_episodes' => 0],
            'top_groups' => [],
            'recent_years' => [],
            'url_analysis' => ['extensions' => [], 'domains' => []]
        ];
        
        foreach ($channels as $channel) {
            $stats['by_type'][$channel['type']]++;
            
            if ($channel['year']) {
                if (!isset($stats['by_year'][$channel['year']])) {
                    $stats['by_year'][$channel['year']] = 0;
                }
                $stats['by_year'][$channel['year']]++;
            }
            
            $groupName = $channel['group_title'] ?: 'Non défini';
            if (!isset($stats['by_group'][$groupName])) {
                $stats['by_group'][$groupName] = 0;
            }
            $stats['by_group'][$groupName]++;
            
            if ($channel['type'] === 'series') {
                $stats['series_details']['total_series']++;
                if ($channel['season']) {
                    $stats['series_details']['total_episodes']++;
                }
            }
        }
        
        arsort($stats['by_group']);
        $stats['top_groups'] = array_slice($stats['by_group'], 0, 10, true);
        
        krsort($stats['by_year']);
        $stats['recent_years'] = array_slice($stats['by_year'], 0, 10, true);
        
        $stats['series_details']['total_seasons'] = count(array_unique(array_column(array_filter($channels, function($c) { return $c['type'] === 'series' && $c['season']; }), 'season')));
        
        jsonResponse(['success' => true, 'stats' => $stats]);
        break;
        
    case 'search':
        if (!isset($_SESSION['m3u_data'])) {
            jsonError('Pas de données en session');
        }
        
        $channels = $_SESSION['m3u_data']['channels'];
        $query = $_POST['query'] ?? '';
        $type = $_POST['type'] ?? 'all';
        $yearFrom = !empty($_POST['yearFrom']) ? intval($_POST['yearFrom']) : null;
        $yearTo = !empty($_POST['yearTo']) ? intval($_POST['yearTo']) : null;
        $group = $_POST['group'] ?? '';
        $season = !empty($_POST['season']) ? intval($_POST['season']) : null;
        $episode = !empty($_POST['episode']) ? intval($_POST['episode']) : null;
        $urlFilter = $_POST['urlFilter'] ?? '';
        
        $results = [];
        $stats = [
            'total_searched' => count($channels),
            'matches_found' => 0,
            'by_type' => ['movie' => 0, 'series' => 0, 'tv' => 0, 'unknown' => 0],
            'by_year' => [],
            'by_group' => []
        ];
        
        foreach ($channels as $channel) {
            $match = true;
            
            if ($type !== 'all' && $channel['type'] !== $type) {
                $match = false;
            }
            
            if (!empty($query) && $match) {
                $searchText = strtolower($channel['title'] . ' ' . $channel['tvg_name'] . ' ' . $channel['group_title']);
                if (strpos($searchText, strtolower($query)) === false) {
                    $match = false;
                }
            }
            
            if ($match && ($yearFrom !== null || $yearTo !== null)) {
                if ($channel['year'] === null) {
                    $match = false;
                } else {
                    if ($yearFrom !== null && $channel['year'] < $yearFrom) $match = false;
                    if ($yearTo !== null && $channel['year'] > $yearTo) $match = false;
                }
            }
            
            if ($match && !empty($group) && stripos($channel['group_title'], $group) === false) {
                $match = false;
            }
            
            if ($match && $season !== null && $channel['season'] !== $season) {
                $match = false;
            }
            
            if ($match && $episode !== null && $channel['episode'] !== $episode) {
                $match = false;
            }
            
            if ($match && !empty($urlFilter) && stripos($channel['url'], $urlFilter) === false) {
                $match = false;
            }
            
            if ($match) {
                $results[] = $channel;
                $stats['matches_found']++;
                $stats['by_type'][$channel['type']]++;
            }
        }
        
        usort($results, function($a, $b) {
            return strcmp($a['title'], $b['title']);
        });
        
        jsonResponse([
            'success' => true,
            'results' => $results,
            'total' => count($results),
            'stats' => $stats
        ]);
        break;
        
    case 'get_channels':
        if (isset($_SESSION['m3u_data'])) {
            jsonResponse([
                'success' => true,
                'channels' => $_SESSION['m3u_data']['channels'],
                'total' => $_SESSION['m3u_data']['total_channels']
            ]);
        } else {
            jsonError('Pas de données en session');
        }
        break;
        
    case 'get_progress':
        $type = $_POST['type'] ?? 'filter';
        $progress_file = __DIR__ . '/temp/' . $type . '_progress.json';
        
        if (file_exists($progress_file)) {
            $progress = json_decode(file_get_contents($progress_file), true);
            jsonResponse($progress);
        } else {
            jsonResponse(null);
        }
        break;
        
    case 'cleanup':
        $temp_dir = __DIR__ . '/temp/';
        if (is_dir($temp_dir)) {
            $files = glob($temp_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
        unset($_SESSION['m3u_data']);
        jsonResponse(['success' => true]);
        break;
        
    default:
        jsonError('Action inconnue: ' . $action);
}
?> 