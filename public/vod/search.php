<?php
// Système standalone de recherche VOD IPTV
// Recherche dans iptv_db et iptv_db2, affiche les résultats non présents dans le système principal

// Charger la configuration
$config = require_once 'config.php';

// Fonctions utilitaires
function getConnection($dbConfig) {
    try {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    } catch (PDOException $e) {
        die("Erreur de connexion à {$dbConfig['dbname']}: " . $e->getMessage());
    }
}

function searchInIptvDb($pdo, $query, $dbName, $limit) {
    $limit = (int) $limit; // S'assurer que c'est un entier
    $sql = "SELECT id, title, type, year, tmdb_id, poster_path, poster_image, 
                   created_at, updated_at, ? as source_db
            FROM poster_iptv 
            WHERE title LIKE ? OR tmdb_id LIKE ?
            LIMIT {$limit}";
    
    $stmt = $pdo->prepare($sql);
    $searchTerm = "%{$query}%";
    $stmt->execute([$dbName, $searchTerm, $searchTerm]);
    
    return $stmt->fetchAll();
}

function searchInMainDb($pdo, $query, $limit) {
    $limit = (int) $limit;
    $sql = "SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, 
                   poster_url as poster_path, thumbnail_url as poster_image, 
                   created_at, updated_at, 'main_db' as source_db
            FROM entertainments 
            WHERE (name LIKE ? OR tmdb_id LIKE ?) AND deleted_at IS NULL
            ORDER BY created_at DESC
            LIMIT {$limit}";
    
    $stmt = $pdo->prepare($sql);
    $searchTerm = "%{$query}%";
    $stmt->execute([$searchTerm, $searchTerm]);
    
    return $stmt->fetchAll();
}

function getSpecialQuery($pdo, $queryType, $dbName, $limit) {
    $limit = (int) $limit;
    
    if ($dbName === 'main_db') {
        // Requêtes pour la base principale (entertainments)
        switch ($queryType) {
            case 'no_tmdb':
                $sql = "SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, 
                               poster_url as poster_path, thumbnail_url as poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM entertainments 
                        WHERE (tmdb_id IS NULL OR tmdb_id = '' OR tmdb_id = '0') AND deleted_at IS NULL
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
            case 'invalid_tmdb':
                $sql = "SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, 
                               poster_url as poster_path, thumbnail_url as poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM entertainments 
                        WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'
                          AND (tmdb_id NOT REGEXP '^[0-9]+$' OR LENGTH(tmdb_id) < 2) AND deleted_at IS NULL
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
            case 'recent':
                $sql = "SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, 
                               poster_url as poster_path, thumbnail_url as poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM entertainments 
                        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) AND deleted_at IS NULL
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
            case 'no_poster':
                $sql = "SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, 
                               poster_url as poster_path, thumbnail_url as poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM entertainments 
                        WHERE (poster_url IS NULL OR poster_url = '') 
                          AND (thumbnail_url IS NULL OR thumbnail_url = '') AND deleted_at IS NULL
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
            case 'movies_only':
                $sql = "SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, 
                               poster_url as poster_path, thumbnail_url as poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM entertainments 
                        WHERE type = 'movie' AND deleted_at IS NULL
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
                                        case 'series_only':
                                $sql = "SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, 
                                               poster_url as poster_path, thumbnail_url as poster_image, 
                                               created_at, updated_at, ? as source_db
                                        FROM entertainments 
                                        WHERE type = 'tvshow' AND deleted_at IS NULL
                                        ORDER BY created_at DESC
                                        LIMIT {$limit}";
                                break;
                                
                            case 'title_mismatch':
                                $sql = "SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, 
                                               poster_url as poster_path, thumbnail_url as poster_image, 
                                               created_at, updated_at, ? as source_db
                                        FROM entertainments 
                                        WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0' AND deleted_at IS NULL
                                        ORDER BY created_at DESC
                                        LIMIT {$limit}";
                                break;
                                
                            default:
                                return [];
                        }
    } else {
        // Requêtes pour les bases IPTV (existantes)
        switch ($queryType) {
            case 'no_tmdb':
                $sql = "SELECT id, title, type, year, tmdb_id, poster_path, poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM poster_iptv 
                        WHERE tmdb_id IS NULL OR tmdb_id = '' OR tmdb_id = '0'
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
            case 'invalid_tmdb':
                $sql = "SELECT id, title, type, year, tmdb_id, poster_path, poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM poster_iptv 
                        WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'
                          AND (tmdb_id NOT REGEXP '^[0-9]+$' OR LENGTH(tmdb_id) < 2)
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
            case 'recent':
                $sql = "SELECT id, title, type, year, tmdb_id, poster_path, poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM poster_iptv 
                        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
            case 'no_poster':
                $sql = "SELECT id, title, type, year, tmdb_id, poster_path, poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM poster_iptv 
                        WHERE (poster_path IS NULL OR poster_path = '') 
                          AND (poster_image IS NULL OR poster_image = '')
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
            case 'movies_only':
                $sql = "SELECT id, title, type, year, tmdb_id, poster_path, poster_image, 
                               created_at, updated_at, ? as source_db
                        FROM poster_iptv 
                        WHERE type = 'movie'
                        ORDER BY created_at DESC
                        LIMIT {$limit}";
                break;
                
                                        case 'series_only':
                                $sql = "SELECT id, title, type, year, tmdb_id, poster_path, poster_image, 
                                               created_at, updated_at, ? as source_db
                                        FROM poster_iptv 
                                        WHERE type IN ('tvshow', 'series', 'tv')
                                        ORDER BY created_at DESC
                                        LIMIT {$limit}";
                                break;
                                
                            case 'title_mismatch':
                                $sql = "SELECT id, title, type, year, tmdb_id, poster_path, poster_image, 
                                               created_at, updated_at, ? as source_db
                                        FROM poster_iptv 
                                        WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'
                                        ORDER BY created_at DESC
                                        LIMIT {$limit}";
                                break;
                                
                            default:
                                return [];
                        }
    }
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$dbName]);
    
    return $stmt->fetchAll();
}

function checkExistsInMain($mainPdo, $tmdbId, $title) {
    // 1. Vérifier par TMDB ID (correspondance exacte)
    if ($tmdbId && $tmdbId != '0' && $tmdbId != '') {
        $stmt = $mainPdo->prepare("SELECT id, name, tmdb_id, type, YEAR(release_date) as year FROM entertainments WHERE tmdb_id = ? AND deleted_at IS NULL");
        $stmt->execute([$tmdbId]);
        $result = $stmt->fetch();
        if ($result) {
            return ['status' => 'tmdb', 'entertainment' => $result, 'title_matches' => []];
        }
    }
    
    // 2. Vérifier par titre (correspondance approximative)
    $titleMatches = [];
    if ($title && strlen(trim($title)) >= 3) {
        // Recherche exacte par titre
        $stmt = $mainPdo->prepare("SELECT id, name, tmdb_id, type, YEAR(release_date) as year FROM entertainments WHERE name = ? AND deleted_at IS NULL LIMIT 5");
        $stmt->execute([trim($title)]);
        $exactMatches = $stmt->fetchAll();
        
        // Recherche approximative par titre (LIKE)
        $stmt = $mainPdo->prepare("SELECT id, name, tmdb_id, type, YEAR(release_date) as year FROM entertainments WHERE name LIKE ? AND deleted_at IS NULL LIMIT 5");
        $stmt->execute(['%' . trim($title) . '%']);
        $likeMatches = $stmt->fetchAll();
        
        // Fusionner les résultats en évitant les doublons
        $allMatches = $exactMatches;
        foreach ($likeMatches as $match) {
            $found = false;
            foreach ($allMatches as $existing) {
                if ($existing['id'] == $match['id']) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $allMatches[] = $match;
            }
        }
        
        $titleMatches = array_slice($allMatches, 0, 5); // Limiter à 5 résultats
    }
    
    // Retourner le résultat avec les correspondances par titre
    if (!empty($titleMatches)) {
        return ['status' => 'title', 'entertainment' => null, 'title_matches' => $titleMatches];
    }
    
    // Aucune correspondance trouvée
    return ['status' => 'none', 'entertainment' => null, 'title_matches' => []];
}

function findTitleMismatches($mainPdo, $iptvPdo1, $iptvPdo2, $selectedDb, $limit) {
    $results = [];
    $limit = (int) $limit;
    
    try {
        // Récupérer les données selon la base sélectionnée
        $allItems = [];
        
        if ($selectedDb === 'all' || $selectedDb === 'main_db') {
            $mainResults = getSpecialQuery($mainPdo, 'title_mismatch', 'main_db', $limit);
            $allItems = array_merge($allItems, $mainResults);
        }
        
        if ($selectedDb === 'all' || $selectedDb === 'both' || $selectedDb === 'iptv_db') {
            $iptvResults = getSpecialQuery($iptvPdo1, 'title_mismatch', 'iptv_db', $limit);
            $allItems = array_merge($allItems, $iptvResults);
        }
        
        if ($selectedDb === 'all' || $selectedDb === 'both' || $selectedDb === 'iptv_db2') {
            $iptvResults2 = getSpecialQuery($iptvPdo2, 'title_mismatch', 'iptv_db2', $limit);
            $allItems = array_merge($allItems, $iptvResults2);
        }
        
        // Pour chaque élément, chercher les correspondances par titre dans les autres bases
        foreach ($allItems as $item) {
            if (empty($item['tmdb_id'])) continue;
            
            $matches = [];
            $sourceDb = $item['source_db'];
            
            // Déterminer le type standardisé pour la recherche
            $searchType = $item['type'];
            if (in_array($item['type'], ['tvshow', 'series', 'tv'])) {
                $searchType = 'tvshow'; // Type standardisé pour les séries
            }
            
            // Chercher dans la base principale si l'item ne vient pas de là
            if ($sourceDb !== 'main_db') {
                $stmt = $mainPdo->prepare("
                    SELECT id, name as title, type, YEAR(release_date) as year, tmdb_id, 'main_db' as db_source 
                    FROM entertainments 
                    WHERE name = ? AND tmdb_id != ? AND tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'
                    AND (type = ? OR (type = 'tvshow' AND ? IN ('tvshow', 'series', 'tv')))
                    AND deleted_at IS NULL
                ");
                $stmt->execute([$item['title'], $item['tmdb_id'], $searchType, $item['type']]);
                $mainMatches = $stmt->fetchAll();
                $matches = array_merge($matches, $mainMatches);
            }
            
            // Chercher dans IPTV DB 1 si l'item ne vient pas de là
            if ($sourceDb !== 'iptv_db') {
                $stmt = $iptvPdo1->prepare("
                    SELECT id, title, type, year, tmdb_id, 'iptv_db' as db_source 
                    FROM poster_iptv 
                    WHERE title = ? AND tmdb_id != ? AND tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'
                    AND (type = ? OR (type IN ('tvshow', 'series', 'tv') AND ? IN ('tvshow', 'series', 'tv')))
                ");
                $stmt->execute([$item['title'], $item['tmdb_id'], $item['type'], $searchType]);
                $iptv1Matches = $stmt->fetchAll();
                $matches = array_merge($matches, $iptv1Matches);
            }
            
            // Chercher dans IPTV DB 2 si l'item ne vient pas de là
            if ($sourceDb !== 'iptv_db2') {
                $stmt = $iptvPdo2->prepare("
                    SELECT id, title, type, year, tmdb_id, 'iptv_db2' as db_source 
                    FROM poster_iptv 
                    WHERE title = ? AND tmdb_id != ? AND tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0'
                    AND (type = ? OR (type IN ('tvshow', 'series', 'tv') AND ? IN ('tvshow', 'series', 'tv')))
                ");
                $stmt->execute([$item['title'], $item['tmdb_id'], $item['type'], $searchType]);
                $iptv2Matches = $stmt->fetchAll();
                $matches = array_merge($matches, $iptv2Matches);
            }
            
            // Si on a trouvé des correspondances, ajouter aux résultats
            if (!empty($matches)) {
                $item['title_matches'] = $matches;
                $item['match_count'] = count($matches);
                $results[] = $item;
            }
        }
        
    } catch (Exception $e) {
        error_log("Erreur dans findTitleMismatches: " . $e->getMessage());
    }
    
    return $results;
}

function extractYearFromTitle($title) {
    $title = trim($title);
    
    // Format 1: "Titre (Année)" ou "Titre(Année)"
    if (preg_match('/^(.+?)\s*\((\d{4})\)\s*$/', $title, $matches)) {
        return [
            'clean_title' => trim($matches[1]),
            'year' => (int)$matches[2]
        ];
    }
    

    // Format 3: "Titre - Année" ou "Titre -Année" ou "Titre- Année"
    if (preg_match('/^(.+?)\s*-\s*(\d{4})\s*$/', $title, $matches)) {
        return [
            'clean_title' => trim($matches[1]),
            'year' => (int)$matches[2]
        ];
    }
    
    // Format 4: "Titre Année" (année à la fin séparée par un espace)
    // Plus restrictif pour éviter les faux positifs
    if (preg_match('/^(.+)\s+(\d{4})\s*$/', $title, $matches)) {
        $potentialTitle = trim($matches[1]);
        $year = (int)$matches[2];
        
        // Validation: l'année doit être raisonnable (1900-2030)
        // et le titre doit faire au moins 2 caractères
        if ($year >= 1900 && $year <= 2030 && strlen($potentialTitle) >= 2) {
            return [
                'clean_title' => $potentialTitle,
                'year' => $year
            ];
        }
    }
    
    // Si aucun format ne correspond, retourner le titre original
    return [
        'clean_title' => $title,
        'year' => null
    ];
}

function fixTitlesWithYear($pdo, $dbName, $limit = 100) {
    $results = [
        'processed' => 0,
        'updated' => 0,
        'errors' => []
    ];
    
    try {
        // S'assurer que la limite est un entier sécurisé
        $limit = (int) $limit;
        if ($limit <= 0 || $limit > 1000) {
            $limit = 100; // Valeur par défaut sécurisée
        }
        
        // Rechercher tous les titres contenant des années dans différents formats
        // Format 1: (Année), Format 2: - Année, Format 3: Titre Année
        $sql = "SELECT id, title, year FROM poster_iptv 
                WHERE title REGEXP '\\\\([0-9]{4}\\\\)|\\s-\\s[0-9]{4}\\s*$|\\s[0-9]{4}\\s*$'
                LIMIT {$limit}";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $items = $stmt->fetchAll();
        
        foreach ($items as $item) {
            $results['processed']++;
            
            $extracted = extractYearFromTitle($item['title']);
            
            // Debug: ajouter des informations sur ce qui est traité
            $originalTitle = $item['title'];
            $cleanTitle = $extracted['clean_title'];
            $extractedYear = $extracted['year'];
            
            // Si on a extrait une année et que le titre a changé
            if ($extracted['year'] && $extracted['clean_title'] !== $item['title']) {
                $updateSql = "UPDATE poster_iptv SET title = ?, year = ? WHERE id = ?";
                $updateStmt = $pdo->prepare($updateSql);
                
                if ($updateStmt->execute([$extracted['clean_title'], $extracted['year'], $item['id']])) {
                    $results['updated']++;
                } else {
                    $results['errors'][] = "Erreur mise à jour ID {$item['id']}: " . implode(', ', $updateStmt->errorInfo());
                }
            } else {
                // Debug: expliquer pourquoi l'item n'a pas été mis à jour
                if (!$extracted['year']) {
                    $results['errors'][] = "ID {$item['id']} '{$originalTitle}': Aucune année extraite";
                } elseif ($extracted['clean_title'] === $item['title']) {
                    $results['errors'][] = "ID {$item['id']} '{$originalTitle}': Titre inchangé après nettoyage";
                }
            }
        }
        
    } catch (Exception $e) {
        $results['errors'][] = "Erreur générale: " . $e->getMessage();
    }
    
    return $results;
}

function searchTmdb($title, $type, $bearerToken, $apiBase) {
    // Nettoyer le titre avant la recherche TMDB
    $extracted = extractYearFromTitle($title);
    $cleanTitle = $extracted['clean_title'];
    
    $searchType = ($type === 'tvshow' || $type === 'series') ? 'tv' : 'movie';
    $url = "{$apiBase}/search/{$searchType}?include_adult=false&language=fr-FR&page=1&query=" . urlencode($cleanTitle);
    
    // Utiliser cURL avec Bearer token
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $bearerToken,
        'accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false || $httpCode !== 200) {
        error_log("Erreur TMDB API: HTTP {$httpCode} - {$response}");
        return [];
    }
    
    $data = json_decode($response, true);
    $results = $data['results'] ?? [];
    
    // Enrichir les résultats avec des détails supplémentaires (limiter aux 5 premiers pour éviter trop d'appels API)
    $enrichedResults = [];
    $count = 0;
    
    foreach ($results as $result) {
        if ($count >= 5) break; // Limiter pour éviter trop d'appels API
        
        // Récupérer les détails complets
        $detailUrl = "{$apiBase}/{$searchType}/{$result['id']}?language=fr-FR";
        
        $detailCh = curl_init();
        curl_setopt($detailCh, CURLOPT_URL, $detailUrl);
        curl_setopt($detailCh, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($detailCh, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $bearerToken,
            'accept: application/json'
        ]);
        curl_setopt($detailCh, CURLOPT_TIMEOUT, 10);
        
        $detailResponse = curl_exec($detailCh);
        $detailHttpCode = curl_getinfo($detailCh, CURLINFO_HTTP_CODE);
        curl_close($detailCh);
        
        if ($detailResponse !== false && $detailHttpCode === 200) {
            $detailData = json_decode($detailResponse, true);
            
            // Fusionner les données de base avec les détails
            $result = array_merge($result, [
                'runtime' => $detailData['runtime'] ?? null,
                'episode_run_time' => $detailData['episode_run_time'] ?? null,
                'genres' => $detailData['genres'] ?? [],
                'production_companies' => $detailData['production_companies'] ?? [],
                'imdb_id' => $detailData['imdb_id'] ?? null,
                'overview' => $detailData['overview'] ?? $result['overview'] ?? ''
            ]);
        }
        
        $enrichedResults[] = $result;
        $count++;
    }
    
    return $enrichedResults;
}

function addToMain($mainPdo, $iptvPdo, $tmdbData, $iptvData, $iptvId) {
    try {
        // Mapping des genres TMDB vers les IDs de votre système
        $genreMapping = [
            28 => 7, 12 => 8, 16 => 12, 35 => 10, 80 => 9, 99 => 11, 18 => 4, 10751 => 2, 14 => 13, 36 => 15, 27 => 16, 10402 => 21, 9648 => 28, 10749 => 5, 878 => 6, 10770 => 31, 53 => 1, 10752 => 3, 37 => 36,
            10759 => 51, 10765 => 52, 10766 => 47, 10767 => 48, 10768 => 49, 10763 => 50, 10764 => 38, 10762 => 41
        ];
        
        $type = ($tmdbData['media_type'] === 'tv' || $iptvData['type'] === 'tvshow' || $iptvData['type'] === 'series') ? 'tvshow' : 'movie';
        
        // Construire les URLs d'images
        $posterUrl = !empty($tmdbData['poster_path']) ? 
            'https://image.tmdb.org/t/p/w500' . $tmdbData['poster_path'] : null;
        $thumbnailUrl = !empty($tmdbData['backdrop_path']) ? 
            'https://image.tmdb.org/t/p/w780' . $tmdbData['backdrop_path'] : null;
        
        // Récupérer la durée pour les films
        $duration = null;
        if ($type === 'movie' && !empty($tmdbData['runtime'])) {
            $hours = floor($tmdbData['runtime'] / 60);
            $minutes = $tmdbData['runtime'] % 60;
            $duration = sprintf('%02d:%02d', $hours, $minutes);
        }
        
        // Description
        $description = $tmdbData['overview'] ?? '';
        
        // Rating IMDB (utiliser vote_average de TMDB) - Format x.x
        $imdbRating = null;
        if (!empty($tmdbData['vote_average'])) {
            $imdbRating = number_format((float)$tmdbData['vote_average'], 1);
        }
        
        // Date de sortie
        $releaseDate = null;
        if ($type === 'movie' && !empty($tmdbData['release_date'])) {
            $releaseDate = $tmdbData['release_date'];
        } elseif ($type === 'tvshow' && !empty($tmdbData['first_air_date'])) {
            $releaseDate = $tmdbData['first_air_date'];
        }
        
        // Titre selon le type
        $title = $type === 'tvshow' ? 
            ($tmdbData['name'] ?? $tmdbData['title'] ?? $iptvData['title']) : 
            ($tmdbData['title'] ?? $tmdbData['name'] ?? $iptvData['title']);
        
        // ÉTAPE 1: Mettre à jour la base IPTV source avec le TMDB ID, poster et year
        $year = null;
        if ($type === 'movie' && !empty($tmdbData['release_date'])) {
            $year = date('Y', strtotime($tmdbData['release_date']));
        } elseif ($type === 'tvshow' && !empty($tmdbData['first_air_date'])) {
            $year = date('Y', strtotime($tmdbData['first_air_date']));
        }
        
        $updateSql = "UPDATE poster_iptv SET tmdb_id = ?, poster_path = ?, year = ? WHERE id = ?";
        $updateStmt = $iptvPdo->prepare($updateSql);
        $updateResult = $updateStmt->execute([
            $tmdbData['id'],
            $tmdbData['poster_path'] ?? null,
            $year,
            $iptvId
        ]);
        
        if (!$updateResult) {
            throw new Exception("Échec de la mise à jour IPTV");
        }
        
        // ÉTAPE 2: Ajouter dans entertainments
        $sql = "INSERT INTO entertainments (
            name, tmdb_id, thumbnail_url, poster_url, description, type, 
            movie_access, language, IMDb_rating, duration, release_date, download_status, 
            status, created_at, updated_at, created_by, updated_by, is_iptv
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?, ?)";
        
        $stmt = $mainPdo->prepare($sql);
        $result = $stmt->execute([
            $title,
            $tmdbData['id'],
            $thumbnailUrl,
            $posterUrl,
            $description,
            $type,
            'free',           // movie_access
            'french',         // language
            $imdbRating,      // IMDb_rating (format x.x)
            $duration,        // duration
            $releaseDate,     // release_date
            1,                // download_status
            1,                // status
            1,                // created_by
            1,                // updated_by
            1                 // is_iptv
        ]);
        
        if (!$result) {
            return false;
        }
        
        $entertainmentId = $mainPdo->lastInsertId();
        
        // ÉTAPE 3: Ajouter les genres si disponibles
        if (!empty($tmdbData['genre_ids']) && is_array($tmdbData['genre_ids'])) {
            $sqlGenre = "INSERT INTO entertainment_gener_mapping (entertainment_id, genre_id, created_at, updated_at) VALUES (?, ?, NOW(), NOW())";
            $stmtGenre = $mainPdo->prepare($sqlGenre);
            
            foreach ($tmdbData['genre_ids'] as $tmdbGenreId) {
                // Vérifier si le genre TMDB existe dans notre mapping
                if (isset($genreMapping[$tmdbGenreId])) {
                    $localGenreId = $genreMapping[$tmdbGenreId];
                    
                    try {
                        $stmtGenre->execute([$entertainmentId, $localGenreId]);
                    } catch (Exception $genreError) {
                        // Logger l'erreur mais continuer avec les autres genres
                        error_log("Erreur lors de l'ajout du genre {$tmdbGenreId} -> {$localGenreId}: " . $genreError->getMessage());
                    }
                }
            }
        }
        
        return $entertainmentId;
    } catch (Exception $e) {
        error_log("Erreur lors de l'ajout: " . $e->getMessage());
        return false;
    }
}

function correctTmdbId($iptvPdo, $iptvId, $newTmdbId, $posterPath = null, $year = null) {
    try {
        $sql = "UPDATE poster_iptv SET tmdb_id = ?";
        $params = [$newTmdbId];
        
        if ($posterPath) {
            $sql .= ", poster_path = ?";
            $params[] = $posterPath;
        }
        
        if ($year) {
            $sql .= ", year = ?";
            $params[] = $year;
        }
        
        $sql .= " WHERE id = ?";
        $params[] = $iptvId;
        
        $stmt = $iptvPdo->prepare($sql);
        return $stmt->execute($params);
    } catch (Exception $e) {
        error_log("Erreur lors de la correction: " . $e->getMessage());
        return false;
    }
}

// Traitement des requêtes AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'search_tmdb') {
        $title = $_POST['title'] ?? '';
        $type = $_POST['type'] ?? '';
        
        if (!$title) {
            echo json_encode(['success' => false, 'message' => 'Titre manquant']);
            exit;
        }
        
        $tmdbResults = searchTmdb($title, $type, $config['tmdb_bearer_token'], $config['tmdb_api_base']);
        
        if (empty($tmdbResults)) {
            echo json_encode(['success' => false, 'message' => 'Aucun résultat TMDB trouvé']);
            exit;
        }
        
        // Enrichir les résultats avec des détails supplémentaires
        foreach ($tmdbResults as &$result) {
            $result['poster_url'] = !empty($result['poster_path']) ? 
                'https://image.tmdb.org/t/p/w500' . $result['poster_path'] : null;
            $result['backdrop_url'] = !empty($result['backdrop_path']) ? 
                'https://image.tmdb.org/t/p/w780' . $result['backdrop_path'] : null;
            $result['media_type'] = ($type === 'tvshow' || $type === 'series') ? 'tv' : 'movie';
        }
        
        echo json_encode(['success' => true, 'results' => array_slice($tmdbResults, 0, 10)]);
        exit;
    }
    
    if ($action === 'add') {
        $iptvId = $_POST['iptv_id'] ?? '';
        $sourceDb = $_POST['source_db'] ?? '';
        $tmdbData = json_decode($_POST['tmdb_data'] ?? '{}', true);
        
        if (!$iptvId || !$sourceDb || empty($tmdbData)) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        $iptvPdo = getConnection($config[$sourceDb]);
        $stmt = $iptvPdo->prepare("SELECT * FROM poster_iptv WHERE id = ?");
        $stmt->execute([$iptvId]);
        $iptvData = $stmt->fetch();
        
        if (!$iptvData) {
            echo json_encode(['success' => false, 'message' => 'Poster IPTV non trouvé']);
            exit;
        }
        
        $mainPdo = getConnection($config['main_db']);
        $entertainmentId = addToMain($mainPdo, $iptvPdo, $tmdbData, $iptvData, $iptvId);
        
        if ($entertainmentId) {
            echo json_encode(['success' => true, 'message' => 'Ajouté avec succès, TMDB ID et année mis à jour', 'entertainment_id' => $entertainmentId]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Erreur lors de l\'ajout']);
        }
        exit;
    }
    
    if ($action === 'correct') {
        $itemId = $_POST['iptv_id'] ?? '';
        $sourceDb = $_POST['source_db'] ?? '';
        $tmdbData = json_decode($_POST['tmdb_data'] ?? '{}', true);
        
        if (!$itemId || !$sourceDb || empty($tmdbData)) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        if ($sourceDb === 'main_db') {
            // Correction dans la base principale
            $mainPdo = getConnection($config['main_db']);
            
            $posterUrl = !empty($tmdbData['poster_path']) ? 
                'https://image.tmdb.org/t/p/w500' . $tmdbData['poster_path'] : null;
            $thumbnailUrl = !empty($tmdbData['backdrop_path']) ? 
                'https://image.tmdb.org/t/p/w780' . $tmdbData['backdrop_path'] : null;
            
            // Rating IMDB (format x.x)
            $imdbRating = null;
            if (!empty($tmdbData['vote_average'])) {
                $imdbRating = number_format((float)$tmdbData['vote_average'], 1);
            }
            
            // Date de sortie
            $releaseDate = null;
            $type = $tmdbData['media_type'] ?? 'movie';
            if ($type === 'movie' && !empty($tmdbData['release_date'])) {
                $releaseDate = $tmdbData['release_date'];
            } elseif ($type === 'tv' && !empty($tmdbData['first_air_date'])) {
                $releaseDate = $tmdbData['first_air_date'];
            }
            
            $sql = "UPDATE entertainments SET 
                    tmdb_id = ?, 
                    poster_url = ?, 
                    thumbnail_url = ?, 
                    description = ?,
                    IMDb_rating = ?,
                    release_date = ?,
                    updated_at = NOW() 
                    WHERE id = ?";
            
            $stmt = $mainPdo->prepare($sql);
            $result = $stmt->execute([
                $tmdbData['id'],
                $posterUrl,
                $thumbnailUrl,
                $tmdbData['overview'] ?? null,
                $imdbRating,
                $releaseDate,
                $itemId
            ]);
            
        } else {
            // Correction dans les bases IPTV
        $iptvPdo = getConnection($config[$sourceDb]);
            
            // Extraire l'année de la date de sortie
            $year = null;
            $type = $tmdbData['media_type'] ?? 'movie';
            if ($type === 'movie' && !empty($tmdbData['release_date'])) {
                $year = date('Y', strtotime($tmdbData['release_date']));
            } elseif ($type === 'tv' && !empty($tmdbData['first_air_date'])) {
                $year = date('Y', strtotime($tmdbData['first_air_date']));
            }
            
            $result = correctTmdbId($iptvPdo, $itemId, $tmdbData['id'], $tmdbData['poster_path'] ?? null, $year);
        }
        
        if ($result) {
            $message = ($sourceDb === 'main_db') ? 
                'TMDB ID et métadonnées mis à jour avec succès' : 
                'TMDB ID, poster et année mis à jour avec succès';
                
            echo json_encode([
                'success' => true, 
                'message' => $message,
                'tmdb_id' => $tmdbData['id'],
                'poster_path' => $tmdbData['poster_path'] ?? null,
                'year' => $year ?? null
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Erreur lors de la correction']);
        }
        exit;
    }
    
    if ($action === 'fix_names') {
        $sourceDb = $_POST['source_db'] ?? '';
        $limit = (int) ($_POST['limit'] ?? 100);
        
        if (!$sourceDb || $sourceDb === 'all' || $sourceDb === 'main_db') {
            echo json_encode(['success' => false, 'message' => 'Cette fonction ne fonctionne que sur les bases IPTV individuelles']);
            exit;
        }
        
        $iptvPdo = getConnection($config[$sourceDb]);
        $results = fixTitlesWithYear($iptvPdo, $sourceDb, $limit);
        
        $message = "Traitement terminé: {$results['processed']} éléments traités, {$results['updated']} mis à jour";
        if (!empty($results['errors'])) {
            $message .= ". Erreurs: " . implode('; ', $results['errors']);
        }
        
        echo json_encode([
            'success' => true,
            'message' => $message,
            'results' => $results
        ]);
        exit;
    }
    
    if ($action === 'edit_iptv_item') {
        $itemId = $_POST['item_id'] ?? '';
        $sourceDb = $_POST['source_db'] ?? '';
        $formData = json_decode($_POST['form_data'] ?? '{}', true);
        
        if (!$itemId || !$sourceDb || empty($formData)) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        if ($sourceDb === 'main_db') {
            echo json_encode(['success' => false, 'message' => 'Cette fonction ne fonctionne que pour les bases IPTV']);
            exit;
        }
        
        try {
            $iptvPdo = getConnection($config[$sourceDb]);
            
            // Construire la requête UPDATE dynamiquement
            $allowedFields = ['title', 'year', 'type', 'tmdb_id', 'poster_path', 'poster_image'];
            $updateFields = [];
            $updateValues = [];
            
            foreach ($allowedFields as $field) {
                if (isset($formData[$field])) {
                    $updateFields[] = "$field = ?";
                    
                    // Traitement spécial pour les champs optionnels
                    $value = $formData[$field];
                    if ($field === 'year' && (empty($value) || $value === '')) {
                        $value = null; // Autoriser année vide
                    } elseif (in_array($field, ['tmdb_id', 'poster_path', 'poster_image']) && empty($value)) {
                        $value = null; // Autoriser ces champs vides
                    }
                    
                    $updateValues[] = $value;
                }
            }
            
            if (empty($updateFields)) {
                echo json_encode(['success' => false, 'message' => 'Aucun champ à mettre à jour']);
                exit;
            }
            
            $updateValues[] = $itemId; // Pour la clause WHERE
            $sql = "UPDATE poster_iptv SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?";
            
            $stmt = $iptvPdo->prepare($sql);
            $result = $stmt->execute($updateValues);
            
            if ($result) {
                // Récupérer l'élément mis à jour
                $stmt = $iptvPdo->prepare("SELECT * FROM poster_iptv WHERE id = ?");
                $stmt->execute([$itemId]);
                $updatedItem = $stmt->fetch();
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Élément mis à jour avec succès',
                    'updated_item' => $updatedItem
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la mise à jour']);
            }
            
        } catch (Exception $e) {
            error_log("Erreur lors de l'édition IPTV: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'get_iptv_item') {
        $itemId = $_POST['item_id'] ?? '';
        $sourceDb = $_POST['source_db'] ?? '';
        
        if (!$itemId || !$sourceDb) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        if ($sourceDb === 'main_db') {
            echo json_encode(['success' => false, 'message' => 'Cette fonction ne fonctionne que pour les bases IPTV']);
            exit;
        }
        
        try {
            $iptvPdo = getConnection($config[$sourceDb]);
            $stmt = $iptvPdo->prepare("SELECT * FROM poster_iptv WHERE id = ?");
            $stmt->execute([$itemId]);
            $item = $stmt->fetch();
            
            if ($item) {
                echo json_encode(['success' => true, 'item' => $item]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Élément non trouvé']);
            }
        } catch (Exception $e) {
            error_log("Erreur lors de la récupération IPTV: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'get_edit_sources') {
        $itemId = $_POST['item_id'] ?? '';
        $sourceDb = $_POST['source_db'] ?? '';
        
        if (!$itemId || !$sourceDb || $sourceDb === 'main_db') {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants ou base non supportée']);
            exit;
        }
        
        try {
            $iptvPdo = getConnection($config[$sourceDb]);
            $stmt = $iptvPdo->prepare("SELECT id, url, quality, language FROM source_iptv WHERE poster_iptv_id = ? ORDER BY id");
            $stmt->execute([$itemId]);
            $sources = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'sources' => $sources]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'add_edit_source') {
        $itemId = $_POST['item_id'] ?? '';
        $sourceDb = $_POST['source_db'] ?? '';
        $sourceData = json_decode($_POST['source_data'] ?? '{}', true);
        
        if (!$itemId || !$sourceDb || $sourceDb === 'main_db' || empty($sourceData)) {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants ou base non supportée']);
            exit;
        }
        
        try {
            $iptvPdo = getConnection($config[$sourceDb]);
            $sql = "INSERT INTO source_iptv (poster_iptv_id, url, quality, language) VALUES (?, ?, ?, ?)";
            $stmt = $iptvPdo->prepare($sql);
            $result = $stmt->execute([
                $itemId,
                $sourceData['url'] ?? '',
                $sourceData['quality'] ?? 'HD',
                $sourceData['language'] ?? 'fr'
            ]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Source ajoutée avec succès']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erreur lors de l\'ajout']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'delete_edit_source') {
        $sourceId = $_POST['source_id'] ?? '';
        $sourceDb = $_POST['source_db'] ?? '';
        
        if (!$sourceId || !$sourceDb || $sourceDb === 'main_db') {
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants ou base non supportée']);
            exit;
        }
        
        try {
            $iptvPdo = getConnection($config[$sourceDb]);
            $stmt = $iptvPdo->prepare("DELETE FROM source_iptv WHERE id = ?");
            $result = $stmt->execute([$sourceId]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Source supprimée avec succès']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la suppression']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'get_movie_source') {
        $movieId = $_POST['movie_id'] ?? '';
        $sourceDb = $_POST['source_db'] ?? '';
        
        // Debug: Log de la requête reçue
        error_log("🎬 get_movie_source appelé - movieId: $movieId, sourceDb: $sourceDb");
        
        if (!$movieId || !$sourceDb) {
            error_log("❌ Paramètres manquants - movieId: '$movieId', sourceDb: '$sourceDb'");
            echo json_encode(['success' => false, 'message' => 'Paramètres manquants']);
            exit;
        }
        
        try {
            error_log("🔍 Tentative de connexion à la base: $sourceDb");
            $iptvPdo = getConnection($config[$sourceDb]);
            error_log("✅ Connexion réussie à $sourceDb");
            
            // Chercher l'URL dans source_iptv
            error_log("🔍 Recherche URL pour poster_iptv_id: $movieId");
            $stmt = $iptvPdo->prepare("SELECT url FROM source_iptv WHERE poster_iptv_id = ?");
            $stmt->execute([$movieId]);
            $source = $stmt->fetch();
            error_log("📊 Résultat requête: " . ($source ? "URL trouvée: " . substr($source['url'], 0, 50) . "..." : "Aucune URL"));
            
            // Debug supplémentaire : vérifier si l'ID existe dans la base
            $stmt = $iptvPdo->prepare("SELECT COUNT(*) as count FROM source_iptv WHERE poster_iptv_id = ?");
            $stmt->execute([$movieId]);
            $count = $stmt->fetch()['count'];
            error_log("🔢 Nombre d'enregistrements trouvés pour poster_iptv_id $movieId: $count");
            
            if ($source && !empty($source['url'])) {
                // Reconstruire l'URL avec le préfixe du config
                $originalUrl = $source['url'];
                
                // Extraire le nom du fichier de l'URL originale
                $pathParts = explode('/', $originalUrl);
                $fileName = end($pathParts); // Dernier élément = nom du fichier
                
                // Construire la nouvelle URL
                $streamUrlBase = $config['iptv_stream_url_base'] ?? 'https://server.flashfilms-box.top/private.php?file=';
                $newUrl = $streamUrlBase . $fileName;
                
                echo json_encode([
                    'success' => true, 
                    'url' => $newUrl,
                    'original_url' => $originalUrl, // Pour debug
                    'filename' => $fileName, // Pour debug
                    'message' => 'Source trouvée et URL reconstruite'
                ]);
            } else {
                echo json_encode([
                    'success' => false, 
                    'message' => 'Aucune source vidéo trouvée pour ce film'
                ]);
            }
        } catch (Exception $e) {
            error_log("❌ Exception dans get_movie_source: " . $e->getMessage());
            error_log("🔍 Stack trace: " . $e->getTraceAsString());
            echo json_encode([
                'success' => false, 
                'message' => 'Erreur lors de la recherche de la source : ' . $e->getMessage()
            ]);
        }
        exit;
    }
}

// Interface principale
$query = $_GET['q'] ?? '';
$specialQuery = $_GET['special'] ?? '';
$selectedDb = $_GET['db'] ?? 'both';
$results = [];
$totalResults = 0;
$searchTitle = '';

if ($query || $specialQuery) {
    try {
        $mainPdo = getConnection($config['main_db']);
        $iptvPdo1 = getConnection($config['iptv_db']);
        $iptvPdo2 = getConnection($config['iptv_db2']);
        
        $allResults = [];
        
        if ($specialQuery) {
            // Requête spéciale
            $specialQueries = [
                'no_tmdb' => 'Sans TMDB ID',
                'invalid_tmdb' => 'TMDB ID invalide',
                'recent' => 'Ajouts récents (7 jours)',
                'no_poster' => 'Sans poster',
                'movies_only' => 'Films seulement',
                'series_only' => 'Séries seulement',
                'title_mismatch' => 'Titres identiques, TMDB différents'
            ];
            
            $searchTitle = $specialQueries[$specialQuery] ?? 'Requête spéciale';
            
            if ($specialQuery === 'title_mismatch') {
                // Traitement spécial pour les correspondances de titres
                $allResults = findTitleMismatches($mainPdo, $iptvPdo1, $iptvPdo2, $selectedDb, $config['search_limit']);
            } else {
                // Requêtes normales
                if ($selectedDb === 'all' || $selectedDb === 'main_db') {
                    $mainResults = getSpecialQuery($mainPdo, $specialQuery, 'main_db', $config['search_limit']);
                    $allResults = array_merge($allResults, $mainResults);
                }
                
                if ($selectedDb === 'all' || $selectedDb === 'both' || $selectedDb === 'iptv_db') {
                    $results1 = getSpecialQuery($iptvPdo1, $specialQuery, 'iptv_db', $config['search_limit']);
                    $allResults = array_merge($allResults, $results1);
                }
                
                if ($selectedDb === 'all' || $selectedDb === 'both' || $selectedDb === 'iptv_db2') {
                    $results2 = getSpecialQuery($iptvPdo2, $specialQuery, 'iptv_db2', $config['search_limit']);
                    $allResults = array_merge($allResults, $results2);
                }
            }
        } else {
            // Recherche classique
            $searchTitle = "\"$query\"";
            
            if ($selectedDb === 'all' || $selectedDb === 'main_db') {
                $mainResults = searchInMainDb($mainPdo, $query, $config['search_limit']);
                $allResults = array_merge($allResults, $mainResults);
            }
            
            if ($selectedDb === 'all' || $selectedDb === 'both' || $selectedDb === 'iptv_db') {
        $results1 = searchInIptvDb($iptvPdo1, $query, 'iptv_db', $config['search_limit']);
                $allResults = array_merge($allResults, $results1);
            }
            
            if ($selectedDb === 'all' || $selectedDb === 'both' || $selectedDb === 'iptv_db2') {
        $results2 = searchInIptvDb($iptvPdo2, $query, 'iptv_db2', $config['search_limit']);
                $allResults = array_merge($allResults, $results2);
            }
        }
        
        foreach ($allResults as $result) {
            if ($specialQuery === 'title_mismatch') {
                // Pour les correspondances de titres, on affiche tous les résultats
                $result['poster_url'] = !empty($result['poster_path']) && strpos($result['poster_path'], 'http') !== 0 ? 
                    $config['tmdb_image_base'] . $result['poster_path'] : 
                    ($result['poster_path'] ?: $result['poster_image']);
                $results[] = $result;
            } else {
                // Nouvelle logique : afficher tous les résultats avec indicateur de présence
                if ($result['source_db'] === 'main_db') {
                    $result['match_status'] = 'main';
                    $result['entertainment'] = null;
                    $result['poster_url'] = $result['poster_path'] ?: $result['poster_image'];
                } else {
                                        // Pour les bases IPTV, toujours vérifier l'existence mais afficher tous les résultats
            $existence = checkExistsInMain($mainPdo, $result['tmdb_id'], $result['title']);
                $result['match_status'] = $existence['status'];
                $result['entertainment'] = $existence['entertainment'];
                $result['title_matches'] = $existence['title_matches'] ?? [];
                $result['poster_url'] = !empty($result['poster_path']) && strpos($result['poster_path'], 'http') !== 0 ? 
                    $config['tmdb_image_base'] . $result['poster_path'] : 
                    ($result['poster_path'] ?: $result['poster_image']);
                }
                $results[] = $result;
            }
        }
        
        $totalResults = count($results);
    } catch (Exception $e) {
        $error = "Erreur de recherche: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recherche VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/phosphor-icons@2.0.0/src/css/icons.css" rel="stylesheet">

    <style>
        .result-card { transition: transform 0.2s; }
        .result-card:hover { transform: translateY(-2px); }
        .poster-img { width: 100px; height: 150px; object-fit: cover; border-radius: 8px; }
        .status-badge { font-size: 0.75rem; }
        .btn-action { min-width: 80px; }
        
        /* Styles pour l'icône Play repositionnée */
        .icon-play {
            position: absolute !important;
            bottom: 15px;
            right: 15px;
            z-index: 10;
        }
        
        /* Zone d'icônes d'actions en bas à droite */
        .action-icons {
            position: absolute !important;
            bottom: 15px;
            right: 50px; /* Décalé vers la gauche du bouton Play */
            z-index: 10;
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        /* Pour les séries (sans bouton Play), centrer les icônes */
        .result-card:has(.icon-seasons) .action-icons {
            right: 15px; /* Même position que le bouton Play */
        }
        
        /* Styles pour les icônes SVG */
        .icon-edit,
        .icon-sources,
        .icon-play,
        .icon-seasons {
            transition: all 0.3s ease;
            opacity: 0.8;
        }
        
        .icon-edit:hover {
            stroke: #e67e22 !important;
            opacity: 1;
            transform: scale(1.1);
        }
        
        .icon-sources:hover {
            stroke: #2980b9 !important;
            opacity: 1;
            transform: scale(1.1);
        }
        
        .icon-play:hover {
            stroke: #229954 !important;
            opacity: 1;
            transform: scale(1.1);
        }
        
        .icon-play:hover polygon {
            fill: #229954 !important;
        }
        
        .icon-seasons:hover {
            stroke: #138d75 !important;
            opacity: 1;
            transform: scale(1.1);
        }
        
        .icon-edit:active,
        .icon-sources:active,
        .icon-play:active,
        .icon-seasons:active {
            transform: scale(0.95);
        }

    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="ph ph-television me-2"></i>
                VOD IPTV
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link active" href="search.php">
                    <i class="ph ph-magnifying-glass me-1"></i>
                    Recherche
                </a>
                <a class="nav-link" href="stats.php">
                    <i class="ph ph-chart-bar me-1"></i>
                    Stats
                </a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="mb-0">
                        <i class="ph ph-magnifying-glass me-2"></i>
                        Recherche de contenu
                    </h1>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="ph ph-house me-1"></i>
                        Accueil
                    </a>
                </div>
                
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="mb-3">
                            <div class="d-flex gap-2 mb-3">
                            <input 
                                type="text" 
                                name="q" 
                                class="form-control" 
                                placeholder="Rechercher un film ou une série..." 
                                value="<?= htmlspecialchars($query) ?>"
                                autofocus
                            >
                                <select name="db" class="form-select" style="max-width: 220px;">
                                    <option value="all" <?= $selectedDb === 'all' ? 'selected' : '' ?>>Toutes les bases</option>
                                    <option value="main_db" <?= $selectedDb === 'main_db' ? 'selected' : '' ?>>Base principale</option>
                                    <option value="both" <?= $selectedDb === 'both' ? 'selected' : '' ?>>Bases IPTV seulement</option>
                                    <option value="iptv_db" <?= $selectedDb === 'iptv_db' ? 'selected' : '' ?>>IPTV DB principale</option>
                                    <option value="iptv_db2" <?= $selectedDb === 'iptv_db2' ? 'selected' : '' ?>>IPTV DB secondaire</option>
                                </select>
                            <button type="submit" class="btn btn-primary">
                                <i class="ph ph-magnifying-glass"></i> Rechercher
                            </button>
                            </div>
                        </form>
                        
                        <div class="border-top pt-3">
                            <h6 class="mb-3">
                                <i class="ph ph-funnel me-2"></i>
                                Requêtes rapides
                            </h6>
                            <div class="row g-2">
                                <div class="col-md-4">
                                    <a href="?special=no_tmdb&db=<?= htmlspecialchars($selectedDb) ?>" 
                                       class="btn btn-outline-warning btn-sm w-100 <?= $specialQuery === 'no_tmdb' ? 'active' : '' ?>">
                                        <i class="ph ph-warning me-1"></i>
                                        Sans TMDB ID
                                    </a>
                        </div>
                                <div class="col-md-4">
                                    <a href="?special=invalid_tmdb&db=<?= htmlspecialchars($selectedDb) ?>" 
                                       class="btn btn-outline-danger btn-sm w-100 <?= $specialQuery === 'invalid_tmdb' ? 'active' : '' ?>">
                                        <i class="ph ph-x-circle me-1"></i>
                                        TMDB invalide
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="?special=recent&db=<?= htmlspecialchars($selectedDb) ?>" 
                                       class="btn btn-outline-info btn-sm w-100 <?= $specialQuery === 'recent' ? 'active' : '' ?>">
                                        <i class="ph ph-clock me-1"></i>
                                        Récents (7j)
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="?special=no_poster&db=<?= htmlspecialchars($selectedDb) ?>" 
                                       class="btn btn-outline-secondary btn-sm w-100 <?= $specialQuery === 'no_poster' ? 'active' : '' ?>">
                                        <i class="ph ph-image me-1"></i>
                                        Sans poster
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="?special=movies_only&db=<?= htmlspecialchars($selectedDb) ?>" 
                                       class="btn btn-outline-primary btn-sm w-100 <?= $specialQuery === 'movies_only' ? 'active' : '' ?>">
                                        <i class="ph ph-film-strip me-1"></i>
                                        Films seulement
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="?special=series_only&db=<?= htmlspecialchars($selectedDb) ?>" 
                                       class="btn btn-outline-success btn-sm w-100 <?= $specialQuery === 'series_only' ? 'active' : '' ?>">
                                        <i class="ph ph-television me-1"></i>
                                        Séries seulement
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="?special=title_mismatch&db=<?= htmlspecialchars($selectedDb) ?>" 
                                       class="btn btn-outline-danger btn-sm w-100 <?= $specialQuery === 'title_mismatch' ? 'active' : '' ?>">
                                        <i class="ph ph-warning-diamond me-1"></i>
                                        Titres identiques, TMDB différents
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        Détecte les doublons potentiels avec même titre mais TMDB ID différents
                                    </small>
                                </div>
                            </div>
                            
                            <!-- Bouton Fix Name -->
                            <div class="border-top pt-3 mt-3">
                                <h6 class="mb-3">
                                    <i class="ph ph-wrench me-2"></i>
                                    Outils de maintenance
                                </h6>
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <button type="button" 
                                                class="btn btn-outline-warning btn-sm w-100" 
                                                onclick="showFixNamesModal()">
                                            <i class="ph ph-text-aa me-1"></i>
                                            Fix Name (Nettoyer les titres)
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            Extrait l'année des titres au format "Titre (2025)"
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($query || $specialQuery): ?>
                                <div class="mt-3 pt-2 border-top">
                                    <a href="search.php" class="btn btn-outline-secondary btn-sm">
                                        <i class="ph ph-x me-1"></i>
                                        Effacer les filtres
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="form-text mt-3">
                            <i class="ph ph-info-circle me-1"></i>
                            Recherche dans les bases de données sélectionnées. 
                            <?php if (in_array($selectedDb, ['both', 'iptv_db', 'iptv_db2'])): ?>
                                Pour les bases IPTV, tous les éléments sont affichés avec indicateur de présence dans le système principal.
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="ph ph-warning me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($query || $specialQuery): ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>
                            <?php if ($specialQuery): ?>
                                <i class="ph ph-funnel me-2"></i>
                                Résultats pour : <?= htmlspecialchars($searchTitle) ?>
                            <?php else: ?>
                                Résultats pour <?= htmlspecialchars($searchTitle) ?>
                            <?php endif; ?>
                            <span class="badge bg-secondary"><?= $totalResults ?></span>
                        </h5>
                        <div class="text-end">
                            <?php if ($selectedDb !== 'all'): ?>
                                <small class="text-muted d-block">
                                    Base : <?php 
                                        switch($selectedDb) {
                                            case 'main_db': echo 'Principale (Entertainments)'; break;
                                            case 'both': echo 'IPTV seulement'; break;
                                            case 'iptv_db': echo 'IPTV principale'; break;
                                            case 'iptv_db2': echo 'IPTV secondaire'; break;
                                            default: echo 'Toutes'; break;
                                        }
                                    ?>
                                </small>
                            <?php endif; ?>
                            <?php if ($totalResults > 0 && in_array($selectedDb, ['both', 'iptv_db', 'iptv_db2'])): ?>
                            <small class="text-muted">
                                    Incluant indicateurs de présence dans le système principal
                            </small>
                        <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if (empty($results)): ?>
                        <div class="alert alert-info">
                            <i class="ph ph-info me-2"></i>
                            Aucun résultat trouvé pour cette recherche.
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($results as $result): ?>
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card result-card h-100">
                                        <div class="card-body position-relative">
                                            <div class="d-flex gap-3">
                                                <div class="flex-shrink-0">
                                                    <?php if (!empty($result['poster_url'])): ?>
                                                        <img 
                                                            src="<?= htmlspecialchars($result['poster_url']) ?>" 
                                                            alt="<?= htmlspecialchars($result['title']) ?>"
                                                            class="poster-img"
                                                            onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDEwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxNTAiIGZpbGw9IiNFOUVDRUYiLz48cGF0aCBkPSJNNDQgNjBINTZWNzJINDRWNjBaIiBmaWxsPSIjOUI5QkE4Ii8+PC9zdmc+'"
                                                        >
                                                    <?php else: ?>
                                                        <div class="poster-img bg-light d-flex align-items-center justify-content-center">
                                                            <i class="ph ph-image text-muted" style="font-size: 2rem;"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="card-title mb-2">
                                                        <?= htmlspecialchars($result['title']) ?>
                                                    </h6>
                                                    <div class="mb-2">
                                                        <span class="badge bg-primary status-badge">
                                                            <?= ucfirst($result['type']) ?>
                                                        </span>
                                                        <?php if ($result['source_db'] === 'main_db'): ?>
                                                            <span class="badge bg-primary status-badge">Base principale</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary status-badge"><?= htmlspecialchars($result['source_db']) ?></span>
                                                            
                                                            <?php if ($result['match_status'] === 'tmdb'): ?>
                                                                <span class="badge bg-success status-badge">✓ Correspondance TMDB</span>
                                                            <?php elseif ($result['match_status'] === 'title'): ?>
                                                                <span class="badge bg-warning text-dark status-badge">⚠️ Correspondance par titre</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-info status-badge">✨ Nouveau contenu</span>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                    <?php if (!empty($result['year'])): ?>
                                                        <small class="text-muted d-block">
                                                            <i class="ph ph-calendar me-1"></i>
                                                            <?= htmlspecialchars($result['year']) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                    <?php if (!empty($result['tmdb_id'])): ?>
                                                        <small class="text-muted d-block">
                                                            <i class="ph ph-database me-1"></i>
                                                            TMDB: <?= htmlspecialchars($result['tmdb_id']) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($result['match_status'] === 'title' && !empty($result['title_matches'])): ?>
                                                        <div class="mt-2">
                                                            <small class="text-warning fw-bold">
                                                                <i class="ph ph-magnifying-glass me-1"></i>
                                                                <?= count($result['title_matches']) ?> correspondance(s) par titre trouvée(s) :
                                                            </small>
                                                            <?php foreach ($result['title_matches'] as $match): ?>
                                                                <div class="mt-1">
                                                                    <div class="d-flex justify-content-between align-items-center bg-light p-2 rounded">
                                                                        <div>
                                                                            <span class="badge bg-warning text-dark me-1">DB Principale</span>
                                                                            <small class="text-dark">
                                                                                <strong><?= htmlspecialchars($match['name']) ?></strong><br>
                                                                                <?php if (!empty($match['tmdb_id'])): ?>
                                                                                    TMDB: <?= htmlspecialchars($match['tmdb_id']) ?>
                                                                                <?php else: ?>
                                                                                    TMDB: Non défini
                                                                                <?php endif; ?>
                                                                                <?php if (!empty($match['year'])): ?>
                                                                                    | Année: <?= htmlspecialchars($match['year']) ?>
                                                                                <?php endif; ?>
                                                                            </small>
                                                                        </div>
                                                                        <button 
                                                                            class="btn btn-outline-warning btn-sm btn-correct"
                                                                            style="font-size: 0.7rem; padding: 2px 6px;"
                                                                            data-id="<?= $match['id'] ?>"
                                                                            data-source-db="main_db"
                                                                            data-title="<?= htmlspecialchars($match['name']) ?>"
                                                                            data-type="<?= htmlspecialchars($match['type']) ?>"
                                                                            data-correct="true"
                                                                            title="Corriger le TMDB ID de cet élément dans la DB principale">
                                                                            <i class="ph ph-wrench me-1"></i>
                                                                            Corriger
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($specialQuery === 'title_mismatch' && !empty($result['title_matches'])): ?>
                                                        <div class="mt-2">
                                                            <small class="text-danger fw-bold">
                                                                <i class="ph ph-warning-diamond me-1"></i>
                                                                <?= $result['match_count'] ?> correspondance(s) de conflit détectée(s) :
                                                            </small>
                                                            <?php foreach ($result['title_matches'] as $match): ?>
                                                                <div class="mt-1">
                                                                    <div class="d-flex justify-content-between align-items-center bg-light p-2 rounded">
                                                                        <div>
                                                                            <span class="badge bg-danger text-white me-1">
                                                                                <?= htmlspecialchars($match['db_source']) ?>
                                                                            </span>
                                                                            <small class="text-dark">
                                                                                <?php if (!empty($match['tmdb_id'])): ?>
                                                                                    TMDB: <?= htmlspecialchars($match['tmdb_id']) ?>
                                                                                <?php else: ?>
                                                                                    TMDB: Non défini
                                                                                <?php endif; ?>
                                                                                <?php if (!empty($match['year'])): ?>
                                                                                    | Année: <?= htmlspecialchars($match['year']) ?>
                                                                                <?php endif; ?>
                                                                            </small>
                                                                        </div>
                                                                        <button 
                                                                            class="btn btn-outline-warning btn-xs"
                                                                            style="font-size: 0.7rem; padding: 2px 6px;"
                                                                            onclick="searchTmdbForCorrect(<?= $match['id'] ?>, '<?= $match['db_source'] ?>', '<?= htmlspecialchars($result['title'], ENT_QUOTES) ?>', '<?= $match['type'] ?>')"
                                                                            title="Corriger le TMDB ID de cet élément en conflit"
                                                                        >
                                                                            <i class="ph ph-wrench me-1"></i>
                                                                            Corriger
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            
                                            <div class="mt-3 d-flex gap-2 flex-wrap">
                                                <?php if ($specialQuery === 'title_mismatch'): ?>
                                                    <!-- Pour les correspondances : bouton principal "Corriger" -->
                                                    <button 
                                                        class="btn btn-warning btn-sm btn-action btn-correct"
                                                        data-id="<?= $result['id'] ?>"
                                                        data-source-db="<?= htmlspecialchars($result['source_db']) ?>"
                                                        data-title="<?= htmlspecialchars($result['title']) ?>"
                                                        data-type="<?= htmlspecialchars($result['type']) ?>"
                                                        data-correct="true"
                                                    >
                                                        <i class="ph ph-wrench me-1"></i>
                                                        Corriger TMDB
                                                    </button>
                                                    <button 
                                                        class="btn btn-info btn-sm btn-action"
                                                        onclick="viewDetails(<?= $result['id'] ?>, '<?= $result['source_db'] ?>')"
                                                    >
                                                        <i class="ph ph-eye me-1"></i>
                                                        Voir détails
                                                    </button>
                                                <?php elseif ($result['source_db'] === 'main_db'): ?>
                                                    <!-- Actions pour la base principale -->
                                                    <button 
                                                        class="btn btn-warning btn-sm btn-action btn-correct"
                                                        data-id="<?= $result['id'] ?>"
                                                        data-source-db="<?= htmlspecialchars($result['source_db']) ?>"
                                                        data-title="<?= htmlspecialchars($result['title']) ?>"
                                                        data-type="<?= htmlspecialchars($result['type']) ?>"
                                                        data-correct="true"
                                                    >
                                                        <i class="ph ph-wrench me-1"></i>
                                                        Corriger TMDB
                                                    </button>
                                                    <button 
                                                        class="btn btn-info btn-sm btn-action"
                                                        onclick="viewDetails(<?= $result['id'] ?>, '<?= $result['source_db'] ?>')"
                                                    >
                                                        <i class="ph ph-eye me-1"></i>
                                                        Voir détails
                                                    </button>
                                                                                        <?php
                                        $editUrl = ($result['type'] === 'tvshow') ? 
                                            "/app/tvshows/{$result['id']}/edit" : 
                                            "/app/entertainments/{$result['id']}/edit";
                                    ?>
                                    <a href="<?= $editUrl ?>" 
                                       class="btn btn-outline-primary btn-sm btn-action" 
                                       target="_blank">
                                        <i class="ph ph-arrow-square-out me-1"></i>
                                        Éditer
                                    </a>
                                                <?php else: ?>
                                                    <!-- Actions pour les bases IPTV selon le statut -->
                                                                                                        <?php if ($result['match_status'] === 'tmdb' && !empty($result['entertainment'])): ?>
                                                        <!-- Déjà présent dans la base principale -->
                                                        <?php
                                                            $editUrl = ($result['entertainment']['type'] === 'tvshow') ? 
                                                                "/app/tvshows/{$result['entertainment']['id']}/edit" : 
                                                                "/app/entertainments/{$result['entertainment']['id']}/edit";
                                                        ?>
                                                        <a href="<?= $editUrl ?>" 
                                                           class="btn btn-primary btn-sm btn-action" 
                                                           target="_blank">
                                                            <i class="ph ph-pencil me-1"></i>
                                                            Éditer dans DB
                                                        </a>
                                                        <button 
                                                            class="btn btn-outline-warning btn-sm btn-action btn-correct"
                                                            data-id="<?= $result['id'] ?>"
                                                            data-source-db="<?= htmlspecialchars($result['source_db']) ?>"
                                                            data-title="<?= htmlspecialchars($result['title']) ?>"
                                                            data-type="<?= htmlspecialchars($result['type']) ?>"
                                                            data-correct="true"
                                                        >
                                                            <i class="ph ph-wrench me-1"></i>
                                                            Corriger IPTV
                                                        </button>
                                                    <?php else: ?>
                                                        <!-- Nouveau contenu -->
                                                <button 
                                                    class="btn btn-success btn-sm btn-action btn-add"
                                                    data-id="<?= $result['id'] ?>"
                                                    data-source-db="<?= htmlspecialchars($result['source_db']) ?>"
                                                    data-title="<?= htmlspecialchars($result['title']) ?>"
                                                    data-type="<?= htmlspecialchars($result['type']) ?>"
                                                >
                                                    <i class="ph ph-plus me-1"></i>
                                                    Ajouter
                                                </button>
                                                <button 
                                                    class="btn btn-warning btn-sm btn-action btn-correct"
                                                    data-id="<?= $result['id'] ?>"
                                                    data-source-db="<?= htmlspecialchars($result['source_db']) ?>"
                                                    data-title="<?= htmlspecialchars($result['title']) ?>"
                                                    data-type="<?= htmlspecialchars($result['type']) ?>"
                                                    data-correct="true"
                                                >
                                                    <i class="ph ph-wrench me-1"></i>
                                                    Corriger
                                                </button>
                                                    <?php endif; ?>
                                                    
                                                    <!-- Icône Play pour les films IPTV seulement -->
                                                    <?php if ($result['type'] === 'movie'): ?>
                                                        <svg class="icon-play btn-play" 
                                                             data-id="<?= $result['id'] ?>"
                                                             data-source-db="<?= htmlspecialchars($result['source_db']) ?>"
                                                             data-title="<?= htmlspecialchars($result['title']) ?>"
                                                             title="Lire le film"
                                                             width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#27ae60" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                                                             style="cursor: pointer;">
                                                            <circle cx="12" cy="12" r="10"></circle>
                                                            <polygon points="10,8 16,12 10,16" fill="#27ae60"></polygon>
                                                        </svg>
                                                    <?php endif; ?>
                                                    
                                                <!-- Zone d'icônes d'actions positionnée en bas à droite -->
                                                <div class="action-icons">
                                                    <!-- Icône Éditer pour les bases IPTV -->
                                                    <svg class="icon-edit btn-edit-iptv" 
                                                         data-id="<?= $result['id'] ?>"
                                                         data-source-db="<?= htmlspecialchars($result['source_db']) ?>"
                                                         title="Éditer les informations de cet élément"
                                                         width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#f39c12" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                                                         style="cursor: pointer;">
                                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                                        <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                                    </svg>
                                                    
                                                    <?php if ($result['type'] === 'movie'): ?>
                                                        <!-- Icône Sources pour les films IPTV -->
                                                        <svg class="icon-sources btn-sources-iptv" 
                                                             data-id="<?= $result['id'] ?>"
                                                             data-source-db="<?= htmlspecialchars($result['source_db']) ?>"
                                                             data-title="<?= htmlspecialchars($result['title'] ?? '') ?>"
                                                             title="Gérer les sources vidéo de ce film"
                                                             width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#3498db" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                                                             style="cursor: pointer;">
                                                            <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
                                                            <path d="m21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
                                                            <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
                                                        </svg>
                                                    <?php else: ?>
                                                        <!-- Icône Saisons pour les séries -->
                                                        <a href="seasons.php?entertainment_id=<?= $result['id'] ?>&source_db=<?= $result['source_db'] ?>&title=<?= urlencode($result['title']) ?>" 
                                                           title="Gérer les saisons et épisodes">
                                                            <svg class="icon-seasons" 
                                                                 width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#16a085" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
                                                                 style="cursor: pointer;">
                                                                <line x1="8" y1="6" x2="21" y2="6"></line>
                                                                <line x1="8" y1="12" x2="21" y2="12"></line>
                                                                <line x1="8" y1="18" x2="21" y2="18"></line>
                                                                <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                                                <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                                                <line x1="3" y1="18" x2="3.01" y2="18"></line>
                                                            </svg>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body" id="successMessage"></div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
        <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body" id="errorMessage"></div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    </div>

    <!-- Modal pour sélection TMDB -->
    <div class="modal fade" id="tmdbModal" tabindex="-1" aria-labelledby="tmdbModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tmdbModalLabel">
                        <i class="ph ph-magnifying-glass me-2"></i>
                        Choisir le contenu TMDB à ajouter
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="tmdbResults"></div>
                    <div id="tmdbLoading" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Recherche en cours...</span>
                        </div>
                        <p class="mt-2">Recherche sur TMDB...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Modal pour Fix Name -->
    <div class="modal fade" id="fixNamesModal" tabindex="-1" aria-labelledby="fixNamesModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="fixNamesModalLabel">
                        <i class="ph ph-text-aa me-2"></i>
                        Fix Name - Nettoyer les titres
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="ph ph-info me-2"></i>
                        Cette fonction va extraire l'année des titres au format "Titre (2025)" et la placer dans le champ année séparé.
                    </div>
                    
                    <form id="fixNamesForm">
                        <div class="mb-3">
                            <label for="fixNamesDb" class="form-label">Base de données</label>
                            <select id="fixNamesDb" name="source_db" class="form-select" required>
                                <option value="">Sélectionnez une base IPTV</option>
                                <option value="iptv_db">IPTV DB principale</option>
                                <option value="iptv_db2">IPTV DB secondaire</option>
                            </select>
                            <div class="form-text">Cette fonction ne fonctionne que sur les bases IPTV individuelles</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="fixNamesLimit" class="form-label">Nombre maximum d'éléments à traiter</label>
                            <input type="number" id="fixNamesLimit" name="limit" class="form-control" value="100" min="1" max="1000">
                            <div class="form-text">Commencez par un petit nombre pour tester</div>
                        </div>
                    </form>
                    
                    <div id="fixNamesResults" style="display: none;">
                        <div class="border-top pt-3">
                            <h6>Résultats du traitement :</h6>
                            <div id="fixNamesResultsContent"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-warning" onclick="executeFixNames()" id="fixNamesBtn">
                        <i class="ph ph-wrench me-1"></i>
                        Lancer le nettoyage
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour édition IPTV -->
    <div class="modal fade" id="editIptvModal" tabindex="-1" aria-labelledby="editIptvModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editIptvModalLabel">
                        <i class="ph ph-pencil me-2"></i>
                        Éditer l'élément IPTV
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="editIptvLoading" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2">Chargement des informations...</p>
                    </div>
                    
                    <form id="editIptvForm" style="display: none;">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="editTitle" class="form-label">Titre</label>
                                    <input type="text" class="form-control" id="editTitle" name="title" required>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                                                                 <div class="mb-3">
                                             <label for="editYear" class="form-label">Année <small class="text-muted">(optionnel)</small></label>
                                             <input type="number" class="form-control" id="editYear" name="year" min="1900" max="2030" placeholder="Ex: 2024">
                                             <div class="form-text">Laissez vide si l'année est inconnue</div>
                                         </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editType" class="form-label">Type</label>
                                            <select class="form-select" id="editType" name="type">
                                                <option value="movie">Film</option>
                                                <option value="tvshow">Série TV</option>
                                                <option value="series">Série</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="editTmdbId" class="form-label">TMDB ID</label>
                                    <input type="text" class="form-control" id="editTmdbId" name="tmdb_id" placeholder="Ex: 299534">
                                    <div class="form-text">Identifiant unique TMDB (optionnel)</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="editPosterPath" class="form-label">Poster Path (TMDB)</label>
                                    <input type="text" class="form-control" id="editPosterPath" name="poster_path" placeholder="Ex: /or06FN3Dka5tukK1e9sl16pB3iy.jpg">
                                    <div class="form-text">Chemin vers l'image poster TMDB</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="editPosterImage" class="form-label">URL Image Poster</label>
                                    <input type="url" class="form-control" id="editPosterImage" name="poster_image" placeholder="https://example.com/poster.jpg">
                                    <div class="form-text">URL complète vers l'image poster</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <label class="form-label">Aperçu du poster</label>
                                    <div id="posterPreview" class="border rounded p-3 bg-light">
                                        <div id="posterPreviewImage" style="min-height: 200px; display: flex; align-items: center; justify-content: center;">
                                            <i class="ph ph-image text-muted" style="font-size: 3rem;"></i>
                                        </div>
                                    </div>
                                    <small class="text-muted d-block mt-2">Le poster s'affichera automatiquement</small>
                                </div>
                            </div>
                        </div>
                        
                        <input type="hidden" id="editItemId" name="item_id">
                        <input type="hidden" id="editSourceDb" name="source_db">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveIptvEdit()" id="saveIptvBtn" style="display: none;">
                        <i class="ph ph-floppy-disk me-1"></i>
                        Sauvegarder
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour gestion des sources -->
    <div class="modal fade" id="sourcesModal" tabindex="-1" aria-labelledby="sourcesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-gradient bg-dark text-white">
                    <h5 class="modal-title" id="sourcesModalLabel">
                        <i class="ph ph-database me-2"></i>
                        Gestion des sources vidéo
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="sourcesLoading" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2">Chargement des sources...</p>
                    </div>
                    
                    <div id="sourcesContent" style="display: none;">
                        <!-- Formulaire d'ajout de source -->
                        <div class="card mb-4">
                            <div class="card-header bg-gradient bg-success text-white">
                                <i class="ph ph-plus me-2"></i>
                                Ajouter une nouvelle source
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="sourceUrl" class="form-label">URL de la source</label>
                                        <input type="url" class="form-control" id="sourceUrl" placeholder="https://example.com/movie.mp4" required>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="sourceQuality" class="form-label">Qualité</label>
                                        <select class="form-select" id="sourceQuality">
                                            <option value="HD">HD</option>
                                            <option value="720p">720p</option>
                                            <option value="1080p">1080p</option>
                                            <option value="4K">4K</option>
                                            <option value="SD">SD</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label for="sourceLanguage" class="form-label">Langue</label>
                                        <select class="form-select" id="sourceLanguage">
                                            <option value="fr">Français</option>
                                            <option value="en">Anglais</option>
                                            <option value="es">Espagnol</option>
                                            <option value="de">Allemand</option>
                                            <option value="it">Italien</option>
                                            <option value="vo">VO</option>
                                        </select>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-success" onclick="addSource()">
                                    <i class="ph ph-plus me-1"></i>
                                    Ajouter la source
                                </button>
                            </div>
                        </div>
                        
                        <!-- Liste des sources -->
                        <div class="card">
                            <div class="card-header bg-gradient bg-info text-white">
                                <i class="ph ph-list me-2"></i>
                                Sources existantes
                            </div>
                            <div class="card-body">
                                <div id="sourcesList">
                                    <div class="text-center py-3 text-muted">
                                        <i class="ph ph-film-strip" style="font-size: 2rem; opacity: 0.5;"></i>
                                        <p class="mb-0 mt-2">Aucune source vidéo</p>
                                        <small>Ajoutez des sources avec le formulaire ci-dessus</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Variables cachées -->
                    <input type="hidden" id="currentMovieId">
                    <input type="hidden" id="currentMovieSourceDb">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let currentIptvId, currentSourceDb, isCorrectMode = false;
        
        // Cache pour les résultats TMDB
        let tmdbCache = {
            title: null,
            results: [],
            timestamp: null,
            // Cache valide pendant 10 minutes
            isValid: function(searchTitle) {
                if (!this.title || !this.timestamp) return false;
                
                // Vérifier si le titre correspond (insensible à la casse et espaces)
                const cleanCacheTitle = this.title.toLowerCase().trim();
                const cleanSearchTitle = searchTitle.toLowerCase().trim();
                
                // Vérifier si le cache n'est pas trop ancien (10 minutes)
                const now = new Date().getTime();
                const cacheAge = now - this.timestamp;
                const maxAge = 10 * 60 * 1000; // 10 minutes en millisecondes
                
                return cleanCacheTitle === cleanSearchTitle && cacheAge < maxAge;
            },
            store: function(title, results) {
                this.title = title;
                this.results = results;
                this.timestamp = new Date().getTime();
                console.log('💾 Cache TMDB mis à jour:', { title, resultsCount: results.length });
            },
            clear: function() {
                this.title = null;
                this.results = [];
                this.timestamp = null;
                console.log('🗑️ Cache TMDB vidé');
            }
        };

        function showToast(type, message) {
            console.log(`📢 showToast appelé: ${type} - ${message}`);
            const toast = document.getElementById(type + 'Toast');
            const messageEl = document.getElementById(type + 'Message');
            
            if (!toast || !messageEl) {
                console.error('❌ Éléments toast non trouvés:', { toast, messageEl });
                alert(`${type.toUpperCase()}: ${message}`); // Fallback
                return;
            }
            
            messageEl.textContent = message;
            new bootstrap.Toast(toast).show();
        }

        // Gestionnaires d'événements pour les boutons
        document.addEventListener('DOMContentLoaded', function() {
            // Boutons Corriger
            document.querySelectorAll('.btn-correct').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.dataset.id;
                    const sourceDb = this.dataset.sourceDb;
                    const title = this.dataset.title;
                    const type = this.dataset.type;
                    
                    console.log('🔧 Bouton Corriger cliqué:', { id, sourceDb, title, type });
                    
                    if (!id || !sourceDb || !title || !type) {
                        console.error('❌ Données manquantes pour le bouton Corriger:', { id, sourceDb, title, type });
                        showToast('error', 'Erreur: Données manquantes pour ce bouton');
                        return;
                    }
                    
                    searchTmdbForCorrect(id, sourceDb, title, type);
                });
            });

            // Boutons Ajouter
            document.querySelectorAll('.btn-add').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.dataset.id;
                    const sourceDb = this.dataset.sourceDb;
                    const title = this.dataset.title;
                    const type = this.dataset.type;
                    
                    console.log('➕ Bouton Ajouter cliqué:', { id, sourceDb, title, type });
                    
                    if (!id || !sourceDb || !title || !type) {
                        console.error('❌ Données manquantes pour le bouton Ajouter:', { id, sourceDb, title, type });
                        showToast('error', 'Erreur: Données manquantes pour ce bouton');
                        return;
                    }
                    
                    searchTmdbForAdd(id, sourceDb, title, type);
                });
            });

            // Boutons Play
            document.querySelectorAll('.btn-play').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.dataset.id;
                    const sourceDb = this.dataset.sourceDb;
                    const title = this.dataset.title;
                    
                    console.log('▶️ Bouton Play cliqué:', { id, sourceDb, title });
                    
                    if (!id || !sourceDb || !title) {
                        console.error('❌ Données manquantes pour le bouton Play:', { id, sourceDb, title });
                        showToast('error', 'Erreur: Données manquantes pour ce bouton');
                        return;
                    }
                    
                    playMovie(id, sourceDb, title);
                });
            });

            // Boutons Éditer IPTV
            document.querySelectorAll('.btn-edit-iptv').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.dataset.id;
                    const sourceDb = this.dataset.sourceDb;
                    
                    console.log('✏️ Bouton Éditer IPTV cliqué:', { id, sourceDb });
                    
                    if (!id || !sourceDb) {
                        console.error('❌ Données manquantes pour le bouton Éditer:', { id, sourceDb });
                        showToast('error', 'Erreur: Données manquantes pour ce bouton');
                        return;
                    }
                    
                    openEditIptvModal(id, sourceDb);
                });
            });

            // Boutons Sources IPTV
            document.querySelectorAll('.btn-sources-iptv').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.dataset.id;
                    const sourceDb = this.dataset.sourceDb;
                    const title = this.dataset.title;
                    
                    console.log('🎬 Bouton Sources IPTV cliqué:', { id, sourceDb, title });
                    
                    if (!id || !sourceDb) {
                        console.error('❌ Données manquantes pour le bouton Sources:', { id, sourceDb });
                        showToast('error', 'Erreur: Données manquantes pour ce bouton');
                        return;
                    }
                    
                    openSourcesModal(id, sourceDb, title);
                });
            });
        });

        function searchTmdbForAdd(iptvId, sourceDb, title, type) {
            currentIptvId = iptvId;
            currentSourceDb = sourceDb;
            isCorrectMode = false;
            
            // Stocker le type et titre pour les recherches personnalisées et le cache
            window.currentSearchType = type;
            window.lastSearchTitle = title;
            
            // Changer le titre de la modal
            document.getElementById('tmdbModalLabel').innerHTML = `
                <i class="ph ph-magnifying-glass me-2"></i>
                Choisir le contenu TMDB à ajouter
            `;
            
            searchTmdbCommon(title, type);
        }

        function searchTmdbForCorrect(iptvId, sourceDb, title, type) {
            currentIptvId = iptvId;
            currentSourceDb = sourceDb;
            isCorrectMode = true;
            
            // Stocker le type et titre pour les recherches personnalisées et le cache
            window.currentSearchType = type;
            window.lastSearchTitle = title;
            
            // Changer le titre de la modal
            document.getElementById('tmdbModalLabel').innerHTML = `
                <i class="ph ph-wrench me-2"></i>
                Choisir le bon contenu TMDB pour corriger
            `;
            
            searchTmdbCommon(title, type);
        }
        
        function searchTmdbCommon(title, type) {
            console.log('🔍 searchTmdbCommon appelé avec:', { title, type, currentIptvId, currentSourceDb, isCorrectMode });
            
            // Vérifications supplémentaires
            if (!title || !type) {
                console.error('❌ Paramètres manquants dans searchTmdbCommon:', { title, type });
                showToast('error', 'Erreur: Titre ou type manquant');
                return;
            }
            
            // Afficher la modal
            const modal = new bootstrap.Modal(document.getElementById('tmdbModal'));
            modal.show();
            
            // Vérifier le cache avant de faire l'appel API
            if (tmdbCache.isValid(title)) {
                console.log('🎯 Cache TMDB trouvé pour:', title);
                showToast('success', '⚡ Résultats TMDB chargés depuis le cache');
                
                document.getElementById('tmdbLoading').style.display = 'none';
                document.getElementById('tmdbResults').innerHTML = '';
                
                // Afficher les résultats du cache avec un indicateur
                displayTmdbResults(tmdbCache.results, true);
                return;
            }
            
            // Pas de cache valide, faire l'appel API
            console.log('🌐 Aucun cache valide, envoi de la requête TMDB...');
            document.getElementById('tmdbLoading').style.display = 'block';
            document.getElementById('tmdbResults').innerHTML = '';
            
            // Rechercher sur TMDB
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=search_tmdb&title=${encodeURIComponent(title)}&type=${type}`
            })
            .then(response => {
                console.log('📡 Réponse HTTP:', response.status, response.statusText);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text(); // D'abord en text pour déboguer
            })
            .then(responseText => {
                console.log('📦 Réponse brute TMDB:', responseText.substring(0, 200) + '...');
                
                let data;
                try {
                    data = JSON.parse(responseText);
                    console.log('✅ JSON parsé avec succès:', data);
                } catch (e) {
                    console.error('❌ Erreur parsing JSON:', e);
                    throw new Error('Réponse serveur invalide: ' + e.message);
                }
                
                document.getElementById('tmdbLoading').style.display = 'none';
                
                if (data.success) {
                    console.log('🎬 Résultats TMDB trouvés:', data.results.length);
                    
                    // Stocker les résultats dans le cache
                    tmdbCache.store(title, data.results);
                    
                    displayTmdbResults(data.results);
                } else {
                    console.log('⚠️ Aucun résultat TMDB:', data.message);
                    document.getElementById('tmdbResults').innerHTML = `
                        <div class="alert alert-warning">
                            <i class="ph ph-warning me-2"></i>
                            ${data.message}
                        </div>
                        <div class="card mt-3">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ph ph-pencil me-2"></i>
                                    Essayer avec un autre nom
                                </h6>
                                <p class="card-text text-muted">
                                    Saisissez un nom personnalisé pour relancer la recherche TMDB
                                </p>
                                <div class="input-group">
                                    <input type="text" 
                                           id="customSearchInput" 
                                           class="form-control" 
                                           placeholder="Tapez un nouveau nom..."
                                           onkeypress="if(event.key==='Enter') searchWithCustomName()">
                                    <button class="btn btn-primary" onclick="searchWithCustomName()">
                                        <i class="ph ph-magnifying-glass me-1"></i>
                                        Rechercher
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Focus sur le champ de saisie
                    setTimeout(() => {
                        const input = document.getElementById('customSearchInput');
                        if (input) input.focus();
                    }, 100);
                }
            })
            .catch(error => {
                console.error('❌ Erreur dans searchTmdbCommon:', error);
                document.getElementById('tmdbLoading').style.display = 'none';
                document.getElementById('tmdbResults').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="ph ph-x-circle me-2"></i>
                        Erreur lors de la recherche TMDB: ${error.message}
                    </div>
                `;
                showToast('error', `Erreur TMDB: ${error.message}`);
            });
        }

        function displayTmdbResults(results, fromCache = false) {
            let html = '';
            
            // Indicateur si les résultats viennent du cache
            if (fromCache) {
                html += `
                    <div class="alert alert-info mb-3">
                        <i class="ph ph-lightning me-2"></i>
                        <strong>Résultats chargés depuis le cache</strong> - Pas d'appel API effectué !
                        <button type="button" class="btn btn-outline-info btn-sm ms-2" onclick="clearTmdbCache()">
                            <i class="ph ph-arrow-clockwise me-1"></i>
                            Forcer une nouvelle recherche
                        </button>
                    </div>
                `;
            }
            
            html += '<div class="row">';
            
            results.forEach((result, index) => {
                const title = result.title || result.name || 'Titre non disponible';
                const releaseDate = result.release_date || result.first_air_date || '';
                const year = releaseDate ? new Date(releaseDate).getFullYear() : '';
                const overview = result.overview || 'Aucune description disponible';
                const rating = result.vote_average ? result.vote_average.toFixed(1) : 'N/A';
                
                html += `
                    <div class="col-12 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex gap-3">
                                    <div class="flex-shrink-0">
                                        ${result.poster_url ? 
                                            `<img src="${result.poster_url}" alt="${title}" class="poster-img">` :
                                            `<div class="poster-img bg-light d-flex align-items-center justify-content-center">
                                                <i class="ph ph-image text-muted" style="font-size: 2rem;"></i>
                                            </div>`
                                        }
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="card-title mb-2">
                                            ${title} ${year ? `(${year})` : ''}
                                        </h6>
                                        <div class="mb-2">
                                            <span class="badge bg-primary me-2">
                                                ${result.media_type === 'tv' ? 'Série TV' : 'Film'}
                                            </span>
                                            <span class="badge bg-warning text-dark">
                                                <i class="ph ph-star-fill me-1"></i>
                                                ${rating}
                                            </span>
                                            <span class="badge bg-secondary me-2">
                                                <i class="ph ph-database me-1"></i>
                                                TMDB: ${result.id}
                                            </span>
                                        </div>
                                        <p class="card-text small text-muted mb-3">
                                            ${overview.length > 200 ? overview.substring(0, 200) + '...' : overview}
                                        </p>
                                        <button 
                                            class="btn btn-success btn-sm"
                                            onclick="confirmSelection(${JSON.stringify(result).replace(/"/g, '&quot;')})"
                                        >
                                            <i class="ph ph-check me-1"></i>
                                            ${isCorrectMode ? 'Corriger avec ce résultat' : 'Choisir ce résultat'}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            document.getElementById('tmdbResults').innerHTML = html;
        }

        function confirmSelection(tmdbData) {
            const button = event.target;
            const originalText = button.innerHTML;
            
            if (isCorrectMode) {
                button.innerHTML = '<i class="ph ph-circle-notch ph-spin me-1"></i>Correction...';
            button.disabled = true;

            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=correct&iptv_id=${currentIptvId}&source_db=${currentSourceDb}&tmdb_data=${encodeURIComponent(JSON.stringify(tmdbData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                        
                        // Fermer la modal
                        bootstrap.Modal.getInstance(document.getElementById('tmdbModal')).hide();
                        
                        // Mettre à jour le bouton original
                        const originalButton = document.querySelector(`button[data-id="${currentIptvId}"][data-correct="true"]`);
                        if (originalButton) {
                            originalButton.innerHTML = '<i class="ph ph-check me-1"></i>Corrigé';
                            originalButton.classList.remove('btn-warning');
                            originalButton.classList.add('btn-outline-warning');
                            originalButton.disabled = true;
                        }
                } else {
                    showToast('error', data.message);
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            })
            .catch(error => {
                    showToast('error', 'Erreur lors de la correction');
                button.innerHTML = originalText;
                button.disabled = false;
            });
            } else {
                button.innerHTML = '<i class="ph ph-circle-notch ph-spin me-1"></i>Ajout...';
            button.disabled = true;

            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=add&iptv_id=${currentIptvId}&source_db=${currentSourceDb}&tmdb_data=${encodeURIComponent(JSON.stringify(tmdbData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                        
                        // Fermer la modal
                        bootstrap.Modal.getInstance(document.getElementById('tmdbModal')).hide();
                        
                        // Mettre à jour le bouton original
                        const originalButton = document.querySelector(`button[data-id="${currentIptvId}"]:not([data-correct])`);
                        if (originalButton) {
                            originalButton.innerHTML = '<i class="ph ph-check me-1"></i>Ajouté';
                            originalButton.classList.remove('btn-success');
                            originalButton.classList.add('btn-outline-success');
                            originalButton.disabled = true;
                        }
                } else {
                    showToast('error', data.message);
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            })
            .catch(error => {
                    showToast('error', 'Erreur lors de l\'ajout');
                button.innerHTML = originalText;
                button.disabled = false;
            });
            }
        }



        function viewDetails(iptvId, sourceDb) {
            showToast('success', `Détails pour ID ${iptvId} de ${sourceDb} (à implémenter)`);
        }

        function playMovie(movieId, sourceDb, title) {
            // Debug initial
            console.log('🎬 playMovie appelé:', { movieId, sourceDb, title });
            
            // Afficher un toast de chargement
            showToast('info', `🔍 Recherche de la source pour "${title}"...`);
            
            // Rechercher la source vidéo directement
            fetch('ajax.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=get_movie_source&movie_id=${encodeURIComponent(movieId)}&source_db=${encodeURIComponent(sourceDb)}`
            })
            .then(response => {
                console.log('📡 Réponse HTTP:', response.status, response.statusText);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text(); // D'abord en text pour voir ce qu'on reçoit
            })
            .then(responseText => {
                console.log('📦 Réponse brute:', responseText);
                
                // Essayer de parser en JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                    console.log('📊 Données parsées:', data);
                } catch (e) {
                    console.error('❌ Erreur parsing JSON:', e);
                    showToast('error', 'Erreur: Réponse serveur invalide');
                    return;
                }
                
                if (data.success && data.url) {
                    // URL trouvée - ouvrir dans un nouvel onglet
                    console.log('🎬 Tentative ouverture de:', data.url);
                    const opened = window.open(data.url, '_blank');
                    
                    if (opened) {
                        showToast('success', `▶️ "${title}" ouvert dans un nouvel onglet`);
                    } else {
                        // Popup bloqué - proposer lien direct
                        showToast('error', 'Popup bloqué. Cliquez sur le lien qui va apparaître.');
                        
                        // Créer un lien temporaire
                        const link = document.createElement('a');
                        link.href = data.url;
                        link.target = '_blank';
                        link.textContent = `Ouvrir "${title}"`;
                        link.style.cssText = 'position:fixed;top:20px;right:20px;z-index:9999;background:#007bff;color:white;padding:10px;border-radius:5px;text-decoration:none;';
                        document.body.appendChild(link);
                        
                        // Supprimer après 10 secondes
                        setTimeout(() => link.remove(), 10000);
                    }
                } else {
                    // Erreur : pas de source trouvée
                    console.log('❌ Erreur dans la réponse:', data);
                    showToast('error', data.message || `Aucune source vidéo trouvée pour "${title}"`);
                }
            })
            .catch(error => {
                console.error('❌ Erreur fetch:', error);
                showToast('error', `Erreur lors de la recherche de la source pour "${title}": ${error.message}`);
            });
        }



        function showFixNamesModal() {
            const modal = new bootstrap.Modal(document.getElementById('fixNamesModal'));
            modal.show();
            
            // Réinitialiser les résultats
            document.getElementById('fixNamesResults').style.display = 'none';
            document.getElementById('fixNamesResultsContent').innerHTML = '';
        }

        function searchWithCustomName() {
            const customInput = document.getElementById('customSearchInput');
            if (!customInput) return;
            
            const customTitle = customInput.value.trim();
            if (!customTitle) {
                showToast('error', 'Veuillez saisir un nom à rechercher');
                return;
            }
            
            // Relancer la recherche avec le nom personnalisé
            // On garde le même type que la recherche originale
            const originalType = getCurrentSearchType();
            searchTmdbCommon(customTitle, originalType);
        }
        
        function getCurrentSearchType() {
            // Cette fonction peut être améliorée pour récupérer le type original
            // Pour l'instant, on assume 'movie' par défaut, mais on pourrait stocker
            // le type original dans une variable globale
            return window.currentSearchType || 'movie';
        }

        function clearTmdbCache() {
            tmdbCache.clear();
            showToast('success', '🗑️ Cache TMDB vidé - Une nouvelle recherche sera effectuée');
            
            // Fermer la modal actuelle
            const modal = bootstrap.Modal.getInstance(document.getElementById('tmdbModal'));
            if (modal) {
                modal.hide();
            }
            
            // Relancer la recherche après un court délai
            setTimeout(() => {
                if (window.currentSearchType && window.lastSearchTitle) {
                    console.log('🔄 Relancement de la recherche TMDB...');
                    searchTmdbCommon(window.lastSearchTitle, window.currentSearchType);
                }
            }, 300);
        }

        function executeFixNames() {
            const form = document.getElementById('fixNamesForm');
            const formData = new FormData(form);
            
            const sourceDb = formData.get('source_db');
            const limit = formData.get('limit');
            
            if (!sourceDb) {
                showToast('error', 'Veuillez sélectionner une base de données');
                return;
            }
            
            const btn = document.getElementById('fixNamesBtn');
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="ph ph-circle-notch ph-spin me-1"></i>Traitement en cours...';
            btn.disabled = true;
            
            // Cacher les anciens résultats
            document.getElementById('fixNamesResults').style.display = 'none';
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=fix_names&source_db=${encodeURIComponent(sourceDb)}&limit=${encodeURIComponent(limit)}`
            })
            .then(response => response.json())
            .then(data => {
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                if (data.success) {
                    showToast('success', data.message);
                    
                    // Afficher les résultats détaillés
                    const results = data.results;
                    let html = `
                        <div class="alert alert-success">
                            <strong>Traitement terminé avec succès !</strong>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="text-primary">${results.processed}</h5>
                                        <small class="text-muted">Éléments traités</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="text-success">${results.updated}</h5>
                                        <small class="text-muted">Mis à jour</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="text-warning">${results.errors.length}</h5>
                                        <small class="text-muted">Erreurs</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    if (results.errors.length > 0) {
                        html += `
                            <div class="alert alert-warning mt-3">
                                <strong>Erreurs rencontrées :</strong>
                                <ul class="mb-0 mt-2">
                                    ${results.errors.map(error => `<li>${error}</li>`).join('')}
                                </ul>
                            </div>
                        `;
                    }
                    
                    document.getElementById('fixNamesResultsContent').innerHTML = html;
                    document.getElementById('fixNamesResults').style.display = 'block';
                    
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                showToast('error', 'Erreur lors du traitement');
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        // Fonctions pour l'édition IPTV
        function openEditIptvModal(itemId, sourceDb) {
            console.log('📝 Ouverture modal édition IPTV:', { itemId, sourceDb });
            
            const modal = new bootstrap.Modal(document.getElementById('editIptvModal'));
            modal.show();
            
            // Afficher le loader
            document.getElementById('editIptvLoading').style.display = 'block';
            document.getElementById('editIptvForm').style.display = 'none';
            document.getElementById('saveIptvBtn').style.display = 'none';
            
            // Charger les données de l'élément
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=get_iptv_item&item_id=${encodeURIComponent(itemId)}&source_db=${encodeURIComponent(sourceDb)}`
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('editIptvLoading').style.display = 'none';
                
                if (data.success) {
                    populateEditForm(data.item, sourceDb);
                    document.getElementById('editIptvForm').style.display = 'block';
                    document.getElementById('saveIptvBtn').style.display = 'inline-block';
                } else {
                    showToast('error', data.message);
                    modal.hide();
                }
            })
            .catch(error => {
                console.error('Erreur lors du chargement:', error);
                document.getElementById('editIptvLoading').style.display = 'none';
                showToast('error', 'Erreur lors du chargement des données');
                modal.hide();
            });
        }

        function populateEditForm(item, sourceDb) {
            console.log('📝 Remplissage du formulaire:', item);
            
            // Remplir les champs
            document.getElementById('editTitle').value = item.title || '';
            document.getElementById('editYear').value = item.year || '';
            document.getElementById('editType').value = item.type || 'movie';
            document.getElementById('editTmdbId').value = item.tmdb_id || '';
            document.getElementById('editPosterPath').value = item.poster_path || '';
            document.getElementById('editPosterImage').value = item.poster_image || '';
            document.getElementById('editItemId').value = item.id;
            document.getElementById('editSourceDb').value = sourceDb;
            
            // Mettre à jour le titre de la modal
            document.getElementById('editIptvModalLabel').innerHTML = `
                <i class="ph ph-pencil me-2"></i>
                Éditer "${item.title}" (${sourceDb})
            `;
            
            // Afficher l'aperçu du poster
            updatePosterPreview();
            
            // Ajouter des event listeners pour l'aperçu en temps réel
            ['editPosterPath', 'editPosterImage'].forEach(fieldId => {
                document.getElementById(fieldId).addEventListener('input', updatePosterPreview);
            });
            

        }

        function updatePosterPreview() {
            const posterPath = document.getElementById('editPosterPath').value;
            const posterImage = document.getElementById('editPosterImage').value;
            const previewContainer = document.getElementById('posterPreviewImage');
            
            let imageUrl = null;
            
            // Priorité : poster_image complet, puis poster_path TMDB
            if (posterImage && posterImage.startsWith('http')) {
                imageUrl = posterImage;
            } else if (posterPath && posterPath.startsWith('/')) {
                imageUrl = 'https://image.tmdb.org/t/p/w300' + posterPath;
            }
            
            if (imageUrl) {
                previewContainer.innerHTML = `<img src="${imageUrl}" alt="Poster preview" style="max-width: 100%; height: auto; border-radius: 4px;" onerror="this.parentElement.innerHTML='<i class=\\'ph ph-image-broken text-muted\\' style=\\'font-size: 3rem;\\'></i><br><small class=\\'text-danger\\'>Image introuvable</small>';">`;
            } else {
                previewContainer.innerHTML = '<i class="ph ph-image text-muted" style="font-size: 3rem;"></i>';
            }
        }

        function saveIptvEdit() {
            console.log('💾 Sauvegarde de l\'édition IPTV...');
            
            const form = document.getElementById('editIptvForm');
            const formData = new FormData(form);
            const btn = document.getElementById('saveIptvBtn');
            const originalText = btn.innerHTML;
            
            // Créer un objet avec les données du formulaire
            const editData = {};
            for (let [key, value] of formData.entries()) {
                if (key !== 'item_id' && key !== 'source_db') {
                    // Gérer les champs optionnels
                    if (key === 'year' && (value === '' || value === null)) {
                        editData[key] = null; // Année peut être vide
                    } else if (['tmdb_id', 'poster_path', 'poster_image'].includes(key) && value === '') {
                        editData[key] = null; // Ces champs peuvent être vides
                    } else {
                        editData[key] = value;
                    }
                }
            }
            
            const itemId = formData.get('item_id');
            const sourceDb = formData.get('source_db');
            
            btn.innerHTML = '<i class="ph ph-circle-notch ph-spin me-1"></i>Sauvegarde...';
            btn.disabled = true;
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=edit_iptv_item&item_id=${encodeURIComponent(itemId)}&source_db=${encodeURIComponent(sourceDb)}&form_data=${encodeURIComponent(JSON.stringify(editData))}`
            })
            .then(response => response.json())
            .then(data => {
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                if (data.success) {
                    showToast('success', data.message);
                    
                    // Fermer la modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editIptvModal'));
                    modal.hide();
                    
                    // Optionnel : recharger la page pour voir les changements
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                console.error('Erreur lors de la sauvegarde:', error);
                showToast('error', 'Erreur lors de la sauvegarde');
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        // Fonctions pour la gestion des sources dans le modal dédié
        function openSourcesModal(movieId, sourceDb, title) {
            console.log('🎬 Ouverture modal sources:', { movieId, sourceDb, title });
            
            // Stocker les données dans les champs cachés
            document.getElementById('currentMovieId').value = movieId;
            document.getElementById('currentMovieSourceDb').value = sourceDb;
            
            // Mettre à jour le titre du modal
            document.getElementById('sourcesModalLabel').innerHTML = `
                <i class="ph ph-database me-2"></i>
                Sources vidéo - "${title}"
            `;
            
            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById('sourcesModal'));
            modal.show();
            
            // Charger les sources
            loadSources(movieId, sourceDb);
        }

        function loadSources(movieId, sourceDb) {
            console.log('🔄 Chargement des sources:', { movieId, sourceDb });
            
            document.getElementById('sourcesLoading').style.display = 'block';
            document.getElementById('sourcesContent').style.display = 'none';
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=get_edit_sources&item_id=${encodeURIComponent(movieId)}&source_db=${encodeURIComponent(sourceDb)}`
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('sourcesLoading').style.display = 'none';
                document.getElementById('sourcesContent').style.display = 'block';
                
                if (data.success) {
                    displaySources(data.sources);
                } else {
                    document.getElementById('sourcesList').innerHTML = `
                        <div class="alert alert-warning">
                            <i class="ph ph-warning me-2"></i>
                            ${data.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Erreur chargement sources:', error);
                document.getElementById('sourcesLoading').style.display = 'none';
                document.getElementById('sourcesContent').style.display = 'block';
                document.getElementById('sourcesList').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="ph ph-x-circle me-2"></i>
                        Erreur lors du chargement des sources
                    </div>
                `;
            });
        }

        function displaySources(sources) {
            const container = document.getElementById('sourcesList');
            
            if (sources.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4 text-muted">
                        <i class="ph ph-film-strip" style="font-size: 3rem; opacity: 0.3;"></i>
                        <h6 class="mt-3 mb-2">Aucune source vidéo</h6>
                        <p class="mb-0">Ajoutez des sources avec le formulaire ci-dessus</p>
                    </div>
                `;
                return;
            }

            let html = '<div class="row g-3">';
            sources.forEach((source, index) => {
                const qualityColor = {
                    '4K': 'danger',
                    '1080p': 'success',
                    '720p': 'primary',
                    'HD': 'info',
                    'SD': 'secondary'
                }[source.quality] || 'secondary';

                const langFlag = {
                    'fr': '🇫🇷',
                    'en': '🇺🇸',
                    'es': '🇪🇸',
                    'de': '🇩🇪',
                    'it': '🇮🇹',
                    'vo': '🌍'
                }[source.language] || '🌍';

                html += `
                    <div class="col-12">
                        <div class="card border-start border-3 border-${qualityColor}">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="badge bg-${qualityColor} me-2">${source.quality}</span>
                                            <span class="me-2 fs-5" title="Langue: ${source.language}">${langFlag}</span>
                                            <small class="text-muted">#${index + 1}</small>
                                        </div>
                                        <div class="text-break small text-muted" title="${source.url}">
                                            <i class="ph ph-link me-1"></i>
                                            ${source.url}
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-outline-danger btn-sm ms-3" 
                                            onclick="deleteSource(${source.id})"
                                            title="Supprimer cette source">
                                        <i class="ph ph-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            container.innerHTML = html;
        }

        function addSource() {
            const movieId = document.getElementById('currentMovieId').value;
            const sourceDb = document.getElementById('currentMovieSourceDb').value;
            const url = document.getElementById('sourceUrl').value;
            const quality = document.getElementById('sourceQuality').value;
            const language = document.getElementById('sourceLanguage').value;

            if (!url) {
                showToast('error', 'Veuillez saisir une URL');
                return;
            }

            const sourceData = { url, quality, language };

            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=add_edit_source&item_id=${encodeURIComponent(movieId)}&source_db=${encodeURIComponent(sourceDb)}&source_data=${encodeURIComponent(JSON.stringify(sourceData))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    // Vider le formulaire
                    document.getElementById('sourceUrl').value = '';
                    document.getElementById('sourceQuality').value = 'HD';
                    document.getElementById('sourceLanguage').value = 'fr';
                    // Recharger la liste
                    loadSources(movieId, sourceDb);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                console.error('Erreur ajout source:', error);
                showToast('error', 'Erreur lors de l\'ajout de la source');
            });
        }

        function deleteSource(sourceId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette source ?')) {
                return;
            }

            const sourceDb = document.getElementById('currentMovieSourceDb').value;
            const movieId = document.getElementById('currentMovieId').value;

            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=delete_edit_source&source_id=${encodeURIComponent(sourceId)}&source_db=${encodeURIComponent(sourceDb)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    loadSources(movieId, sourceDb);
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                console.error('Erreur suppression source:', error);
                showToast('error', 'Erreur lors de la suppression');
            });
        }
    </script>
</body>
</html> 