<?php
// Système de traitement en lot VOD IPTV
$config = require_once 'config.php';
require_once 'search.php'; // Pour réutiliser les fonctions

function getConnection($dbConfig) {
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
    return new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
}

// Traitement AJAX pour les opérations en lot
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'batch_correct_tmdb') {
        $sourceDb = $_POST['source_db'] ?? '';
        $limit = (int) ($_POST['limit'] ?? 10);
        
        if (!$sourceDb) {
            echo json_encode(['success' => false, 'message' => 'Base de données manquante']);
            exit;
        }
        
        try {
            $pdo = getConnection($config[$sourceDb]);
            
            // Récupérer les éléments sans TMDB ID
            if ($sourceDb === 'main_db') {
                $sql = "SELECT id, name as title, type FROM entertainments 
                        WHERE (tmdb_id IS NULL OR tmdb_id = '' OR tmdb_id = '0') 
                        AND deleted_at IS NULL LIMIT {$limit}";
            } else {
                $sql = "SELECT id, title, type FROM poster_iptv 
                        WHERE tmdb_id IS NULL OR tmdb_id = '' OR tmdb_id = '0' 
                        LIMIT {$limit}";
            }
            
            $stmt = $pdo->query($sql);
            $items = $stmt->fetchAll();
            
            $processed = 0;
            $errors = [];
            
            foreach ($items as $item) {
                try {
                    // Recherche TMDB
                    $tmdbResults = searchTmdb($item['title'], $item['type'], $config['tmdb_bearer_token'], $config['tmdb_api_base']);
                    
                    if (!empty($tmdbResults)) {
                        $firstResult = $tmdbResults[0];
                        
                        if ($sourceDb === 'main_db') {
                            // Correction dans entertainments
                            $posterUrl = !empty($firstResult['poster_path']) ? 
                                'https://image.tmdb.org/t/p/w500' . $firstResult['poster_path'] : null;
                            $imdbRating = !empty($firstResult['vote_average']) ? 
                                number_format((float)$firstResult['vote_average'], 1) : null;
                            
                            $updateSql = "UPDATE entertainments SET 
                                        tmdb_id = ?, poster_url = ?, IMDb_rating = ?, updated_at = NOW() 
                                        WHERE id = ?";
                            $updateStmt = $pdo->prepare($updateSql);
                            $updateStmt->execute([$firstResult['id'], $posterUrl, $imdbRating, $item['id']]);
                        } else {
                            // Correction dans IPTV
                            $year = null;
                            if ($item['type'] === 'movie' && !empty($firstResult['release_date'])) {
                                $year = date('Y', strtotime($firstResult['release_date']));
                            } elseif ($item['type'] === 'tvshow' && !empty($firstResult['first_air_date'])) {
                                $year = date('Y', strtotime($firstResult['first_air_date']));
                            }
                            
                            $updateSql = "UPDATE poster_iptv SET 
                                        tmdb_id = ?, poster_path = ?, year = ? 
                                        WHERE id = ?";
                            $updateStmt = $pdo->prepare($updateSql);
                            $updateStmt->execute([
                                $firstResult['id'], 
                                $firstResult['poster_path'] ?? null, 
                                $year,
                                $item['id']
                            ]);
                        }
                        
                        $processed++;
                    }
                    
                    // Petite pause pour éviter de surcharger l'API TMDB
                    usleep(250000); // 0.25 seconde
                    
                } catch (Exception $e) {
                    $errors[] = "Erreur pour '{$item['title']}': " . $e->getMessage();
                }
            }
            
            echo json_encode([
                'success' => true, 
                'processed' => $processed,
                'total' => count($items),
                'errors' => $errors,
                'message' => "Traitement terminé: {$processed}/" . count($items) . " éléments mis à jour"
            ]);
            
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
    
    if ($action === 'batch_add_to_main') {
        $sourceDb = $_POST['source_db'] ?? '';
        $limit = (int) ($_POST['limit'] ?? 5);
        
        if (!in_array($sourceDb, ['iptv_db', 'iptv_db2'])) {
            echo json_encode(['success' => false, 'message' => 'Base source invalide']);
            exit;
        }
        
        try {
            $mainPdo = getConnection($config['main_db']);
            $iptvPdo = getConnection($config[$sourceDb]);
            
            // Récupérer les éléments IPTV avec TMDB ID mais pas encore dans main
            $sql = "SELECT id, title, type, tmdb_id, poster_path FROM poster_iptv 
                    WHERE tmdb_id IS NOT NULL AND tmdb_id != '' AND tmdb_id != '0' 
                    AND NOT EXISTS (
                        SELECT 1 FROM {$config['main_db']['dbname']}.entertainments 
                        WHERE tmdb_id = poster_iptv.tmdb_id AND deleted_at IS NULL
                    ) 
                    LIMIT {$limit}";
            
            $stmt = $iptvPdo->query($sql);
            $items = $stmt->fetchAll();
            
            $processed = 0;
            $errors = [];
            
            foreach ($items as $item) {
                try {
                    // Récupérer les détails TMDB
                    $tmdbType = ($item['type'] === 'tvshow' || $item['type'] === 'series') ? 'tv' : 'movie';
                    $detailUrl = "{$config['tmdb_api_base']}/{$tmdbType}/{$item['tmdb_id']}?language=fr-FR";
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $detailUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Authorization: Bearer ' . $config['tmdb_bearer_token'],
                        'accept: application/json'
                    ]);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                    
                    $response = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    if ($response && $httpCode === 200) {
                        $tmdbData = json_decode($response, true);
                        $tmdbData['media_type'] = $tmdbType;
                        
                        $result = addToMain($mainPdo, $iptvPdo, $tmdbData, $item, $item['id']);
                        if ($result) {
                            $processed++;
                        }
                    }
                    
                    usleep(500000); // 0.5 seconde entre les requêtes
                    
                } catch (Exception $e) {
                    $errors[] = "Erreur pour '{$item['title']}': " . $e->getMessage();
                }
            }
            
            echo json_encode([
                'success' => true, 
                'processed' => $processed,
                'total' => count($items),
                'errors' => $errors,
                'message' => "Ajout en lot terminé: {$processed}/" . count($items) . " éléments ajoutés"
            ]);
            
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Erreur: ' . $e->getMessage()]);
        }
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Traitement en lot - VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/phosphor-icons@2.0.0/src/css/icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="ph ph-lightning me-2"></i>
                Traitement en lot
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="search.php">Recherche</a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ph ph-wrench me-2"></i>
                            Correction TMDB en lot
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            Corrige automatiquement les TMDB ID manquants en recherchant sur l'API TMDB.
                        </p>
                        <div class="mb-3">
                            <label class="form-label">Base de données</label>
                            <select id="correctDb" class="form-select">
                                <option value="main_db">Base principale</option>
                                <option value="iptv_db">IPTV DB principale</option>
                                <option value="iptv_db2">IPTV DB secondaire</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Nombre d'éléments à traiter</label>
                            <select id="correctLimit" class="form-select">
                                <option value="5">5 éléments</option>
                                <option value="10" selected>10 éléments</option>
                                <option value="25">25 éléments</option>
                                <option value="50">50 éléments</option>
                            </select>
                        </div>
                        <button onclick="startBatchCorrect()" id="correctBtn" class="btn btn-warning">
                            <i class="ph ph-play me-2"></i>
                            Commencer la correction
                        </button>
                        <div id="correctProgress" class="mt-3" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                            </div>
                            <small class="text-muted">Traitement en cours...</small>
                        </div>
                        <div id="correctResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="ph ph-plus me-2"></i>
                            Ajout en lot vers main
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            Ajoute automatiquement vers la base principale les éléments IPTV qui ont un TMDB ID valide.
                        </p>
                        <div class="mb-3">
                            <label class="form-label">Base source</label>
                            <select id="addDb" class="form-select">
                                <option value="iptv_db">IPTV DB principale</option>
                                <option value="iptv_db2">IPTV DB secondaire</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Nombre d'éléments à traiter</label>
                            <select id="addLimit" class="form-select">
                                <option value="3">3 éléments</option>
                                <option value="5" selected>5 éléments</option>
                                <option value="10">10 éléments</option>
                                <option value="20">20 éléments</option>
                            </select>
                        </div>
                        <button onclick="startBatchAdd()" id="addBtn" class="btn btn-success">
                            <i class="ph ph-play me-2"></i>
                            Commencer l'ajout
                        </button>
                        <div id="addProgress" class="mt-3" style="display: none;">
                            <div class="progress">
                                <div class="progress-bar bg-success progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                            </div>
                            <small class="text-muted">Traitement en cours...</small>
                        </div>
                        <div id="addResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function startBatchCorrect() {
            const btn = document.getElementById('correctBtn');
            const progress = document.getElementById('correctProgress');
            const result = document.getElementById('correctResult');
            const db = document.getElementById('correctDb').value;
            const limit = document.getElementById('correctLimit').value;
            
            btn.disabled = true;
            progress.style.display = 'block';
            result.innerHTML = '';
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=batch_correct_tmdb&source_db=${db}&limit=${limit}`
            })
            .then(response => response.json())
            .then(data => {
                progress.style.display = 'none';
                btn.disabled = false;
                
                if (data.success) {
                    result.innerHTML = `
                        <div class="alert alert-success">
                            <i class="ph ph-check-circle me-2"></i>
                            ${data.message}
                            ${data.errors.length > 0 ? '<hr><strong>Erreurs:</strong><br>' + data.errors.join('<br>') : ''}
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="ph ph-x-circle me-2"></i>
                            ${data.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                progress.style.display = 'none';
                btn.disabled = false;
                result.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="ph ph-x-circle me-2"></i>
                        Erreur lors du traitement
                    </div>
                `;
            });
        }
        
        function startBatchAdd() {
            const btn = document.getElementById('addBtn');
            const progress = document.getElementById('addProgress');
            const result = document.getElementById('addResult');
            const db = document.getElementById('addDb').value;
            const limit = document.getElementById('addLimit').value;
            
            btn.disabled = true;
            progress.style.display = 'block';
            result.innerHTML = '';
            
            fetch('', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `action=batch_add_to_main&source_db=${db}&limit=${limit}`
            })
            .then(response => response.json())
            .then(data => {
                progress.style.display = 'none';
                btn.disabled = false;
                
                if (data.success) {
                    result.innerHTML = `
                        <div class="alert alert-success">
                            <i class="ph ph-check-circle me-2"></i>
                            ${data.message}
                            ${data.errors.length > 0 ? '<hr><strong>Erreurs:</strong><br>' + data.errors.join('<br>') : ''}
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="ph ph-x-circle me-2"></i>
                            ${data.message}
                        </div>
                    `;
                }
            })
            .catch(error => {
                progress.style.display = 'none';
                btn.disabled = false;
                result.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="ph ph-x-circle me-2"></i>
                        Erreur lors du traitement
                    </div>
                `;
            });
        }
    </script>
</body>
</html> 