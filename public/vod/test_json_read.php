<?php
// Script de test pour diagnostiquer les problèmes JSON
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Diagnostic des fichiers JSON d'analyse</h2>";

$dataDir = __DIR__ . '/data_download/';
$files = array_merge(
    glob($dataDir . 'analysis_*.json'),      // Anciens fichiers sans préfixe
    glob($dataDir . 'china-analysis_*.json'), // Fichiers IPTV1
    glob($dataDir . 'mega-analysis_*.json')   // Fichiers IPTV2
);

echo "<p><strong>Répertoire:</strong> $dataDir</p>";
echo "<p><strong>Fichiers trouvés:</strong> " . count($files) . "</p>";

if (empty($files)) {
    echo "<p style='color: red;'>❌ Aucun fichier JSON trouvé !</p>";
    exit;
}

// Tester le fichier le plus récent
$latestFile = end($files);
$basename = basename($latestFile);
echo "<p><strong>Fichier testé:</strong> " . $basename . "</p>";

// Détecter le type de base
$databaseType = 'legacy';
if (preg_match('/china-analysis_/', $basename)) {
    $databaseType = 'iptv1 (CHINA)';
} elseif (preg_match('/mega-analysis_/', $basename)) {
    $databaseType = 'iptv2 (MEGA)';
}
echo "<p><strong>Type de base:</strong> " . $databaseType . "</p>";

$fileSize = filesize($latestFile);
echo "<p><strong>Taille:</strong> " . round($fileSize/1024/1024, 1) . " MB</p>";

// Test de lecture
echo "<h3>📖 Test de lecture...</h3>";
$start = microtime(true);

$content = file_get_contents($latestFile);
if ($content === false) {
    echo "<p style='color: red;'>❌ Impossible de lire le fichier</p>";
    exit;
}

echo "<p>✅ Fichier lu en " . round((microtime(true) - $start) * 1000, 2) . "ms</p>";

// Test de décodage JSON
echo "<h3>🔧 Test de décodage JSON...</h3>";
$start = microtime(true);

$data = json_decode($content, true);
$decodeTime = round((microtime(true) - $start) * 1000, 2);

if ($data === null) {
    echo "<p style='color: red;'>❌ Erreur décodage JSON: " . json_last_error_msg() . "</p>";
    exit;
}

echo "<p>✅ JSON décodé en {$decodeTime}ms</p>";

// Analyser la structure
echo "<h3>📊 Structure des données</h3>";
echo "<ul>";
echo "<li><strong>Clés principales:</strong> " . implode(', ', array_keys($data)) . "</li>";

if (isset($data['statistics'])) {
    echo "<li><strong>Statistiques:</strong> ✅";
    $stats = $data['statistics'];
    echo "<ul>";
    foreach ($stats as $key => $value) {
        echo "<li>$key: " . number_format($value) . "</li>";
    }
    echo "</ul></li>";
} else {
    echo "<li><strong>Statistiques:</strong> ❌ Manquantes</li>";
}

echo "<li><strong>Films:</strong> " . (isset($data['movies']) ? count($data['movies']) : 'NON DÉFINI') . "</li>";
echo "<li><strong>Séries:</strong> " . (isset($data['series']) ? count($data['series']) : 'NON DÉFINI') . "</li>";

if (isset($data['metadata'])) {
    echo "<li><strong>Métadonnées:</strong> ✅";
    echo "<ul>";
    if (isset($data['metadata']['target_database'])) {
        echo "<li>Base cible: " . $data['metadata']['target_database'] . "</li>";
    }
    if (isset($data['metadata']['database_prefix'])) {
        echo "<li>Préfixe: " . $data['metadata']['database_prefix'] . "</li>";
    }
    if (isset($data['metadata']['analysis_date'])) {
        echo "<li>Date d'analyse: " . $data['metadata']['analysis_date'] . "</li>";
    }
    echo "</ul></li>";
} else {
    echo "<li><strong>Métadonnées:</strong> ❌ Manquantes</li>";
}

echo "</ul>";

// Test d'un échantillon
if (isset($data['movies']) && count($data['movies']) > 0) {
    echo "<h3>🎬 Échantillon film</h3>";
    $sample = $data['movies'][0];
    echo "<pre>" . json_encode($sample, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
}

if (isset($data['series']) && count($data['series']) > 0) {
    echo "<h3>📺 Échantillon série</h3>";
    $sample = $data['series'][0];
    echo "<pre>" . json_encode($sample, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
}

echo "<h3>✅ Test terminé avec succès</h3>";
?> 