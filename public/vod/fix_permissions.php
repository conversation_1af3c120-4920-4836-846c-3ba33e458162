<?php
/**
 * Script utilitaire pour diagnostiquer et corriger les permissions
 * Système d'import M3U - VOD IPTV
 * 
 * Usage: Accéder via navigateur à /public/vod/fix_permissions.php
 */

header('Content-Type: text/html; charset=utf-8');

// Configuration
$directories = [
    'data_download' => __DIR__ . '/data_download/',
    'temp' => __DIR__ . '/temp/',
    'logs' => __DIR__ . '/logs/',
    'sys_temp' => sys_get_temp_dir() . '/m3u_analysis/'
];

echo '<h1>🔧 Diagnostic et correction des permissions</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .box { border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9; }
</style>';

echo '<div class="box">';
echo '<h2>📊 État actuel des répertoires</h2>';

foreach ($directories as $name => $path) {
    echo "<h3>📁 $name: <code>$path</code></h3>";
    
    if (is_dir($path)) {
        echo '<span class="success">✅ Répertoire existe</span><br>';
        
        if (is_readable($path)) {
            echo '<span class="success">✅ Lecture autorisée</span><br>';
        } else {
            echo '<span class="error">❌ Lecture interdite</span><br>';
        }
        
        if (is_writable($path)) {
            echo '<span class="success">✅ Écriture autorisée</span><br>';
        } else {
            echo '<span class="error">❌ Écriture interdite</span><br>';
        }
        
        $perms = fileperms($path);
        $perms_octal = sprintf('%o', $perms & 0777);
        echo "<span class='info'>📋 Permissions actuelles: $perms_octal</span><br>";
        
        $owner = function_exists('posix_getpwuid') && function_exists('fileowner') ? 
                 posix_getpwuid(fileowner($path))['name'] ?? 'inconnu' : 'inconnu';
        echo "<span class='info'>👤 Propriétaire: $owner</span><br>";
        
    } else {
        echo '<span class="warning">⚠️ Répertoire n\'existe pas</span><br>';
    }
    
    echo '<hr>';
}
echo '</div>';

// Test de création de fichier
echo '<div class="box">';
echo '<h2>🧪 Test de création de fichier</h2>';

foreach ($directories as $name => $path) {
    if ($name === 'sys_temp') continue; // Skip system temp for creation test
    
    echo "<h3>Test dans $name</h3>";
    
    // Créer le répertoire s'il n'existe pas
    if (!is_dir($path)) {
        if (mkdir($path, 0777, true)) {
            echo '<span class="success">✅ Répertoire créé avec succès</span><br>';
        } else {
            echo '<span class="error">❌ Impossible de créer le répertoire</span><br>';
            continue;
        }
    }
    
    // Test de création de fichier
    $testFile = $path . 'test_' . date('YmdHis') . '.txt';
    $testContent = 'Test de permissions - ' . date('Y-m-d H:i:s');
    
    if (file_put_contents($testFile, $testContent)) {
        echo '<span class="success">✅ Création de fichier réussie</span><br>';
        
        // Test de lecture
        if (file_get_contents($testFile) === $testContent) {
            echo '<span class="success">✅ Lecture de fichier réussie</span><br>';
        } else {
            echo '<span class="error">❌ Lecture de fichier échouée</span><br>';
        }
        
        // Nettoyage
        if (unlink($testFile)) {
            echo '<span class="success">✅ Suppression de fichier réussie</span><br>';
        } else {
            echo '<span class="warning">⚠️ Impossible de supprimer le fichier de test</span><br>';
        }
        
    } else {
        echo '<span class="error">❌ Création de fichier échouée</span><br>';
        $error = error_get_last();
        echo '<span class="error">Erreur: ' . ($error['message'] ?? 'Inconnue') . '</span><br>';
    }
    
    echo '<hr>';
}
echo '</div>';

// Actions de correction
if (isset($_GET['action'])) {
    echo '<div class="box">';
    echo '<h2>🔨 Actions de correction</h2>';
    
    switch ($_GET['action']) {
        case 'fix_permissions':
            foreach ($directories as $name => $path) {
                if ($name === 'sys_temp') continue;
                
                echo "<h3>Correction de $name</h3>";
                
                if (!is_dir($path)) {
                    if (mkdir($path, 0777, true)) {
                        echo '<span class="success">✅ Répertoire créé</span><br>';
                    } else {
                        echo '<span class="error">❌ Échec création répertoire</span><br>';
                        continue;
                    }
                }
                
                if (chmod($path, 0777)) {
                    echo '<span class="success">✅ Permissions corrigées (0777)</span><br>';
                } else {
                    echo '<span class="error">❌ Échec correction permissions</span><br>';
                }
            }
            break;
            
        case 'cleanup':
            foreach ($directories as $name => $path) {
                if (!is_dir($path)) continue;
                
                echo "<h3>Nettoyage de $name</h3>";
                
                $files = glob($path . 'test_*.txt');
                foreach ($files as $file) {
                    if (unlink($file)) {
                        echo '<span class="success">✅ Supprimé: ' . basename($file) . '</span><br>';
                    }
                }
                
                $files = glob($path . 'playlist_*.m3u');
                $count = count($files);
                if ($count > 10) { // Garder seulement les 10 plus récents
                    rsort($files); // Trier par nom (date)
                    $filesToDelete = array_slice($files, 10);
                    
                    foreach ($filesToDelete as $file) {
                        if (unlink($file)) {
                            echo '<span class="success">✅ Ancien fichier supprimé: ' . basename($file) . '</span><br>';
                        }
                    }
                }
            }
            break;
    }
    echo '</div>';
}

// Recommandations
echo '<div class="box">';
echo '<h2>💡 Recommandations</h2>';

echo '<h3>🔧 Actions rapides:</h3>';
echo '<a href="?action=fix_permissions" style="background: #007bff; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔨 Corriger les permissions</a> ';
echo '<a href="?action=cleanup" style="background: #28a745; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🧹 Nettoyer les fichiers temporaires</a><br><br>';

echo '<h3>📋 Commandes SSH (si vous avez accès):</h3>';
echo '<pre style="background: #f0f0f0; padding: 10px; border-radius: 5px;">';
echo '# Se placer dans le bon répertoire' . "\n";
echo 'cd ' . __DIR__ . "\n\n";
echo '# Créer les répertoires avec les bonnes permissions' . "\n";
echo 'mkdir -p data_download temp logs' . "\n";
echo 'chmod 755 data_download temp logs' . "\n\n";
echo '# Alternative plus permissive si nécessaire' . "\n";
echo 'chmod 777 data_download temp logs' . "\n\n";
echo '# Vérifier les permissions' . "\n";
echo 'ls -la' . "\n";
echo '</pre>';

echo '<h3>🌐 Configuration serveur partagé:</h3>';
echo '<ul>';
echo '<li>Utilisez le <strong>gestionnaire de fichiers</strong> de votre hébergeur (cPanel, Plesk, etc.)</li>';
echo '<li>Créez les dossiers <code>data_download</code>, <code>temp</code>, <code>logs</code></li>';
echo '<li>Définissez les permissions à <strong>755</strong> ou <strong>777</strong> si nécessaire</li>';
echo '<li>Contactez le support technique si les problèmes persistent</li>';
echo '</ul>';

echo '<h3>🏥 Solutions de fallback:</h3>';
echo '<ul>';
echo '<li>✅ Le système utilise automatiquement <code>' . sys_get_temp_dir() . '</code> si les permissions échouent</li>';
echo '<li>✅ Les analyses sont sauvegardées dans un répertoire temporaire sécurisé</li>';
echo '<li>✅ L\'historique fonctionne même avec les fichiers temporaires</li>';
echo '</ul>';

echo '</div>';

echo '<div class="box">';
echo '<h2>🔄 Actions</h2>';
echo '<a href="import_m3u.php" style="background: #6c757d; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">⬅️ Retour à l\'import M3U</a> ';
echo '<a href="?" style="background: #17a2b8; color: white; padding: 10px; text-decoration: none; border-radius: 5px;">🔄 Rafraîchir le diagnostic</a>';
echo '</div>';

?> 