<?php
// Script de test pour vérifier la structure des bases de données

// Charger la configuration
$config = require_once 'config.php';

function testConnection($dbConfig, $dbName) {
    echo "<h4>Test de {$dbName}</h4>";
    
    try {
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        echo "<div class='alert alert-success'>✓ Connexion réussie à {$dbConfig['dbname']}</div>";
        
        if ($dbName === 'main_db') {
            // Test table entertainments
            $stmt = $pdo->query("SHOW TABLES LIKE 'entertainments'");
            $table = $stmt->fetch();
            if ($table) {
                echo "<div class='alert alert-info'>✓ Table 'entertainments' trouvée</div>";
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM entertainments WHERE deleted_at IS NULL");
                $count = $stmt->fetch()['count'];
                echo "<div class='alert alert-info'>📊 {$count} entertainments trouvés</div>";
                
                // Tester les types
                $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM entertainments WHERE deleted_at IS NULL GROUP BY type");
                while ($row = $stmt->fetch()) {
                    echo "<div class='alert alert-light'>• {$row['type']}: {$row['count']}</div>";
                }
            } else {
                echo "<div class='alert alert-warning'>⚠ Table 'entertainments' non trouvée</div>";
            }
        } else {
            // Test table poster_iptv
            $stmt = $pdo->query("SHOW TABLES LIKE 'poster_iptv'");
            $table = $stmt->fetch();
            if ($table) {
                echo "<div class='alert alert-info'>✓ Table 'poster_iptv' trouvée</div>";
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM poster_iptv");
                $count = $stmt->fetch()['count'];
                echo "<div class='alert alert-info'>📊 {$count} posters IPTV trouvés</div>";
                
                // Tester les types
                $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM poster_iptv GROUP BY type");
                while ($row = $stmt->fetch()) {
                    echo "<div class='alert alert-light'>• {$row['type']}: {$row['count']}</div>";
                }
            } else {
                echo "<div class='alert alert-warning'>⚠ Table 'poster_iptv' non trouvée</div>";
            }
        }
        
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>❌ Erreur de connexion: " . $e->getMessage() . "</div>";
    }
    
    echo "<hr>";
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des bases de données - VOD IPTV</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <h1>Test des bases de données</h1>
                <p class="text-muted">Vérification de la structure et connectivité</p>
                
                <?php
                testConnection($config['main_db'], 'main_db');
                testConnection($config['iptv_db'], 'iptv_db');
                testConnection($config['iptv_db2'], 'iptv_db2');
                ?>
                
                <div class="mt-4">
                    <a href="index.php" class="btn btn-primary">← Retour</a>
                    <a href="search.php" class="btn btn-success">Tester la recherche →</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 