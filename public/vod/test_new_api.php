<?php
// Test de la nouvelle API simple
session_start();

// Simuler des données de test en session
if (!isset($_SESSION['m3u_data'])) {
    // Données de test simple
    $_SESSION['m3u_data'] = [
        'source' => 'test',
        'file_path' => '/test.m3u',
        'channels' => [
            [
                'title' => 'Film Test FR (2023)',
                'tvg_name' => 'film-test-fr',
                'tvg_logo' => '',
                'group_title' => 'Films Français',
                'duration' => -1,
                'is_series' => false,
                'season' => null,
                'episode' => null,
                'year' => 2023,
                'type' => 'movie',
                'url' => 'http://example.com/film.m3u8',
                'line_number' => 1
            ],
            [
                'title' => 'Série Test S01E01',
                'tvg_name' => 'serie-test',
                'tvg_logo' => '',
                'group_title' => 'Séries TV',
                'duration' => -1,
                'is_series' => true,
                'season' => 1,
                'episode' => 1,
                'year' => 2022,
                'type' => 'series',
                'url' => 'http://example.com/serie.m3u8',
                'line_number' => 3
            ],
            [
                'title' => 'Chaîne TV France',
                'tvg_name' => 'tv-france',
                'tvg_logo' => '',
                'group_title' => 'Télévision',
                'duration' => -1,
                'is_series' => false,
                'season' => null,
                'episode' => null,
                'year' => null,
                'type' => 'tv',
                'url' => 'http://example.com/tv.m3u8',
                'line_number' => 5
            ]
        ],
        'total_channels' => 3,
        'filter_stats' => ['filtered' => false],
        'timestamp' => time()
    ];
    echo "✅ Données de test créées: 3 chaînes\n";
}

// Tester l'action get_stats
echo "\n=== Test get_stats ===\n";
$_POST = ['action' => 'get_stats'];
ob_start();
include 'api_simple.php';
$output = ob_get_clean();

echo "Réponse: " . $output . "\n";

$data = json_decode($output, true);
if ($data && $data['success']) {
    echo "✅ API get_stats fonctionne\n";
    echo "Total: " . $data['stats']['total_channels'] . "\n";
    echo "Films: " . $data['stats']['by_type']['movie'] . "\n";
    echo "Séries: " . $data['stats']['by_type']['series'] . "\n";
    echo "TV: " . $data['stats']['by_type']['tv'] . "\n";
} else {
    echo "❌ Erreur get_stats: " . ($data['error'] ?? 'JSON invalide') . "\n";
}

// Tester l'action search
echo "\n=== Test search ===\n";
$_POST = [
    'action' => 'search',
    'query' => 'test',
    'type' => 'all'
];
ob_start();
include 'api_simple.php';
$output = ob_get_clean();

echo "Réponse: " . $output . "\n";

$data = json_decode($output, true);
if ($data && $data['success']) {
    echo "✅ API search fonctionne\n";
    echo "Résultats trouvés: " . count($data['results']) . "\n";
    foreach ($data['results'] as $result) {
        echo "- " . $result['title'] . " (" . $result['type'] . ")\n";
    }
} else {
    echo "❌ Erreur search: " . ($data['error'] ?? 'JSON invalide') . "\n";
}

echo "\n=== Test terminé ===\n";
?> 