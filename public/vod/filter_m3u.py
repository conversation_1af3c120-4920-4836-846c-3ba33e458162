#!/usr/bin/env python3
"""
Filtre M3U optimisé pour VOD IPTV - Version background
Basé sur votre script original avec optimisations pour gros fichiers
"""

import argparse
import re
import os
import json
import time
import sys
from datetime import datetime

class M3UFilter:
    def __init__(self, verbose=False, progress_file=None):
        self.verbose = verbose
        self.progress_file = progress_file
        self.french_patterns = [
            # Format avec tvg-name (format original)
            r'tvg-name="FR[^"]*"',
            
            # Format avec préfixe direct après la virgule
            r',\s*FR\s*[-:|]\s*',  # ,FR - ou ,FR: ou ,FR|
            
            # Format avec crochets ou parenthèses
            r',\s*\[FR\]\s*',      # [FR]
            r',\s*\{FR\}\s*',      # {FR}
            
            # Format dans le titre (insensible à la casse)
            r',.*?FR\s*[-:|]\s*',   # Quelque part dans le titre avec FR -
            
            # Format avec pipe
            r'FR\|\s*',            # FR|
        ]
        
        # Extensions vidéo courantes
        self.video_extensions = ('.mkv', '.mp4', '.m3u8', '.avi', '.mov', '.wmv', '.flv', '.webm')
        
        # Compile les regex pour performance
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.french_patterns]
    
    def log(self, message, level="INFO"):
        """Log avec timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] [{level}] {message}"
        
        if self.verbose:
            print(log_msg)
        
        # Log dans fichier si spécifié
        if hasattr(self, 'log_file') and self.log_file:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_msg + '\n')
    
    def update_progress(self, current, total, french_detected, french_kept, status="processing"):
        """Met à jour le fichier de progression"""
        if not self.progress_file:
            return
            
        progress_data = {
            'status': status,
            'current_line': current,
            'total_lines': total,
            'french_detected': french_detected,
            'french_kept': french_kept,
            'progress_percent': round((current / total) * 100, 1) if total > 0 else 0,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f)
        except Exception as e:
            self.log(f"Erreur mise à jour progression: {e}", "ERROR")
    
    def is_french_channel(self, line):
        """
        Détecte si une ligne EXTINF correspond à une chaîne française.
        Version optimisée avec regex compilées.
        """
        if not line.startswith('#EXTINF'):
            return False
        
        # Vérifier chaque pattern compilé
        for pattern in self.compiled_patterns:
            if pattern.search(line):
                return True
        
        return False
    
    def get_file_line_count(self, file_path):
        """Compte rapidement les lignes d'un fichier"""
        self.log("Comptage des lignes du fichier...")
        
        line_count = 0
        with open(file_path, 'rb') as f:
            for line in f:
                line_count += 1
        
        self.log(f"Fichier contient {line_count:,} lignes")
        return line_count
    
    def filter_french_channels(self, input_file, output_file):
        """
        Filtre les chaînes françaises d'une playlist M3U.
        Version optimisée pour gros fichiers avec progression.
        """
        self.log(f"Début du filtrage: {input_file} -> {output_file}")
        
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"Fichier d'entrée non trouvé: {input_file}")
        
        # Comptage total des lignes pour progression
        total_lines = self.get_file_line_count(input_file)
        
        # Statistiques
        stats = {
            'lines_processed': 0,
            'french_detected': 0,
            'french_kept': 0,
            'original_size': os.path.getsize(input_file),
            'start_time': time.time()
        }
        
        try:
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as infile, \
                 open(output_file, 'w', encoding='utf-8') as outfile:
                
                # Lire la première ligne pour vérifier si c'est #EXTM3U
                first_line = infile.readline().strip()
                if first_line == "#EXTM3U":
                    outfile.write("#EXTM3U\n")
                    self.log("En-tête #EXTM3U détecté et écrit")
                else:
                    # Remettre le pointeur au début si ce n'est pas #EXTM3U
                    infile.seek(0)
                
                keep_line = False
                current_extinf = None
                
                for line_num, line in enumerate(infile, 1):
                    line = line.strip()
                    stats['lines_processed'] = line_num
                    
                    # Mise à jour progression toutes les 5000 lignes
                    if line_num % 5000 == 0:
                        self.update_progress(
                            line_num, total_lines, 
                            stats['french_detected'], stats['french_kept']
                        )
                        if self.verbose:
                            elapsed = time.time() - stats['start_time']
                            rate = line_num / elapsed if elapsed > 0 else 0
                            eta = (total_lines - line_num) / rate if rate > 0 else 0
                            self.log(f"Progression: {line_num:,}/{total_lines:,} lignes "
                                   f"({stats['french_detected']:,} FR détectées, "
                                   f"{stats['french_kept']:,} conservées) "
                                   f"- ETA: {eta:.0f}s")
                    
                    # Utiliser la nouvelle fonction de détection
                    if self.is_french_channel(line):
                        current_extinf = line
                        keep_line = True
                        stats['french_detected'] += 1
                    # Si c'est une URL et qu'on doit garder la ligne précédente
                    elif line.startswith('http') and keep_line:
                        # Vérifie si l'URL se termine par une des extensions vidéo
                        if any(line.lower().endswith(ext) for ext in self.video_extensions):
                            outfile.write(current_extinf + '\n')
                            outfile.write(line + '\n')
                            stats['french_kept'] += 1
                        keep_line = False
                        current_extinf = None
                    # Si c'est une autre ligne (commentaire, métadonnées, etc.)
                    elif not line.startswith('http'):
                        keep_line = False
                        current_extinf = None
        
        except Exception as e:
            self.log(f"Erreur lors du filtrage: {e}", "ERROR")
            self.update_progress(0, total_lines, 0, 0, "error")
            raise
        
        # Statistiques finales
        stats['end_time'] = time.time()
        stats['duration'] = stats['end_time'] - stats['start_time']
        stats['filtered_size'] = os.path.getsize(output_file)
        stats['reduction_percent'] = round((1 - stats['filtered_size'] / stats['original_size']) * 100, 1)
        
        self.log(f"Filtrage terminé en {stats['duration']:.1f}s:")
        self.log(f"  - Lignes traitées: {stats['lines_processed']:,}")
        self.log(f"  - Chaînes FR détectées: {stats['french_detected']:,}")
        self.log(f"  - Chaînes conservées: {stats['french_kept']:,}")
        self.log(f"  - Taille originale: {stats['original_size']:,} octets")
        self.log(f"  - Taille filtrée: {stats['filtered_size']:,} octets")
        self.log(f"  - Réduction: {stats['reduction_percent']}%")
        
        # Mise à jour finale de la progression
        self.update_progress(
            total_lines, total_lines,
            stats['french_detected'], stats['french_kept'],
            "completed"
        )
        
        return stats

def main():
    parser = argparse.ArgumentParser(description='Filtre les chaînes françaises d\'une playlist M3U')
    parser.add_argument('-i', '--input', required=True,
                        help='Fichier M3U d\'entrée')
    parser.add_argument('-o', '--output', required=True,
                        help='Fichier M3U de sortie')
    parser.add_argument('-v', '--verbose', action='store_true',
                        help='Mode verbeux : affiche les détails')
    parser.add_argument('-p', '--progress', 
                        help='Fichier JSON pour suivre la progression')
    parser.add_argument('-l', '--log-file',
                        help='Fichier de log')
    
    args = parser.parse_args()
    
    # Créer le filtre
    filter_instance = M3UFilter(verbose=args.verbose, progress_file=args.progress)
    
    if args.log_file:
        filter_instance.log_file = args.log_file
    
    try:
        # Marquer le début
        if args.progress:
            filter_instance.update_progress(0, 0, 0, 0, "starting")
        
        # Lancer le filtrage
        stats = filter_instance.filter_french_channels(args.input, args.output)
        
        # Succès
        filter_instance.log("✅ Filtrage terminé avec succès")
        
        # Écrire les statistiques finales dans un fichier JSON
        stats_file = args.output + '.stats.json'
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2)
        
        sys.exit(0)
        
    except Exception as e:
        filter_instance.log(f"❌ Erreur fatale: {e}", "ERROR")
        if args.progress:
            filter_instance.update_progress(0, 0, 0, 0, "error")
        sys.exit(1)

if __name__ == "__main__":
    main() 