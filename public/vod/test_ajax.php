<?php
// Test des actions AJAX
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Test des actions AJAX ===\n";

// Simuler une requête POST pour test
$_POST['action'] = 'get_stats';

// Inclure la classe
require_once 'import_m3u_v2.php';

// Test direct de la classe
$importer = new M3UImportV2();

echo "✅ Classe chargée\n";

// Test du fichier test
$testFile = __DIR__ . '/test_playlist.m3u';
if (file_exists($testFile)) {
    echo "✅ Fichier test trouvé\n";
    
    // Parser le contenu
    $result = $importer->parseM3UContent($testFile);
    if (isset($result['channels'])) {
        echo "✅ Parsing réussi: " . count($result['channels']) . " chaînes\n";
        
        // Stocker en session pour test stats
        $_SESSION['m3u_data'] = [
            'source' => 'test',
            'file_path' => $testFile,
            'channels' => $result['channels'],
            'total_channels' => count($result['channels']),
            'filter_stats' => ['filtered' => false],
            'timestamp' => time()
        ];
        
        // Test des statistiques
        $stats = $importer->generateDetailedStats($result['channels']);
        echo "✅ Statistiques générées\n";
        echo "Films: " . $stats['by_type']['movie'] . "\n";
        echo "Séries: " . $stats['by_type']['series'] . "\n";
        echo "TV: " . $stats['by_type']['tv'] . "\n";
        echo "Total: " . $stats['total_channels'] . "\n";
        
        // Test JSON response
        echo "\n=== Test JSON Response ===\n";
        $jsonResponse = json_encode(['success' => true, 'stats' => $stats]);
        if ($jsonResponse) {
            echo "✅ JSON valide généré\n";
            echo "Taille: " . strlen($jsonResponse) . " caractères\n";
        } else {
            echo "❌ Erreur JSON: " . json_last_error_msg() . "\n";
        }
        
    } else {
        echo "❌ Erreur parsing: " . ($result['error'] ?? 'Erreur inconnue') . "\n";
    }
} else {
    echo "❌ Fichier test non trouvé\n";
}

echo "\n=== Test terminé ===\n";
?> 