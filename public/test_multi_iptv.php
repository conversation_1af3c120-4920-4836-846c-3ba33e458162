<?php

/**
 * Script de test pour le système Multi-IPTV
 * À placer à la racine du projet et à exécuter via le navigateur
 */

echo '<h1>Test du système Multi-IPTV</h1>';

// Charger <PERSON> (depuis le dossier public, remonter d'un niveau)
require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Modules\Entertainment\Services\MultiIptvService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

try {
    // Activer temporairement les configurations pour le test
    Config::set('entertainment.iptv_integration', true);
    Config::set('entertainment.multi_iptv_enabled', true);
    
    echo '<h2>1. Test de la configuration</h2>';
    
    // Tester les connexions aux bases de données
    $connections = ['iptv_db', 'iptv_db2'];
    
    foreach ($connections as $connection) {
        try {
            DB::connection($connection)->getPdo();
            echo '<p style="color:green">✅ Connexion ' . $connection . ' : OK</p>';
            
            // Compter les contenus
            $movieCount = DB::connection($connection)
                ->table('poster_iptv')
                ->where('type', 'movie')
                ->count();
            $seriesCount = DB::connection($connection)
                ->table('poster_iptv')
                ->where('type', 'tvshow')
                ->count();
                
            echo '<ul>';
            echo '<li>Films : ' . $movieCount . '</li>';
            echo '<li>Séries : ' . $seriesCount . '</li>';
            echo '</ul>';
            
        } catch (\Exception $e) {
            echo '<p style="color:red">❌ Connexion ' . $connection . ' : ERREUR - ' . $e->getMessage() . '</p>';
        }
    }
    
    echo '<h2>2. Test du service Multi-IPTV</h2>';
    
    $multiIptvService = new MultiIptvService();
    
    // Test de l'activation
    $isEnabled = $multiIptvService->isEnabled();
    echo '<p>Service activé : ' . ($isEnabled ? '<span style="color:green">OUI</span>' : '<span style="color:red">NON</span>') . '</p>';
    
    if ($isEnabled) {
        // Test avec un TMDB ID d'exemple
        $testTmdbId = '998589'; // Remplacez par un ID qui existe dans vos bases
        $testTmdbIdSeries = '17610';
        
        // Vider TOUT le cache Redis Multi-IPTV sur le serveur
        $cacheCleared = 0;
        
        // Vider par TMDB ID utilisés
        $commonTmdbIds = [$testTmdbId, $testTmdbIdSeries];
        foreach ($commonTmdbIds as $tmdbId) {
            if (Cache::forget("multi_iptv_movie_{$tmdbId}")) $cacheCleared++;
            if (Cache::forget("multi_iptv_series_{$tmdbId}")) $cacheCleared++;
        }
        
        // Forcer un redémarrage du service pour les nouveaux logs
        unset($multiIptvService);
        $multiIptvService = new MultiIptvService();
        
        // Vider le cache pour forcer le recalcul des tests spécifiques
        $multiIptvService->clearCache($testTmdbId, 'movie');
        $multiIptvService->clearCache($testTmdbIdSeries, 'series');
        echo '<p style="color:blue">🧹 Cache Redis vidé massivement (' . $cacheCleared . ' entrées supprimées)</p>';
        
        // Vérifier la configuration sur le serveur
        echo '<h5>🔧 Configuration serveur :</h5>';
        echo '<ul>';
        echo '<li>MULTI_IPTV_ENABLED: ' . (config('entertainment.multi_iptv_enabled') ? '✅ true' : '❌ false') . '</li>';
        echo '<li>IPTV_INTEGRATION: ' . (config('entertainment.iptv_integration') ? '✅ true' : '❌ false') . '</li>';
        echo '<li>Cache driver: ' . config('cache.default') . '</li>';
        echo '<li>Environment: ' . app()->environment() . '</li>';
        echo '</ul>';
        
        echo '<h3>Test avec TMDB ID : ' . $testTmdbId . '</h3>';
        
        // Test pour les films
        echo '<h4>Sources de film :</h4>';
        $movieSources = $multiIptvService->getMovieSources($testTmdbId);
        
        if ($movieSources->isNotEmpty()) {
            echo '<p style="color:green">✅ ' . $movieSources->count() . ' sources trouvées</p>';
            echo '<ul>';
            foreach ($movieSources->take(3) as $source) {
                echo '<li>Source ' . $source['iptv_source'] . ' (ID: ' . $source['id'] . ') - Qualité: ' . $source['quality'] . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p style="color:orange">⚠️ Aucune source de film trouvée</p>';
        }
        
        // Test pour les séries
        echo '<h4>Données de série :</h4>';
        
        // Debug : Vérifier manuellement les deux bases
        echo '<h5>🔍 Debug - Vérification manuelle des bases :</h5>';
        
        try {
            $poster1 = DB::connection('iptv_db')->table('poster_iptv')->where('tmdb_id', $testTmdbIdSeries)->where('type', 'tvshow')->first();
            $poster2 = DB::connection('iptv_db2')->table('poster_iptv')->where('tmdb_id', $testTmdbIdSeries)->where('type', 'tvshow')->first();
            
            echo '<ul>';
            echo '<li>iptv_db : ' . ($poster1 ? "✅ Trouvé (ID: {$poster1->id})" : "❌ Pas trouvé") . '</li>';
            echo '<li>iptv_db2 : ' . ($poster2 ? "✅ Trouvé (ID: {$poster2->id})" : "❌ Pas trouvé") . '</li>';
            
            if ($poster1 && $poster2) {
                // Compter TOUTES les saisons de chaque base
                $totalSeasons1 = DB::connection('iptv_db')->table('season_iptv')->where('poster_iptv_id', $poster1->id)->count();
                $totalSeasons2 = DB::connection('iptv_db2')->table('season_iptv')->where('poster_iptv_id', $poster2->id)->count();
                echo '<li>iptv_db Total saisons : ' . $totalSeasons1 . '</li>';
                echo '<li>iptv_db2 Total saisons : ' . $totalSeasons2 . '</li>';
                
                // Compter les sources pour le premier épisode de chaque base
                $season1 = DB::connection('iptv_db')->table('season_iptv')->where('poster_iptv_id', $poster1->id)->first();
                $season2 = DB::connection('iptv_db2')->table('season_iptv')->where('poster_iptv_id', $poster2->id)->first();
                
                if ($season1) {
                    $episode1 = DB::connection('iptv_db')->table('episode_iptv')->where('season_iptv_id', $season1->id)->where('episode_number', 1)->first();
                    if ($episode1) {
                        $sources1 = DB::connection('iptv_db')->table('source_episode_iptv')->where('episode_iptv_id', $episode1->id)->count();
                        echo '<li>iptv_db Episode 1 : ' . $sources1 . ' sources</li>';
                    }
                }
                
                if ($season2) {
                    $episode2 = DB::connection('iptv_db2')->table('episode_iptv')->where('season_iptv_id', $season2->id)->where('episode_number', 1)->first();
                    if ($episode2) {
                        $sources2 = DB::connection('iptv_db2')->table('source_episode_iptv')->where('episode_iptv_id', $episode2->id)->count();
                        echo '<li>iptv_db2 Episode 1 : ' . $sources2 . ' sources</li>';
                        
                        // Comparer les URLs
                        if ($episode1 && $episode2) {
                            $url1 = DB::connection('iptv_db')->table('source_episode_iptv')->where('episode_iptv_id', $episode1->id)->first()->url ?? 'N/A';
                            $url2 = DB::connection('iptv_db2')->table('source_episode_iptv')->where('episode_iptv_id', $episode2->id)->first()->url ?? 'N/A';
                            
                            echo '<li>URL iptv_db: ' . substr($url1, 0, 50) . '...</li>';
                            echo '<li>URL iptv_db2: ' . substr($url2, 0, 50) . '...</li>';
                            echo '<li>URLs identiques: ' . ($url1 === $url2 ? '⚠️ OUI' : '✅ NON') . '</li>';
                        }
                    }
                }
            }
            echo '</ul>';
            
        } catch (\Exception $e) {
            echo '<p style="color:red">Erreur debug : ' . $e->getMessage() . '</p>';
        }
        
        // FORCER le bypass complet du cache pour ce test
        Config::set('cache.default', 'array'); // Utiliser cache en mémoire au lieu de Redis
        
        $seriesData = $multiIptvService->getTvShowData($testTmdbIdSeries);
        
        // Test manuel DIRECT pour bypass complet du service
        echo '<h5>🔧 Test manuel direct (bypass service) :</h5>';
        try {
            // Tester directement getSeriesFromDatabase
            $reflection = new ReflectionClass($multiIptvService);
            $method = $reflection->getMethod('getSeriesFromDatabase');
            $method->setAccessible(true);
            
            $directData1 = $method->invoke($multiIptvService, 'iptv_db', $testTmdbIdSeries);
            $directData2 = $method->invoke($multiIptvService, 'iptv_db2', $testTmdbIdSeries);
            
            echo '<ul>';
            echo '<li>Direct iptv_db : ' . ($directData1 ? count($directData1) . ' saisons' : 'NULL') . '</li>';
            echo '<li>Direct iptv_db2 : ' . ($directData2 ? count($directData2) . ' saisons' : 'NULL') . '</li>';
            echo '</ul>';
            
        } catch (\Exception $e) {
            echo '<p style="color:red">Erreur test direct : ' . $e->getMessage() . '</p>';
        }
        
        // Afficher les derniers logs pour debug
        echo '<h5>📋 Logs Multi-IPTV :</h5>';
        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile)) {
            $logs = file_get_contents($logFile);
            $multiIptvLogs = array_filter(explode("\n", $logs), function($line) {
                return strpos($line, 'MultiIptv Service:') !== false;
            });
            $recentLogs = array_slice($multiIptvLogs, -5); // 5 derniers logs
            echo '<div style="background-color: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">';
            foreach ($recentLogs as $log) {
                echo htmlspecialchars($log) . '<br>';
            }
            echo '</div>';
        }
        
        if ($seriesData) {
            echo '<p style="color:green">✅ ' . count($seriesData) . ' saisons trouvées</p>';
            
            // Debug : compter le total de sources
            $totalSources = 0;
            $totalEpisodes = 0;
            foreach ($seriesData as $season) {
                $totalEpisodes += count($season['episodes']);
                foreach ($season['episodes'] as $episode) {
                    $totalSources += count($episode['sources']);
                }
            }
            echo '<p style="color:blue">📊 Debug: ' . $totalEpisodes . ' épisodes total, ' . $totalSources . ' sources total</p>';
            echo '<p style="color:blue">📊 Moyenne: ' . round($totalSources / $totalEpisodes, 2) . ' sources par épisode</p>';
            
            foreach ($seriesData as $index => $season) {
                if ($index >= 2) {
                    echo '<p>... et ' . (count($seriesData) - 2) . ' autres saisons</p>';
                    break;
                }
                
                $totalSources = 0;
                foreach ($season['episodes'] as $episode) {
                    $totalSources += count($episode['sources']);
                }
                
                echo '<ul>';
                echo '<li><strong>Saison ' . $season['season_number'] . '</strong> : ' . count($season['episodes']) . ' épisodes, ' . $totalSources . ' sources total</li>';
                
                // Afficher le premier épisode
                if (!empty($season['episodes'])) {
                    $firstEpisode = $season['episodes'][0];
                    echo '<ul>';
                    echo '<li>Premier épisode : ' . $firstEpisode['name'] . ' (' . count($firstEpisode['sources']) . ' sources)</li>';
                    
                    // Afficher les sources de cet épisode
                    foreach ($firstEpisode['sources'] as $sourceIndex => $source) {
                        if ($sourceIndex >= 2) {
                            echo '<ul><li>... et ' . (count($firstEpisode['sources']) - 2) . ' autres sources</li></ul>';
                            break;
                        }
                        echo '<ul><li>Source ' . $source['iptv_source'] . ' - ' . $source['quality'] . '</li></ul>';
                    }
                    echo '</ul>';
                }
                echo '</ul>';
            }
        } else {
            echo '<p style="color:orange">⚠️ Aucune série trouvée</p>';
        }
        
        // Test des statistiques
        echo '<h3>Statistiques des bases IPTV :</h3>';
        $stats = $multiIptvService->getStats();
        
        foreach ($stats as $connection => $stat) {
            echo '<h4>' . $connection . '</h4>';
            if ($stat['connected']) {
                echo '<p style="color:green">✅ Connecté</p>';
                echo '<ul>';
                echo '<li>Films : ' . $stat['movies'] . '</li>';
                echo '<li>Séries : ' . $stat['series'] . '</li>';
                echo '<li>Total : ' . $stat['total'] . '</li>';
                echo '</ul>';
            } else {
                echo '<p style="color:red">❌ Déconnecté : ' . $stat['error'] . '</p>';
            }
        }
    }
    
    echo '<h2>3. Test de performance</h2>';
    
    if ($isEnabled) {
        $startTime = microtime(true);
        
        // Test de cache
        $testTmdbId = '998589';
        $testTmdbIdSeries = '17610';
        $multiIptvService->getMovieSources($testTmdbId);
        $multiIptvService->getTvShowData($testTmdbIdSeries);
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000; // en millisecondes
        
        echo '<p>Temps d\'exécution : ' . round($executionTime, 2) . ' ms</p>';
        
        if ($executionTime < 1000) {
            echo '<p style="color:green">✅ Performance excellente (< 1s)</p>';
        } elseif ($executionTime < 3000) {
            echo '<p style="color:orange">⚠️ Performance acceptable (< 3s)</p>';
        } else {
            echo '<p style="color:red">❌ Performance lente (> 3s)</p>';
        }
        
        // Test de cache (deuxième appel)
        $startTime2 = microtime(true);
        $multiIptvService->getMovieSources($testTmdbId);
        $multiIptvService->getTvShowData($testTmdbIdSeries);
        $endTime2 = microtime(true);
        $cacheTime = ($endTime2 - $startTime2) * 1000;
        
        echo '<p>Temps d\'exécution avec cache : ' . round($cacheTime, 2) . ' ms</p>';
        echo '<p>Accélération : x' . round($executionTime / $cacheTime, 1) . '</p>';
    }
    
} catch (\Exception $e) {
    echo '<h2 style="color:red">Erreur</h2>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

echo '<p style="margin-top: 30px;"><strong>Test terminé à ' . date('Y-m-d H:i:s') . '</strong></p>'; 