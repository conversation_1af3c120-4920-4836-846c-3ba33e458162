<?php

require_once '../vendor/autoload.php';

// Charger l'application Laravel
$app = require_once '../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Modules\Entertainment\Services\UrlScrapperService;

// HTML de test (basé sur votre exemple)
$testHtml = '
<div class="tabsbox filmlinks ignore-select">
    <div class="sadst" style="text-align: center; margin-bottom: 23px;">
        <p style="color: #d90000">Si vous rencontrez des problèmes de lecture, veuillez désactiver adblock</p>
        <p style="color: #0000ff">Connectez vous à votre compte flemmix, les liens seront plus vite à jour et le cache supprimé</p>
    </div>
    <div class="tabs-sel linkstab" oncontextmenu="if (!window.__cfRLUnblockHandlers) return false; return false" data-cf-modified-3862bb2873e163dd6723a5d4-="">       
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://ups2up.fun/embed-op7mt3u9sgkw.html\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>USTR</span></a>   
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://vide0.net/e/q11t42e5wx3j\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>DdStream</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://luluvdoo.com/e/e9l39hb4aqw3\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>LuLuTV</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://jilliandescribecompany.com/e/qwz6zqt9pk80\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>Voe</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://tipfly.xyz/em-519368-h9ykj6cspz4v\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>One</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://uqload.net/embed-pj9itqbg76hv.html\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>uqload</span></a>                     
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://waaw1.tv/e/iEeO0av4fz5P\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>netu</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://vidmoly.to/embed-7xcsy91cvtoz.html\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>Vmoly</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://movearnpre.com/v/6gonush85g6k\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>Filelions</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://streamtape.com/e/MXkbp78K0pUYrx\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>stape</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://dhcplay.com/e/bkao8j1o48ke\')" data-cf-modified-3862bb2873e163dd6723a5d4-=""><span>Swish</span></a>
    </div>
</div>
';

echo "<h1>Test du service de scrapping d'URLs</h1>";
echo "<hr>";

// Tester le service
$scrapperService = new UrlScrapperService();
$result = $scrapperService->extractStreamingUrls($testHtml);

echo "<h2>Résultat du scrapping :</h2>";
echo "<pre>";
print_r($result);
echo "</pre>";

if ($result['success'] && !empty($result['urls'])) {
    echo "<h3>URLs extraites :</h3>";
    echo "<table border='1' cellpadding='10' cellspacing='0' style='width: 100%; border-collapse: collapse;'>";
    echo "<tr><th>Source</th><th>Type</th><th>URL</th></tr>";
    
    foreach ($result['urls'] as $urlData) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($urlData['source']) . "</td>";
        echo "<td>" . htmlspecialchars($urlData['type']) . "</td>";
        echo "<td style='word-break: break-all;'>" . htmlspecialchars($urlData['url']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p style='color: red;'>Aucune URL trouvée ou erreur lors du scrapping.</p>";
}

echo "<hr>";
echo "<p><em>Test terminé à " . date('Y-m-d H:i:s') . "</em></p>"; 