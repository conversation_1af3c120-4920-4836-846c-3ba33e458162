<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Intégration IPTV
    |--------------------------------------------------------------------------
    |
    | Cette option contrôle si l'intégration IPTV est activée. Lorsqu'elle est
    | activée, le système recherchera des sources supplémentaires dans la
    | base de données IPTV.
    |
    */
    'iptv_integration' => env('ENABLE_IPTV_INTEGRATION', false),
    
    /*
    |--------------------------------------------------------------------------
    | Multi-IPTV avec Base Standard
    |--------------------------------------------------------------------------
    |
    | Active la fusion intelligente entre les bases IPTV multiples et la base
    | standard. Cela permet de combiner les sources IPTV avec les nouvelles
    | saisons de la base standard.
    |
    */
    'multi_iptv_enabled' => env('ENABLE_MULTI_IPTV', false),
    
    /*
    |--------------------------------------------------------------------------
    | Priorité des sources
    |--------------------------------------------------------------------------
    |
    | Cette option contrôle comment les sources IPTV sont intégrées aux sources
    | existantes. 'append' les ajoute à la fin, 'prepend' les met au début.
    |
    */
    'iptv_source_priority' => env('IPTV_SOURCE_PRIORITY', 'append'),
]; 