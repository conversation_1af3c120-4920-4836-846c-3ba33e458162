<?php

/**
 * Script de test pour le SeriesScrapperService
 * Ce script teste les regex et la logique de parsing
 */

// Test des patterns WIFLIX
function testWiflixPattern() {
    $html = '
    <div class="ep1vf" style="display: none">
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://ups2up.fun/embed-rerhoyqrjy2y.html\')" data-cf-modified-75379522bc1ec8a77234d532-=""><span class="clichost">Lecteur 1</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://vide0.net/e/41dl217q1jvo\')" data-cf-modified-75379522bc1ec8a77234d532-=""><span class="clichost">Lecteur 2</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://jilliandescribecompany.com/e/ofuruawujgif\')" data-cf-modified-75379522bc1ec8a77234d532-=""><span class="clichost">Lecteur 3</span></a>
    </div>
    <div class="ep2vf" style="display: none">
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://ups2up.fun/embed-a2z7g3b1h2j8.html\')" data-cf-modified-75379522bc1ec8a77234d532-=""><span class="clichost">Lecteur 1</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://vide0.net/e/mbgd1ozoi360\')" data-cf-modified-75379522bc1ec8a77234d532-=""><span class="clichost">Lecteur 2</span></a>
        <a onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo(\'https://jilliandescribecompany.com/e/fkatfxavbwmj\')" data-cf-modified-75379522bc1ec8a77234d532-=""><span class="clichost">Lecteur 3</span></a>
    </div>';
    
    echo "=== Test WIFLIX ===\n";
    
    // Test de détection des épisodes
    preg_match_all('/<div\s+class="ep(\d+)(vf|vs|vo)"[^>]*>(.*?)<\/div>/s', $html, $episodeMatches, PREG_SET_ORDER);
    
    echo "Épisodes trouvés : " . count($episodeMatches) . "\n";
    
    foreach ($episodeMatches as $episodeMatch) {
        $episodeNumber = (int)$episodeMatch[1];
        $language = $episodeMatch[2];
        $episodeContent = $episodeMatch[3];
        
        echo "Épisode $episodeNumber ($language) :\n";
        
        // Test extraction des sources
        preg_match_all('/onclick="[^"]*loadVideo\([\'"]([^\'"]+)[\'"]\)[^"]*"[^>]*>.*?<span[^>]*class="clichost"[^>]*>([^<]+)<\/span>/s', $episodeContent, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $url = html_entity_decode($match[1]);
            $readerName = trim($match[2]);
            echo "  - $readerName: $url\n";
        }
        echo "\n";
    }
}

// Test des patterns FRENCH-STREAM
function testFrenchStreamPattern() {
    $html = '
    <button class="watch-button" data-episode="10-vf" data-url="https://uqload.net/embed-s5a8zvzdzawn.html">
    <button class="watch-button" data-episode="10-vf" data-url="https://vidzy.org/embed-iypij14ezfyv.html">
    <button class="watch-button" data-episode="11-vf" data-url="https://uqload.net/embed-xyz123.html">
    <button class="watch-button" data-episode="11-vs" data-url="https://vidzy.org/embed-abc456.html">
    ';
    
    echo "=== Test FRENCH-STREAM ===\n";
    
    // Simuler DOMDocument
    $dom = new DOMDocument();
    @$dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
    
    $xpath = new DOMXPath($dom);
    $buttons = $xpath->query('//button[@class="watch-button"][@data-episode][@data-url]');
    
    echo "Boutons trouvés : " . $buttons->length . "\n";
    
    $episodes = [];
    
    foreach ($buttons as $button) {
        $dataEpisode = $button->getAttribute('data-episode');
        $dataUrl = $button->getAttribute('data-url');
        
        if (preg_match('/^(\d+)-(vf|vs|vo)$/', $dataEpisode, $matches)) {
            $episodeNumber = (int)$matches[1];
            $language = $matches[2];
            
            echo "Épisode $episodeNumber ($language): $dataUrl\n";
            
            // Test filtre VF seulement
            if ($language === 'vf') {
                // Test domaines autorisés
                $allowedDomains = ['uqload.net', 'vidzy.org'];
                $isAllowed = false;
                
                foreach ($allowedDomains as $domain) {
                    if (strpos($dataUrl, $domain) !== false) {
                        $isAllowed = true;
                        break;
                    }
                }
                
                echo "  -> " . ($isAllowed ? "AUTORISÉ" : "REFUSÉ") . "\n";
            } else {
                echo "  -> IGNORÉ (pas VF)\n";
            }
        }
    }
}

// Test de détection du type de site
function testSiteDetection() {
    echo "=== Test Détection de Site ===\n";
    
    $wiflixHtml = '<div class="ep1vf" style="display: none">';
    $frenchStreamHtml = '<button class="watch-button" data-episode="10-vf" data-url="test">';
    
    if (strpos($frenchStreamHtml, 'data-episode') !== false && strpos($frenchStreamHtml, 'watch-button') !== false) {
        echo "FRENCH-STREAM détecté correctement\n";
    }
    
    if (preg_match('/class="ep\d+vf"/', $wiflixHtml)) {
        echo "WIFLIX détecté correctement\n";
    }
}

// Exécuter les tests
echo "Script de test pour SeriesScrapperService\n";
echo "========================================\n\n";

testSiteDetection();
echo "\n";
testWiflixPattern();
echo "\n";
testFrenchStreamPattern();

echo "\n=== Fin des tests ===\n"; 