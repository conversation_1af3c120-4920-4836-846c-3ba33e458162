<?php

/**
 * Script de test pour le système Multi-IPTV
 * À placer à la racine du projet et à exécuter via le navigateur
 */

echo '<h1>Test du système Multi-IPTV</h1>';

// Charger Laravel
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Modules\Entertainment\Services\MultiIptvService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

try {
    // Activer temporairement les configurations pour le test
    Config::set('entertainment.iptv_integration', true);
    Config::set('entertainment.multi_iptv_enabled', true);
    
    echo '<h2>1. Test de la configuration</h2>';
    
    // Tester les connexions aux bases de données
    $connections = ['iptv_db', 'iptv_db2'];
    
    foreach ($connections as $connection) {
        try {
            DB::connection($connection)->getPdo();
            echo '<p style="color:green">✅ Connexion ' . $connection . ' : OK</p>';
            
            // Compter les contenus
            $movieCount = DB::connection($connection)
                ->table('poster_iptv')
                ->where('type', 'movie')
                ->count();
            $seriesCount = DB::connection($connection)
                ->table('poster_iptv')
                ->where('type', 'tvshow')
                ->count();
                
            echo '<ul>';
            echo '<li>Films : ' . $movieCount . '</li>';
            echo '<li>Séries : ' . $seriesCount . '</li>';
            echo '</ul>';
            
        } catch (\Exception $e) {
            echo '<p style="color:red">❌ Connexion ' . $connection . ' : ERREUR - ' . $e->getMessage() . '</p>';
        }
    }
    
    echo '<h2>2. Test du service Multi-IPTV</h2>';
    
    $multiIptvService = new MultiIptvService();
    
    // Test de l'activation
    $isEnabled = $multiIptvService->isEnabled();
    echo '<p>Service activé : ' . ($isEnabled ? '<span style="color:green">OUI</span>' : '<span style="color:red">NON</span>') . '</p>';
    
    if ($isEnabled) {
        // Test avec un TMDB ID d'exemple
        $testTmdbId = '84363'; // Remplacez par un ID qui existe dans vos bases
        
        echo '<h3>Test avec TMDB ID : ' . $testTmdbId . '</h3>';
        
        // Test pour les films
        echo '<h4>Sources de film :</h4>';
        $movieSources = $multiIptvService->getMovieSources($testTmdbId);
        
        if ($movieSources->isNotEmpty()) {
            echo '<p style="color:green">✅ ' . $movieSources->count() . ' sources trouvées</p>';
            echo '<ul>';
            foreach ($movieSources->take(3) as $source) {
                echo '<li>Source ' . $source['iptv_source'] . ' (ID: ' . $source['id'] . ') - Qualité: ' . $source['quality'] . '</li>';
            }
            echo '</ul>';
        } else {
            echo '<p style="color:orange">⚠️ Aucune source de film trouvée</p>';
        }
        
        // Test pour les séries
        echo '<h4>Données de série :</h4>';
        $seriesData = $multiIptvService->getTvShowData($testTmdbId);
        
        if ($seriesData) {
            echo '<p style="color:green">✅ ' . count($seriesData) . ' saisons trouvées</p>';
            
            foreach ($seriesData as $index => $season) {
                if ($index >= 2) {
                    echo '<p>... et ' . (count($seriesData) - 2) . ' autres saisons</p>';
                    break;
                }
                
                $totalSources = 0;
                foreach ($season['episodes'] as $episode) {
                    $totalSources += count($episode['sources']);
                }
                
                echo '<ul>';
                echo '<li><strong>Saison ' . $season['season_number'] . '</strong> : ' . count($season['episodes']) . ' épisodes, ' . $totalSources . ' sources total</li>';
                
                // Afficher le premier épisode
                if (!empty($season['episodes'])) {
                    $firstEpisode = $season['episodes'][0];
                    echo '<ul>';
                    echo '<li>Premier épisode : ' . $firstEpisode['name'] . ' (' . count($firstEpisode['sources']) . ' sources)</li>';
                    
                    // Afficher les sources de cet épisode
                    foreach ($firstEpisode['sources'] as $sourceIndex => $source) {
                        if ($sourceIndex >= 2) {
                            echo '<ul><li>... et ' . (count($firstEpisode['sources']) - 2) . ' autres sources</li></ul>';
                            break;
                        }
                        echo '<ul><li>Source ' . $source['iptv_source'] . ' - ' . $source['quality'] . '</li></ul>';
                    }
                    echo '</ul>';
                }
                echo '</ul>';
            }
        } else {
            echo '<p style="color:orange">⚠️ Aucune série trouvée</p>';
        }
        
        // Test des statistiques
        echo '<h3>Statistiques des bases IPTV :</h3>';
        $stats = $multiIptvService->getStats();
        
        foreach ($stats as $connection => $stat) {
            echo '<h4>' . $connection . '</h4>';
            if ($stat['connected']) {
                echo '<p style="color:green">✅ Connecté</p>';
                echo '<ul>';
                echo '<li>Films : ' . $stat['movies'] . '</li>';
                echo '<li>Séries : ' . $stat['series'] . '</li>';
                echo '<li>Total : ' . $stat['total'] . '</li>';
                echo '</ul>';
            } else {
                echo '<p style="color:red">❌ Déconnecté : ' . $stat['error'] . '</p>';
            }
        }
    }
    
    echo '<h2>3. Test de performance</h2>';
    
    if ($isEnabled) {
        $startTime = microtime(true);
        
        // Test de cache
        $testTmdbId = '84363';
        $multiIptvService->getMovieSources($testTmdbId);
        $multiIptvService->getTvShowData($testTmdbId);
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000; // en millisecondes
        
        echo '<p>Temps d\'exécution : ' . round($executionTime, 2) . ' ms</p>';
        
        if ($executionTime < 1000) {
            echo '<p style="color:green">✅ Performance excellente (< 1s)</p>';
        } elseif ($executionTime < 3000) {
            echo '<p style="color:orange">⚠️ Performance acceptable (< 3s)</p>';
        } else {
            echo '<p style="color:red">❌ Performance lente (> 3s)</p>';
        }
        
        // Test de cache (deuxième appel)
        $startTime2 = microtime(true);
        $multiIptvService->getMovieSources($testTmdbId);
        $multiIptvService->getTvShowData($testTmdbId);
        $endTime2 = microtime(true);
        $cacheTime = ($endTime2 - $startTime2) * 1000;
        
        echo '<p>Temps d\'exécution avec cache : ' . round($cacheTime, 2) . ' ms</p>';
        echo '<p>Accélération : x' . round($executionTime / $cacheTime, 1) . '</p>';
    }
    
} catch (\Exception $e) {
    echo '<h2 style="color:red">Erreur</h2>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

echo '<p style="margin-top: 30px;"><strong>Test terminé à ' . date('Y-m-d H:i:s') . '</strong></p>';

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Container\Container;
use Illuminate\Database\Capsule\Manager as Capsule;

// Test du MultiIptvService
$service = app(Modules\Entertainment\Services\MultiIptvService::class);

// Test avec un ID TMDB d'exemple
$tmdbId = '1399'; // Game of Thrones

echo "=== Test MultiIptvService avec fusion Standard ===\n\n";

// Test des statistiques
echo "1. Statistiques des bases de données :\n";
$stats = $service->getStats();
foreach ($stats as $dbName => $stat) {
    echo "  - {$dbName}: ";
    if ($stat['connected']) {
        echo "✅ Connecté - {$stat['movies']} films, {$stat['series']} séries ({$stat['type']})\n";
    } else {
        echo "❌ Erreur: {$stat['error']}\n";
    }
}

echo "\n2. Test de récupération de série (TMDB ID: {$tmdbId}) :\n";

// Test de récupération des données
$seriesData = $service->getTvShowData($tmdbId);

if ($seriesData) {
    echo "✅ Série trouvée avec " . count($seriesData) . " saisons :\n";
    
    foreach ($seriesData as $season) {
        $iptvSource = $season['iptv_source'] ?? 'unknown';
        $iptvFlag = $season['is_iptv'] ? '📺' : '🎬';
        
        echo "  {$iptvFlag} Saison {$season['season_number']} ({$iptvSource}) - {$season['total_episodes']} épisodes\n";
        
        // Afficher quelques épisodes
        $episodeCount = min(3, count($season['episodes']));
        for ($i = 0; $i < $episodeCount; $i++) {
            $episode = $season['episodes'][$i];
            $sourceCount = count($episode['sources']);
            echo "    - Episode {$episode['episode_number']}: {$sourceCount} sources\n";
        }
        
        if (count($season['episodes']) > 3) {
            echo "    - ... et " . (count($season['episodes']) - 3) . " autres épisodes\n";
        }
    }
} else {
    echo "❌ Aucune série trouvée\n";
}

echo "\n3. Test du cache :\n";
echo "  - Vider le cache...\n";
$service->clearCache($tmdbId, 'series');
echo "  - ✅ Cache vidé\n";

echo "\n4. Test de re-récupération (depuis cache) :\n";
$start = microtime(true);
$seriesData2 = $service->getTvShowData($tmdbId);
$end = microtime(true);
$duration = round(($end - $start) * 1000, 2);

if ($seriesData2) {
    echo "✅ Série re-récupérée en {$duration}ms avec " . count($seriesData2) . " saisons\n";
} else {
    echo "❌ Échec de re-récupération\n";
}

echo "\n=== Test terminé ===\n"; 