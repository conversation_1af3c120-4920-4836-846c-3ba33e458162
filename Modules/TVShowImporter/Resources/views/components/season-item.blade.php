<div class="accordion-item season-item" id="season-{{ $season->id }}">
    <h2 class="accordion-header">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                data-bs-target="#season-collapse-{{ $season->id }}">
            <div class="d-flex align-items-center w-100">
                <div class="season-number">S{{ str_pad($season->season_index, 2, '0', STR_PAD_LEFT) }}</div>
                <div class="season-info ms-3">
                    <div class="season-name">{{ $season->name }}</div>
                    <div class="season-meta text-muted">
                        {{ $season->episodes->count() }} épisode(s)
                    </div>
                </div>
            </div>
        </button>
    </h2>

    <div id="season-collapse-{{ $season->id }}" class="accordion-collapse collapse" 
         data-bs-parent="#seasonsAccordion">
        <div class="accordion-body position-relative">
            @include('tvshowimporter::components.loading-spinner', ['message' => 'Sauvegarde en cours...'])
            
            <form class="season-form mb-4" action="{{ route('tvshowimporter.update-season', $season->id) }}" 
                  method="POST" data-season-id="{{ $season->id }}">
                @csrf
                @method('PUT')
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Nom de la saison</label>
                        <input type="text" class="form-control" name="season_name" 
                               value="{{ $season->name }}" placeholder="Nom de la saison"
                               data-auto-save>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Numéro de la saison</label>
                        <input type="number" class="form-control" name="season_index" 
                               value="{{ $season->season_index }}" placeholder="Numéro"
                               data-auto-save>
                    </div>
                </div>
            </form>

            <div class="episodes-list" data-season-id="{{ $season->id }}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Episodes</h6>
                

                    <button type="button" class="btn btn-primary btn-sm" 
                            onclick="openAddEpisodeModal({{ $season->id }})" 
                            data-bs-toggle="modal" 
                            data-bs-target="#addEpisodeModal">
                        <i class="ph ph-plus"></i> Ajouter un épisode
                    </button>

                </div>

                <div class="accordion" id="episodesAccordion_{{ $season->id }}">
                    @foreach($season->episodes as $episode)
                        @include('tvshowimporter::components.episode-item', ['episode' => $episode])
                    @endforeach
                </div>
            </div>

            <div class="text-end mt-4">
                <button type="button" class="btn btn-danger btn-sm" 
                        onclick="deleteSeason({{ $season->id }})">
                    <i class="ph ph-trash"></i> Supprimer la saison
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.season-item {
    border: none;
    margin-bottom: 1rem;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
    border-radius: 8px;
    overflow: hidden;
}

.season-number {
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 600;
    color: #666;
}

.season-name {
    font-weight: 500;
    font-size: 1.1rem;
}

.season-meta {
    font-size: 0.875rem;
}

.episodes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}
</style> 