<?php

namespace Modules\TVShowImporter\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Season\Models\Season;
use Modules\Episode\Models\Episode;
use Modules\Entertainment\Models\Entertainment;
use GuzzleHttp\Client;
use Modules\Entertainment\Models\Source;

class TVShowImporterController extends Controller
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://api.themoviedb.org/3/',
            'timeout' => 5.0,
        ]);
    }

    public function importFromTMDB(Request $request)
    {
        $request->validate([
            'tvshow_id' => 'required|exists:entertainments,id',
            'seasons' => 'required|array',
            'seasons.*' => 'required|integer'
        ]);

        try {
            $entertainment = Entertainment::findOrFail($request->tvshow_id);
            
            if (!$entertainment->tmdb_id) {
                throw new \Exception('ID TMDB non défini pour cette série');
            }

            $importedSeasons = [];

            foreach ($request->seasons as $tmdbSeasonNumber) {
                try {
                    $seasonData = $this->getTMDBSeasonData($entertainment->tmdb_id, $tmdbSeasonNumber);
                    
                    // Vérifier si la saison existe déjà
                    $existingSeason = Season::where('entertainment_id', $entertainment->id)
                        ->where('season_index', $seasonData['season_number'])
                        ->first();

                    if ($existingSeason) {
                        continue; // Passer à la suivante si elle existe déjà
                    }

                    $season = Season::create([
                        'entertainment_id' => $entertainment->id,
                        'name' => $seasonData['name'],
                        'season_index' => $seasonData['season_number'],
                        'status' => 1
                    ]);

                    foreach ($seasonData['episodes'] as $episodeData) {
                        Episode::create([
                            'season_id' => $season->id,
                            'name' => $episodeData['name'],
                            'episode_number' => $episodeData['episode_number'],
                            'description' => $episodeData['overview'],
                            'duration' => $this->formatDuration($episodeData['runtime'] ?? 45),
                            'air_date' => $episodeData['air_date'],
                            'status' => 1
                        ]);
                    }

                    $importedSeasons[] = $season;
                } catch (\Exception $e) {
                    \Log::error("Erreur lors de l'importation de la saison {$tmdbSeasonNumber}: " . $e->getMessage());
                    continue;
                }
            }

            if (empty($importedSeasons)) {
                return response()->json(['message' => 'Aucune nouvelle saison importée'], 200);
            }

            return response()->json($importedSeasons);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Erreur lors de l\'importation: ' . $e->getMessage()], 500);
        }
    }

    public function importFromFile(Request $request)
    {
        // Logique d'importation de fichier
    }

    public function updateSeason(Request $request, $id)
    {
        $request->validate([
            'season_name' => 'sometimes|string|max:255',
            'season_index' => 'sometimes|integer|min:1'
        ]);

        $season = Season::findOrFail($id);
        
        $dataToUpdate = [];
        if ($request->has('season_name')) {
            $dataToUpdate['name'] = $request->input('season_name');
        }
        if ($request->has('season_index')) {
            $dataToUpdate['season_index'] = $request->input('season_index');
        }

        if (!empty($dataToUpdate)) {
            $season->update($dataToUpdate);
        }
        
        return response()->json(['success' => true]);
    }

    public function storeSeason(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'number' => 'required|integer|min:1',
            'tvshow_id' => 'required|exists:entertainments,id'
        ]);

        $season = Season::create([
            'entertainment_id' => $request->tvshow_id,
            'name' => $request->name,
            'season_index' => $request->season_index,
            'status' => 1
        ]);

        return response()->json($season);
    }

    public function deleteSeason($id)
    {
        $season = Season::findOrFail($id);
        $season->episodes()->delete(); // Supprimer les épisodes associés
        $season->forceDelete();
        
        return response()->json(['success' => true]);
    }

    public function storeEpisode(Request $request)
    {
        $request->validate([
            'season_id' => 'required|exists:seasons,id'
        ]);

        $maxNumber = Episode::where('season_id', $request->season_id)->max('episode_number') ?? 0;

        $episode = Episode::create([
            'season_id' => $request->season_id,
            'name' => 'Nouvel épisode',
            'episode_number' => $maxNumber + 1,
            'status' => 1
        ]);

        return response()->json($episode);
    }

    public function updateEpisode(Request $request, $id)
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'episode_number' => 'sometimes|integer|min:1',
            'duration' => 'sometimes|string',
            'description' => 'sometimes|string'
        ]);

        $episode = Episode::findOrFail($id);
        
        $episode->updateOrFail($request->only(['name', 'episode_number', 'duration', 'description']));
        
        return response()->json(['success' => true]);
    }

    public function deleteEpisode($id)
    {
        $episode = Episode::findOrFail($id);
        // Supprimer les sources associées si nécessaire
        $episode->delete();
        
        return response()->json(['success' => true]);
    }

    public function getTMDBSeasons($id)
    {
        try {
            $response = $this->client->get("tv/{$id}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . config('services.tmdb.api_token'),
                    'accept' => 'application/json'
                ],
                'query' => [
                    'language' => 'fr-FR'
                ]
            ]);

            $data = json_decode($response->getBody(), true);
            
            if (!isset($data['seasons'])) {
                throw new \Exception('Aucune saison trouvée');
            }

            // Filtrer les saisons spéciales (0) et formatter les données
            $seasons = array_filter($data['seasons'], function($season) {
                return $season['season_number'] > 0;
            });

            return response()->json(array_values($seasons));
        } catch (\Exception $e) {
            return response()->json(['error' => 'Erreur lors de la récupération des saisons: ' . $e->getMessage()], 500);
        }
    }

    public function getTMDBEpisodes($id, $season)
    {
        try {
            $response = $this->client->get("tv/{$id}/season/{$season}", [
                'headers' => [
                    'Authorization' => 'Bearer ' . config('services.tmdb.api_token'),
                    'accept' => 'application/json'
                ],
                'query' => [
                    'language' => 'fr-FR'
                ]
            ]);

            $data = json_decode($response->getBody(), true);
            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Erreur lors de la récupération des épisodes'], 500);
        }
    }

    protected function getTMDBSeasonData($tvshowId, $seasonNumber)
    {
        $response = $this->client->get("tv/{$tvshowId}/season/{$seasonNumber}", [
            'headers' => [
                'Authorization' => 'Bearer ' . config('services.tmdb.api_token'),
                'accept' => 'application/json'
            ],
            'query' => [
                'language' => 'fr-FR'
            ]
        ]);

        $data = json_decode($response->getBody(), true);
        
        if (!isset($data['episodes'])) {
            throw new \Exception('Aucun épisode trouvé pour cette saison');
        }

        return $data;
    }

    protected function formatDuration($minutes)
    {
        $hours = floor($minutes / 60);
        $minutes = $minutes % 60;
        return sprintf('%02d:%02d:00', $hours, $minutes);
    }

    public function getSeasonPartial($id)
    {
        $season = Season::with('episodes')->findOrFail($id);
        return view('tvshowimporter::components.season-item', ['season' => $season])->render();
    }

    public function getEpisodePartial($id)
    {
        $episode = Episode::findOrFail($id);
        return view('tvshowimporter::components.episode-item', compact('episode'));
    }

    public function getSources($episodeId)
    {
        $sources = Source::where('sourceable_id', $episodeId)->where('sourceable_type', Episode::class)->get();
        return response()->json(['sources' => $sources]);
    }

    public function storeSource(Request $request)
    {
        $validated = $request->validate([
            'episode_id' => 'required|exists:episodes,id',
            'title' => 'required|string|max:255',
            'source_type' => 'required|string|in:Streaming,Download,Torrent',
            'url_type' => 'required|string|in:Direct,Embed,M3U8,YouTube,Vimeo',
            'url' => 'required|url',
            'quality' => 'nullable|string|in:SD,HD,FHD,4K',
            'size' => 'nullable|numeric'
        ]);

        $episode = Episode::findOrFail($validated['episode_id']);
        $source = new Source($validated);
        $episode->sources()->save($source);

        return response()->json([
            'message' => 'Source ajoutée avec succès',
            'id' => $source->id
        ]);
    }

    public function deleteSource($sourceId)
    {
        $source = Source::findOrFail($sourceId);
        $source->delete();

        return response()->json(['message' => 'Source supprimée avec succès']);
    }

    public function updateSource(Request $request, $id)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'source_type' => 'required|string|in:Streaming,Download,Torrent',
            'url_type' => 'required|string|in:Direct,Embed,M3U8,YouTube,Vimeo',
            'url' => 'required|url',
            'quality' => 'nullable|string|in:SD,HD,FHD,4K',
            'size' => 'nullable|numeric'
        ]);

        $source = Source::findOrFail($id);
        $source->update($validated);

        return response()->json([
            'message' => 'Source mise à jour avec succès',
            'id' => $source->id
        ]);
    }

    public function editSource($id)
    {
        $source = Source::findOrFail($id);
        return response()->json($source);
    }

    public function getSourcePartial($id)
    {
        $source = Source::findOrFail($id);
        return view('tvshowimporter::components.source-item', compact('source'));
    }
}
