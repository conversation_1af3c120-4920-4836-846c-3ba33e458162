@extends('setting::backend.setting.index')

@section('settings-content')
<form method="POST" action="{{ route('backend.setting.store') }}">
    @csrf

    <div class="card">
        <div class="card-header p-0 mb-4">
            <h4><i class="fa-solid fa-rectangle-ad"></i> {{ __('Ads Settings') }} </h4>
        </div>
        <div class="card-body p-0">
            <!-- Nav tabs -->
            <ul class="nav nav-tabs" id="adsSettingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="ads-types-tab" data-bs-toggle="tab" data-bs-target="#ads-types" type="button" role="tab" aria-controls="ads-types" aria-selected="true">
                        <i class="fa-solid fa-toggle-on"></i> Ads Types
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="ads-config-tab" data-bs-toggle="tab" data-bs-target="#ads-config" type="button" role="tab" aria-controls="ads-config" aria-selected="false">
                        <i class="fa-solid fa-sliders"></i> Ads Configuration
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="unity-ids-tab" data-bs-toggle="tab" data-bs-target="#unity-ids" type="button" role="tab" aria-controls="unity-ids" aria-selected="false">
                        <i class="fa-solid fa-gamepad"></i> Unity Ads IDs
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="admob-ids-tab" data-bs-toggle="tab" data-bs-target="#admob-ids" type="button" role="tab" aria-controls="admob-ids" aria-selected="false">
                        <i class="fa-brands fa-google"></i> AdMob Ads IDs
                    </button>
                </li>
            </ul>

            <!-- Tab panes -->
            <div class="tab-content mt-4" id="adsSettingsTabContent">
                <!-- Ads Types Tab -->
                <div class="tab-pane fade show active" id="ads-types" role="tabpanel" aria-labelledby="ads-types-tab">
                    <div class="row gy-4">
                
                <!-- Interstitial Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Interstitial Ads Type'))->class('form-label') }}
                    {{ html()->select('interstitial_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('interstitial_ads_type', $settings['interstitial_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Interstitial Ads Type') }}
                    @error('interstitial_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Banner Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Banner Ads Type'))->class('form-label') }}
                    {{ html()->select('banner_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('banner_ads_type', $settings['banner_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Banner Ads Type') }}
                    @error('banner_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Reward Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Reward Ads Type'))->class('form-label') }}
                    {{ html()->select('reward_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('reward_ads_type', $settings['reward_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Reward Ads Type') }}
                    @error('reward_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Embed Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Embed Ads Type'))->class('form-label') }}
                    {{ html()->select('embed_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('embed_ads_type', $settings['embed_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Embed Ads Type') }}
                    @error('embed_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Player Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Player Ads Type'))->class('form-label') }}
                    {{ html()->select('player_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('player_ads_type', $settings['player_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Player Ads Type') }}
                    @error('player_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Reward Ads for Embed -->
                <div class="col-md-6">
                    {{ html()->label(__('Reward Ads Type for Embed'))->class('form-label') }}
                    {{ html()->select('reward_ads_type_for_embed', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('reward_ads_type_for_embed', $settings['reward_ads_type_for_embed'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Reward Ads Type for Embed') }}
                    @error('reward_ads_type_for_embed')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Reward Ads for Player -->
                <div class="col-md-6">
                    {{ html()->label(__('Reward Ads Type for Player'))->class('form-label') }}
                    {{ html()->select('reward_ads_type_for_player', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('reward_ads_type_for_player', $settings['reward_ads_type_for_player'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Reward Ads Type for Player') }}
                    @error('reward_ads_type_for_player')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                    </div>
                </div>

                <!-- Ads Configuration Tab -->
                <div class="tab-pane fade" id="ads-config" role="tabpanel" aria-labelledby="ads-config-tab">
                    <div class="row gy-4">

                        <!-- Click Between Interstitial Ad -->
                        <div class="col-md-6">
                            {{ html()->label(__('Click Between Interstitial Ad'))->class('form-label') }}
                            {{ html()->number('click_between_interstitial_ad')
                                ->class('form-control')
                                ->placeholder('Enter number of clicks')
                                ->value(old('click_between_interstitial_ad', $settings['click_between_interstitial_ad'] ?? '5')) }}
                            @error('click_between_interstitial_ad')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Force Ads for Download -->
                        <div class="col-md-6">
                            {{ html()->label(__('Force Ads for Download'))->class('form-label') }}
                            <div class="d-flex justify-content-between align-items-center form-control">
                                {{ html()->label(__('Enable Force Ads for Download'), 'force_ads_for_download')->class('form-label mb-0 text-body') }}
                                <div class="form-check form-switch">
                                    {{ html()->hidden('force_ads_for_download', 0) }}
                                    {{ html()->checkbox('force_ads_for_download', old('force_ads_for_download', $settings['force_ads_for_download'] ?? 0), 1)->class('form-check-input')->id('force_ads_for_download') }}
                                </div>
                            </div>
                            @error('force_ads_for_download')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Show Scrolling Ads -->
                        <div class="col-md-6">
                            {{ html()->label(__('Show Scrolling Ads'))->class('form-label') }}
                            <div class="d-flex justify-content-between align-items-center form-control">
                                {{ html()->label(__('Enable Scrolling Ads'), 'show_scrolling_ads')->class('form-label mb-0 text-body') }}
                                <div class="form-check form-switch">
                                    {{ html()->hidden('show_scrolling_ads', 0) }}
                                    {{ html()->checkbox('show_scrolling_ads', old('show_scrolling_ads', $settings['show_scrolling_ads'] ?? 0), 1)->class('form-check-input')->id('show_scrolling_ads') }}
                                </div>
                            </div>
                            @error('show_scrolling_ads')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Scrolling Ads Pixel -->
                        <div class="col-md-6">
                            {{ html()->label(__('Scrolling Ads Pixel'))->class('form-label') }}
                            {{ html()->number('scrolling_ads_pixel')
                                ->class('form-control')
                                ->placeholder('Enter pixel value')
                                ->value(old('scrolling_ads_pixel', $settings['scrolling_ads_pixel'] ?? '300')) }}
                            @error('scrolling_ads_pixel')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Show Interval Ads in Video -->
                        <div class="col-md-6">
                            {{ html()->label(__('Show Interval Ads in Video'))->class('form-label') }}
                            <div class="d-flex justify-content-between align-items-center form-control">
                                {{ html()->label(__('Enable Interval Ads in Video'), 'show_interval_ads_in_video')->class('form-label mb-0 text-body') }}
                                <div class="form-check form-switch">
                                    {{ html()->hidden('show_interval_ads_in_video', 0) }}
                                    {{ html()->checkbox('show_interval_ads_in_video', old('show_interval_ads_in_video', $settings['show_interval_ads_in_video'] ?? 0), 1)->class('form-check-input')->id('show_interval_ads_in_video') }}
                                </div>
                            </div>
                            @error('show_interval_ads_in_video')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Enabled VOD Download -->
                        <div class="col-md-6">
                            {{ html()->label(__('Enabled VOD Download'))->class('form-label') }}
                            <div class="d-flex justify-content-between align-items-center form-control">
                                {{ html()->label(__('Enable VOD Download'), 'enabled_vod_download')->class('form-label mb-0 text-body') }}
                                <div class="form-check form-switch">
                                    {{ html()->hidden('enabled_vod_download', 0) }}
                                    {{ html()->checkbox('enabled_vod_download', old('enabled_vod_download', $settings['enabled_vod_download'] ?? 0), 1)->class('form-check-input')->id('enabled_vod_download') }}
                                </div>
                            </div>
                            @error('enabled_vod_download')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                    </div>
                </div>

                <!-- Unity Ads IDs Tab -->
                <div class="tab-pane fade" id="unity-ids" role="tabpanel" aria-labelledby="unity-ids-tab">
                    <div class="row gy-4">

                        <!-- Unity Game ID -->
                        <div class="col-md-6">
                            {{ html()->label(__('Unity Game ID'))->class('form-label') }}
                            {{ html()->text('unity_game_id')
                                ->class('form-control')
                                ->placeholder('Enter Unity Game ID')
                                ->value(old('unity_game_id', $settings['unity_game_id'] ?? '')) }}
                            @error('unity_game_id')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Unity Interstitial ID -->
                        <div class="col-md-6">
                            {{ html()->label(__('Unity Interstitial ID'))->class('form-label') }}
                            {{ html()->text('unity_interstitial_id')
                                ->class('form-control')
                                ->placeholder('Enter Unity Interstitial ID')
                                ->value(old('unity_interstitial_id', $settings['unity_interstitial_id'] ?? '')) }}
                            @error('unity_interstitial_id')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Unity Banner ID -->
                        <div class="col-md-6">
                            {{ html()->label(__('Unity Banner ID'))->class('form-label') }}
                            {{ html()->text('unity_banner_id')
                                ->class('form-control')
                                ->placeholder('Enter Unity Banner ID')
                                ->value(old('unity_banner_id', $settings['unity_banner_id'] ?? '')) }}
                            @error('unity_banner_id')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- Unity Reward ID -->
                        <div class="col-md-6">
                            {{ html()->label(__('Unity Reward ID'))->class('form-label') }}
                            {{ html()->text('unity_reward_id')
                                ->class('form-control')
                                ->placeholder('Enter Unity Reward ID')
                                ->value(old('unity_reward_id', $settings['unity_reward_id'] ?? '')) }}
                            @error('unity_reward_id')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                    </div>
                </div>

                <!-- AdMob Ads IDs Tab -->
                <div class="tab-pane fade" id="admob-ids" role="tabpanel" aria-labelledby="admob-ids-tab">
                    <div class="row gy-4">

                        <!-- AdMob Interstitial ID -->
                        <div class="col-md-6">
                            {{ html()->label(__('AdMob Interstitial ID'))->class('form-label') }}
                            {{ html()->text('admob_interstitial_id')
                                ->class('form-control')
                                ->placeholder('Enter AdMob Interstitial ID')
                                ->value(old('admob_interstitial_id', $settings['admob_interstitial_id'] ?? '')) }}
                            @error('admob_interstitial_id')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- AdMob Banner ID -->
                        <div class="col-md-6">
                            {{ html()->label(__('AdMob Banner ID'))->class('form-label') }}
                            {{ html()->text('admob_banner_id')
                                ->class('form-control')
                                ->placeholder('Enter AdMob Banner ID')
                                ->value(old('admob_banner_id', $settings['admob_banner_id'] ?? '')) }}
                            @error('admob_banner_id')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- AdMob Reward ID -->
                        <div class="col-md-6">
                            {{ html()->label(__('AdMob Reward ID'))->class('form-label') }}
                            {{ html()->text('admob_reward_id')
                                ->class('form-control')
                                ->placeholder('Enter AdMob Reward ID')
                                ->value(old('admob_reward_id', $settings['admob_reward_id'] ?? '')) }}
                            @error('admob_reward_id')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        <!-- AdMob Open Ad ID -->
                        <div class="col-md-6">
                            {{ html()->label(__('AdMob Open Ad ID'))->class('form-label') }}
                            {{ html()->text('admob_openad_id')
                                ->class('form-control')
                                ->placeholder('Enter AdMob Open Ad ID')
                                ->value(old('admob_openad_id', $settings['admob_openad_id'] ?? '')) }}
                            @error('admob_openad_id')
                            <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="d-grid d-sm-flex justify-content-sm-end gap-3 mt-4">
        {{ html()->submit(__('messages.save'))->class('btn btn-primary') }}
    </div>
</form>

@push('after-scripts')
<script>
$(document).ready(function() {
    $('.select2').select2({
        width: '100%',
        allowClear: false
    });
});
</script>
@endpush

@endsection
