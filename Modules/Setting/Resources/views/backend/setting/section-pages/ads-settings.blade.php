@extends('setting::backend.setting.index')

@section('settings-content')
<form method="POST" action="{{ route('backend.setting.store') }}">
    @csrf

    <div class="card">
        <div class="card-header p-0 mb-4">
            <h4><i class="fa-solid fa-rectangle-ad"></i> {{ __('Ads Settings') }} </h4>
        </div>
        <div class="card-body p-0">
            <div class="row gy-4">
                
                <!-- Interstitial Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Interstitial Ads Type'))->class('form-label') }}
                    {{ html()->select('interstitial_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('interstitial_ads_type', $settings['interstitial_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Interstitial Ads Type') }}
                    @error('interstitial_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Banner Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Banner Ads Type'))->class('form-label') }}
                    {{ html()->select('banner_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('banner_ads_type', $settings['banner_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Banner Ads Type') }}
                    @error('banner_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Reward Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Reward Ads Type'))->class('form-label') }}
                    {{ html()->select('reward_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('reward_ads_type', $settings['reward_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Reward Ads Type') }}
                    @error('reward_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Embed Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Embed Ads Type'))->class('form-label') }}
                    {{ html()->select('embed_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('embed_ads_type', $settings['embed_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Embed Ads Type') }}
                    @error('embed_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Player Ads -->
                <div class="col-md-6">
                    {{ html()->label(__('Player Ads Type'))->class('form-label') }}
                    {{ html()->select('player_ads_type', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('player_ads_type', $settings['player_ads_type'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Player Ads Type') }}
                    @error('player_ads_type')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Reward Ads for Embed -->
                <div class="col-md-6">
                    {{ html()->label(__('Reward Ads Type for Embed'))->class('form-label') }}
                    {{ html()->select('reward_ads_type_for_embed', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('reward_ads_type_for_embed', $settings['reward_ads_type_for_embed'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Reward Ads Type for Embed') }}
                    @error('reward_ads_type_for_embed')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Reward Ads for Player -->
                <div class="col-md-6">
                    {{ html()->label(__('Reward Ads Type for Player'))->class('form-label') }}
                    {{ html()->select('reward_ads_type_for_player', [
                        'disable' => 'Disable',
                        'unity' => 'Unity',
                        'admob' => 'AdMob',
                        'both' => 'Both'
                    ], old('reward_ads_type_for_player', $settings['reward_ads_type_for_player'] ?? 'disable'))
                        ->class('form-control select2')
                        ->placeholder('Select Reward Ads Type for Player') }}
                    @error('reward_ads_type_for_player')
                    <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

            </div>
        </div>
    </div>

    <div class="d-grid d-sm-flex justify-content-sm-end gap-3 mt-4">
        {{ html()->submit(__('messages.save'))->class('btn btn-primary') }}
    </div>
</form>

@push('after-scripts')
<script>
$(document).ready(function() {
    $('.select2').select2({
        width: '100%',
        allowClear: false
    });
});
</script>
@endpush

@endsection
