@extends('backend.layouts.app')

@section('title') {{ $module_action }} @endsection

@section('content')
<div class="card">
    <div class="card-header">
        <x-backend.section-header>
            <div class="d-flex flex-wrap gap-3">
                <div class="d-flex align-items-center gap-3">
                    <i class="ph ph-bell fs-2"></i>
                    <div>
                        <h4 class="mb-0">{{ $module_title }}</h4>
                        <p class="mb-0 text-muted">{{ $entertainment->name }}</p>
                    </div>
                </div>
            </div>
            <x-slot name="toolbar">
                <a href="{{ url()->previous() }}" class="btn btn-secondary">
                    <i class="ph ph-arrow-left me-1"></i>Retour
                </a>
            </x-slot>
        </x-backend.section-header>
    </div>
    
    <div class="card-body">
        <div class="row">
            <!-- Formulaire de notification -->
            <div class="col-lg-8">
                <form id="notificationForm" method="POST" action="{{ route('backend.entertainments.send-custom-notification', $entertainment->id) }}">
                    @csrf
                    
                    <div class="mb-4">
                        <label for="notification_title" class="form-label">Titre de la notification <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="notification_title" name="title" 
                               value="{{ $entertainment->name }}" required>
                    </div>
                    
                    <div class="mb-4">
                        <label for="notification_body" class="form-label">Message <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="notification_body" name="body" rows="4" required>{{ \Str::limit($entertainment->description, 150) ?: 'Nouveau contenu disponible !' }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="large_icon_url" class="form-label">Icône large (URL)</label>
                                <input type="url" class="form-control" id="large_icon_url" name="large_icon" 
                                       value="{{ $entertainment->poster_url ? (str_starts_with($entertainment->poster_url, 'http') ? $entertainment->poster_url : 'https://streamit.voirfilmshd-admin.top/storage/streamit-laravel/uploads/' . pathinfo($entertainment->poster_url, PATHINFO_EXTENSION) . '/' . $entertainment->poster_url) : '' }}"
                                       placeholder="https://exemple.com/icon.jpg">
                                <div class="form-text">URL de l'icône à afficher dans la notification</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label for="big_image_url" class="form-label">Grande image (URL)</label>
                                <input type="url" class="form-control" id="big_image_url" name="big_image" 
                                       value="{{ $entertainment->poster_url ? (str_starts_with($entertainment->poster_url, 'http') ? $entertainment->poster_url : 'https://streamit.voirfilmshd-admin.top/storage/streamit-laravel/uploads/' . pathinfo($entertainment->poster_url, PATHINFO_EXTENSION) . '/' . $entertainment->poster_url) : '' }}"
                                       placeholder="https://exemple.com/image.jpg">
                                <div class="form-text">
                                    URL de la grande image à afficher dans la notification<br>
                                    <small class="text-info">💡 Note: Les images peuvent prendre quelques secondes à apparaître lors du premier envoi (mise en cache FCM)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Section destinataires -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">Destinataires</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="recipient_type" id="all_users" value="all" checked>
                                    <label class="form-check-label" for="all_users">
                                        <strong>Tous les utilisateurs</strong> (Topic: voir_films_hd)
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="recipient_type" id="test_token" value="token">
                                    <label class="form-check-label" for="test_token">
                                        <strong>Token de test</strong> (pour tester uniquement)
                                    </label>
                                </div>
                            </div>
                            
                            <div id="token_input_section" class="mt-3" style="display: none;">
                                <label for="test_token_value" class="form-label">Token FCM de test</label>
                                <input type="text" class="form-control" id="test_token_value" name="test_token" 
                                       placeholder="Collez votre token FCM ici pour tester">
                                <div class="form-text">Ce token sera utilisé uniquement pour ce test</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="sendBtn">
                            <i class="ph ph-paper-plane-tilt me-1"></i>Envoyer la notification
                        </button>
                        <a href="{{ url()->previous() }}" class="btn btn-secondary">Annuler</a>
                    </div>
                </form>
            </div>
            
            <!-- Aperçu -->
            <div class="col-lg-4">
                <div class="card sticky-top">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="ph ph-eye me-1"></i>Aperçu de la notification
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="notification-preview border rounded p-3">
                            <div class="d-flex align-items-start">
                                <img id="preview_icon" src="" alt="Icône" class="rounded me-2" 
                                     style="width: 40px; height: 40px; object-fit: cover; display: none;">
                                <div class="flex-grow-1">
                                    <h6 id="preview_title" class="mb-1">{{ $entertainment->name }}</h6>
                                    <p id="preview_body" class="mb-2 text-muted small">{{ \Str::limit($entertainment->description, 100) ?: 'Nouveau contenu disponible !' }}</p>
                                    <img id="preview_image" src="" alt="Image" class="img-fluid rounded" 
                                         style="max-width: 100%; display: none;">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>Type:</strong> {{ ucfirst($entertainment->type) }}<br>
                                <strong>ID:</strong> {{ $entertainment->id }}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show position-fixed" 
         style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show position-fixed" 
         style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
@endif
@endsection

@push('after-scripts')
<script>
$(document).ready(function() {
    // Gestion de l'affichage du champ token
    $('input[name="recipient_type"]').on('change', function() {
        if ($(this).val() === 'token') {
            $('#token_input_section').show();
            $('#test_token_value').attr('required', true).attr('name', 'test_token').prop('disabled', false);
        } else {
            $('#token_input_section').hide();
            $('#test_token_value').attr('required', false).removeAttr('name').prop('disabled', true);
        }
    });
    
    // Mise à jour de l'aperçu en temps réel
    function updatePreview() {
        const title = $('#notification_title').val() || 'Titre';
        const body = $('#notification_body').val() || 'Message';
        const iconUrl = $('#large_icon_url').val();
        const imageUrl = $('#big_image_url').val();
        
        $('#preview_title').text(title);
        $('#preview_body').text(body);
        
        // Afficher/masquer l'icône
        if (iconUrl) {
            $('#preview_icon').attr('src', iconUrl).show().on('error', function() {
                $(this).hide();
            });
        } else {
            $('#preview_icon').hide();
        }
        
        // Afficher/masquer l'image
        if (imageUrl) {
            $('#preview_image').attr('src', imageUrl).show().on('error', function() {
                $(this).hide();
            });
        } else {
            $('#preview_image').hide();
        }
    }
    
    // Mettre à jour l'aperçu au chargement
    updatePreview();
    
    // Mettre à jour l'aperçu en temps réel
    $('#notification_title, #notification_body, #large_icon_url, #big_image_url').on('input', updatePreview);
    
    // Gestion de l'envoi du formulaire
    $('#notificationForm').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitBtn = $('#sendBtn');
        const originalText = submitBtn.html();
        
        // Validation
        if (!$('#notification_title').val() || !$('#notification_body').val()) {
            alert('Veuillez remplir le titre et le message');
            return;
        }
        
        if ($('input[name="recipient_type"]:checked').val() === 'token' && !$('#test_token_value').val()) {
            alert('Veuillez saisir un token de test');
            return;
        }
        
        // Désactiver le bouton
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Envoi en cours...');
        
        // Envoyer via AJAX
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.status) {
                    // Afficher le message de succès
                    const successAlert = `
                        <div class="alert alert-success alert-dismissible fade show position-fixed" 
                             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${response.message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    `;
                    $('body').append(successAlert);
                    
                    // Auto-dismiss après 5 secondes
                    setTimeout(function() {
                        $('.alert-success').fadeOut();
                    }, 5000);
                } else {
                    alert('Erreur: ' + (response.message || 'Erreur inconnue'));
                }
            },
            error: function(xhr) {
                let errorMsg = 'Erreur lors de l\'envoi';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                } else if (xhr.status === 422) {
                    errorMsg = 'Données invalides';
                }
                alert(errorMsg);
            },
            complete: function() {
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
@endpush 