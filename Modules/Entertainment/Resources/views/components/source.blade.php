@php
    $constants = Modules\Constant\Models\Constant::whereIn('type', ['upload_type', 'movie_language', 'video_quality', 'source_type'])->get();
    $upload_url_type = $constants->where('type', 'upload_type');
    $source_type = $constants->where('type', 'source_type');
@endphp
<div class="d-flex align-items-center justify-content-between mt-5 pt-4 mb-3">
    <h5>{{ __('source.title') }}</h5>
    {{-- <button type="button" class="btn btn-sm btn-primary"><i class="ph ph-plus-circle"></i> {{__('source.add_source')}}</button> --}}
</div>
<div class="card">
    <div class="card-body">
        <div class="row gy-3">
            <div class="col-md-12">
                {{-- List of sources item with delete and edit icons --}}
                @if(isset($sources) && $sources?->count())
                    @foreach($sources as $source)
                    <div class="row gy-3 video-inputs-container">
                        <div class="col-md-3">
                            {{ html()->label(__('source.source_title'), 'title')->class('form-label') }}
                            {{ html()->text('title[]', $source->title)->class('form-control')->id('title_' . $source->id) }}
                            @error('title')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-3">
                            {{ html()->label(__('source.source_type'), 'source_type')->class('form-label') }}
                            {{ html()->select(
                                    'source_type[]',
                                    $source_type->pluck('name', 'value')->prepend(__('placeholder.lbl_select_source_type'), ''),
                                    $source->source_type,
                                )->class('form-control select2 source_type')->id('source_type_' . $source->id) }}
                            @error('source_type')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-3">
                            {{ html()->label(__('movie.lbl_video_upload_type'), 'source_url_type')->class('form-label') }}
                            {{ html()->select(
                                    'source_url_type[]',
                                    $upload_url_type->pluck('name', 'value')->prepend(__('placeholder.lbl_select_source_type'), ''),
                                    $source->url_type,
                                )->class('form-control select2 source_url_type')->id('source_url_type_' . $source->id) }}
                            @error('source_url_type')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-md-3">
                            {{ html()->label(__('source.url'), 'url')->class('form-label') }}
                            {{ html()->text('source_url[]', $source->url)->class('form-control')->id('source_url_' . $source->id) }}
                            @error('source_url')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-sm-12">
                            <div class="text-end">
                                <button type="button" class="btn btn-secondary-subtle btn-sm fs-4 remove-video-input"><i class="ph ph-trash align-middle"></i></button>
                            </div>
                        </div> 
                    </div>
                    @endforeach
                @else
                <div class="row gy-3 video-inputs-container">
                    <div class="col-md-3">
                        {{ html()->label(__('source.title'), 'title')->class('form-label') }}
                        {{ html()->text('title[]', null)->class('form-control')->id('title_new') }}
                        @error('title')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-md-3">
                        {{ html()->label(__('source.source_type'), 'source_type')->class('form-label') }}
                        {{ html()->select(
                                'source_type[]',
                                $source_type->pluck('name', 'value')->prepend(__('placeholder.lbl_select_source_type'), '')
                            )->class('form-control select2 source_type')->id('source_type_new') }}
                        @error('source_type')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-md-3">
                        {{ html()->label(__('movie.lbl_video_upload_type'), 'source_url_type')->class('form-label') }}
                        {{ html()->select(
                                'source_url_type[]',
                                $upload_url_type->pluck('name', 'value')->prepend(__('placeholder.lbl_select_source_type'), ''),
                                old('source_url_type'),
                            )->class('form-control select2 source_url_type')->id('source_url_type_new') }}
                        @error('source_url_type')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-md-3">
                        {{ html()->label(__('source.url'), 'url')->class('form-label') }}
                        {{ html()->text('source_url[]', null)->class('form-control')->id('source_url_new') }}
                        @error('url')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="col-sm-12">
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary-subtle btn-sm fs-4 remove-video-input d-none"><i class="ph ph-trash align-middle"></i></button>
                        </div>
                    </div> 
                </div>
                @endif
                {{-- End of List of sources item with delete and edit icons --}}
            </div>
            
            <div class="text-end mt-3">
                <button type="button" class="btn btn-sm btn-secondary me-2" data-bs-toggle="modal" data-bs-target="#bulkImportModal">
                    <i class="ph ph-download"></i> Bulk Import
                </button>
                <a id="add_more_source" class="btn btn-sm btn-primary"><i class="ph ph-plus-circle"></i> {{__('episode.lbl_add_more')}}</a>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour le Bulk Import -->
<div class="modal fade" id="bulkImportModal" tabindex="-1" aria-labelledby="bulkImportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkImportModalLabel">
                    <i class="ph ph-download me-2"></i>Import en lot des sources
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="ph ph-info me-2"></i>
                    Vous pouvez coller soit l'URL d'une page web, soit directement le contenu HTML contenant les liens de streaming.
                </div>
                
                <div class="mb-3">
                    <label for="scrappingContent" class="form-label">
                        URL ou contenu HTML <span class="text-danger">*</span>
                    </label>
                    <textarea 
                        id="scrappingContent" 
                        class="form-control" 
                        rows="8" 
                        placeholder="Collez ici l'URL de la page (ex: https://example.com/movie) ou le contenu HTML contenant les liens de streaming..."
                    ></textarea>
                    <div class="form-text">
                        Si vous collez une URL, le système récupérera automatiquement le contenu de la page.
                    </div>
                </div>
                
                <div id="scrapResults" class="d-none">
                    <h6 class="mb-3">URLs trouvées :</h6>
                    <div id="urlsList" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                        <!-- Les URLs seront listées ici -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" id="extractUrlsBtn" class="btn btn-primary">
                    <i class="ph ph-magnifying-glass me-2"></i>Extraire les URLs
                </button>
                <button type="button" id="importUrlsBtn" class="btn btn-success d-none">
                    <i class="ph ph-check me-2"></i>Importer les URLs sélectionnées
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const bulkImportModal = document.getElementById('bulkImportModal');
    const scrappingContent = document.getElementById('scrappingContent');
    const extractUrlsBtn = document.getElementById('extractUrlsBtn');
    const importUrlsBtn = document.getElementById('importUrlsBtn');
    const scrapResults = document.getElementById('scrapResults');
    const urlsList = document.getElementById('urlsList');
    
    let extractedUrls = [];

    // Fonction pour extraire les URLs
    extractUrlsBtn.addEventListener('click', async function() {
        const content = scrappingContent.value.trim();
        
        if (!content) {
            alert('Veuillez entrer une URL ou du contenu HTML');
            return;
        }

        // Afficher le loader
        extractUrlsBtn.innerHTML = '<i class="ph ph-circle-notch ph-spin me-2"></i>Extraction en cours...';
        extractUrlsBtn.disabled = true;

        try {
            const response = await fetch('{{ route("backend.url-scrapper.extract") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ content: content })
            });

            const result = await response.json();

            if (result.success) {
                extractedUrls = result.data.urls;
                displayExtractedUrls(extractedUrls);
                scrapResults.classList.remove('d-none');
                importUrlsBtn.classList.remove('d-none');
                
                // Notification de succès
                showNotification('success', result.message);
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'Erreur lors de l\'extraction: ' + error.message);
        } finally {
            // Restaurer le bouton
            extractUrlsBtn.innerHTML = '<i class="ph ph-magnifying-glass me-2"></i>Extraire les URLs';
            extractUrlsBtn.disabled = false;
        }
    });

    // Fonction pour afficher les URLs extraites
    function displayExtractedUrls(urls) {
        urlsList.innerHTML = '';
        
        if (urls.length === 0) {
            urlsList.innerHTML = '<div class="text-center text-muted p-3">Aucune URL trouvée</div>';
            return;
        }

        urls.forEach((urlData, index) => {
            const urlItem = document.createElement('div');
            urlItem.className = 'form-check mb-2 p-2 border rounded';
            urlItem.innerHTML = `
                <input class="form-check-input" type="checkbox" value="${index}" id="url_${index}" checked>
                <label class="form-check-label w-100" for="url_${index}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="fw-medium">${urlData.source}</div>
                            <div class="text-muted small">${urlData.url}</div>
                        </div>
                        <span class="badge bg-secondary">${urlData.type}</span>
                    </div>
                </label>
            `;
            urlsList.appendChild(urlItem);
        });
    }

    // Fonction pour importer les URLs sélectionnées
    importUrlsBtn.addEventListener('click', function() {
        const selectedCheckboxes = urlsList.querySelectorAll('input[type="checkbox"]:checked');
        
        if (selectedCheckboxes.length === 0) {
            alert('Veuillez sélectionner au moins une URL à importer');
            return;
        }

        // Récupérer l'ID du divertissement depuis l'URL
        const urlParams = new URLSearchParams(window.location.search);
        const entertainmentId = {{ isset($data) && isset($data->id) ? $data->id : 'null' }};
        
        if (!entertainmentId) {
            showNotification('error', 'ID de divertissement non trouvé');
            return;
        }

        // Préparer les données des sources sélectionnées
        const sourcesToAdd = [];
        selectedCheckboxes.forEach(checkbox => {
            const index = parseInt(checkbox.value);
            const urlData = extractedUrls[index];
            
            // Extraire le nom de domaine pour le titre
            let extractedTitle = urlData.source;
            try {
                const url = new URL(urlData.url);
                extractedTitle = url.hostname;
            } catch (e) {
                console.error('URL invalide:', urlData.url);
            }
            
            sourcesToAdd.push({
                title: extractedTitle,
                source_type: 'both',
                url_type: 'embed',
                url: urlData.url
            });
        });

        // Afficher le loader
        importUrlsBtn.innerHTML = '<i class="ph ph-circle-notch ph-spin me-2"></i>Enregistrement en cours...';
        importUrlsBtn.disabled = true;

        // Envoyer les données au serveur via AJAX
        fetch('{{ route("backend.sources.bulk-store") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                entertainment_id: entertainmentId,
                sources: sourcesToAdd
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showNotification('success', `${result.count} source(s) ajoutée(s) avec succès`);
                
                // Fermer le modal et rafraîchir la page après un court délai
                bootstrap.Modal.getInstance(bulkImportModal).hide();
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('error', result.message || 'Erreur lors de l\'ajout des sources');
            }
        })
        .catch(error => {
            showNotification('error', 'Erreur lors de l\'ajout des sources: ' + error.message);
        })
        .finally(() => {
            // Restaurer le bouton
            importUrlsBtn.innerHTML = '<i class="ph ph-check me-2"></i>Importer les URLs sélectionnées';
            importUrlsBtn.disabled = false;
        });
    });

    // Fonction de notification (vous pouvez l'adapter selon votre système de notifications)
    function showNotification(type, message) {
        // Implémentation basique - vous pouvez remplacer par votre système de notifications
        if (type === 'success') {
            console.log('✅ ' + message);
        } else {
            console.error('❌ ' + message);
        }
        
        // Ou utiliser une notification toast si disponible
        if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            alert(message);
        }
    }

    // Réinitialiser le modal à la fermeture
    bulkImportModal.addEventListener('hidden.bs.modal', function() {
        scrappingContent.value = '';
        scrapResults.classList.add('d-none');
        importUrlsBtn.classList.add('d-none');
        urlsList.innerHTML = '';
        extractedUrls = [];
    });
});
</script>