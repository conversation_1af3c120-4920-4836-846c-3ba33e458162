<!-- Composant Scrapper de Séries -->
<div class="card mt-4">
    <div class="card-body">
        <div class="d-flex align-items-center justify-content-between mb-3">
            <h6>
                <i class="ph ph-television me-2"></i>
                Scrapper de Séries
            </h6>
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#seriesScrapperModal">
                <i class="ph ph-download"></i> Importer épisodes
            </button>
        </div>
        
        <div class="alert alert-info">
            <i class="ph ph-info me-2"></i>
            Ce scrapper permet d'extraire automatiquement les épisodes et leurs sources depuis WIFLIX et FRENCH-STREAM.
            Il organise les données par épisode et langue (VF/VS).
        </div>
    </div>
</div>

<!-- Modal pour le Scrapper de Séries -->
<div class="modal fade" id="seriesScrapperModal" tabindex="-1" aria-labelledby="seriesScrapperModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="seriesScrapperModalLabel">
                    <i class="ph ph-television me-2"></i>Scrapper de Séries
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Instructions -->
                <div class="alert alert-warning">
                    <h6><i class="ph ph-warning me-2"></i>Instructions :</h6>
                    <ul class="mb-0">
                        <li><strong>WIFLIX :</strong> Copiez l'URL de la page de la série avec les épisodes visibles</li>
                        <li><strong>FRENCH-STREAM :</strong> Copiez l'URL ou le code HTML contenant les boutons d'épisodes</li>
                        <li>Le système détectera automatiquement le type de site</li>
                    </ul>
                </div>
                
                                 <!-- Sélection du type de site et saison -->
                 <div class="row mb-3">
                     <div class="col-md-4">
                         <label for="seriesSiteType" class="form-label">
                             Type de site <span class="text-danger">*</span>
                         </label>
                         <select id="seriesSiteType" class="form-control">
                             <option value="auto">Détection automatique</option>
                             <option value="wiflix">WIFLIX</option>
                             <option value="french-stream">FRENCH-STREAM</option>
                         </select>
                     </div>
                     <div class="col-md-4">
                         <label for="targetSeasonSelect" class="form-label">
                             Saison de destination <span class="text-danger">*</span>
                         </label>
                         <select id="targetSeasonSelect" class="form-control" required>
                             <option value="">Sélectionnez une saison</option>
                             @if(isset($data) && $data->season)
                                 @foreach($data->season as $season)
                                     <option value="{{ $season->id }}">{{ $season->name ?: 'Saison ' . $season->season_index }}</option>
                                 @endforeach
                             @endif
                         </select>
                         <div class="form-text">
                             Les épisodes seront ajoutés à la saison sélectionnée
                         </div>
                     </div>
                     <div class="col-md-4">
                         <label for="seriesLanguageFilter" class="form-label">
                             Langues à importer
                         </label>
                         <select id="seriesLanguageFilter" class="form-control" multiple>
                             <option value="VF" selected>🇫🇷 Version Française (VF)</option>
                             <option value="VOSTFR">🇫🇷📝 Version Française Sous-titrée (VOSTFR)</option>
                             <option value="VS">📝 Version Sous-titrée (VS)</option>
                             <option value="VO">🌍 Version Originale (VO)</option>
                         </select>
                     </div>
                 </div>
                
                <!-- Zone de saisie -->
                <div class="mb-3">
                    <label for="seriesScrapContent" class="form-label">
                        URL ou contenu HTML <span class="text-danger">*</span>
                    </label>
                    <textarea 
                        id="seriesScrapContent" 
                        class="form-control" 
                        rows="8" 
                        placeholder="Collez ici l'URL de la série ou le contenu HTML contenant les épisodes..."
                    ></textarea>
                    <div class="form-text">
                        Exemples : https://wiflix.com/serie-name ou le code HTML de la page
                    </div>
                </div>
                
                <!-- Zone de résultats -->
                <div id="seriesScrapResults" class="d-none">
                    <h6 class="mb-3">
                        <i class="ph ph-check-circle me-2 text-success"></i>
                        Épisodes trouvés :
                    </h6>
                    
                    <!-- Options d'importation -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="ph ph-gear me-2"></i>Options d'importation
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="addToExistingEpisodes">
                                        <label class="form-check-label" for="addToExistingEpisodes">
                                            <i class="ph ph-plus-circle me-1"></i>
                                            Ajouter aux épisodes existants
                                        </label>
                                        <div class="form-text">
                                            Si coché, les sources seront ajoutées aux épisodes existants au lieu de créer de nouveaux épisodes
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="startFromEpisode" class="form-label">
                                        <i class="ph ph-play me-1"></i>
                                        Commencer à partir de l'épisode
                                    </label>
                                    <input type="number" class="form-control" id="startFromEpisode" 
                                           value="1" min="1" placeholder="1">
                                    <div class="form-text">
                                        Ignorer les épisodes précédents et commencer à partir de ce numéro (ex: si vous mettez 5, seuls les épisodes 5, 6, 7... seront importés)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" id="selectAllEpisodes" class="btn btn-sm btn-outline-primary">
                                    <i class="ph ph-check-square"></i> Tout sélectionner
                                </button>
                                <button type="button" id="deselectAllEpisodes" class="btn btn-sm btn-outline-secondary ms-2">
                                    <i class="ph ph-square"></i> Tout désélectionner
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <span id="selectedEpisodesCount" class="badge bg-info">0 épisodes sélectionnés</span>
                            </div>
                        </div>
                    </div>
                    <div id="episodesList" class="border rounded p-3" style="max-height: 400px; overflow-y: auto;">
                        <!-- Les épisodes seront listés ici -->
                    </div>
                </div>
                
                <!-- Zone d'information sur le site détecté -->
                <div id="siteDetectionInfo" class="d-none">
                    <div class="alert alert-success">
                        <i class="ph ph-check-circle me-2"></i>
                        <span id="detectedSiteText"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" id="extractSeriesBtn" class="btn btn-primary">
                    <i class="ph ph-magnifying-glass me-2"></i>Extraire les épisodes
                </button>
                <button type="button" id="importSeriesBtn" class="btn btn-success d-none">
                    <i class="ph ph-check me-2"></i>Importer les épisodes sélectionnés
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const seriesModal = document.getElementById('seriesScrapperModal');
    const seriesContent = document.getElementById('seriesScrapContent');
    const siteTypeSelect = document.getElementById('seriesSiteType');
    const languageFilter = document.getElementById('seriesLanguageFilter');
    const extractBtn = document.getElementById('extractSeriesBtn');
    const importBtn = document.getElementById('importSeriesBtn');
    const resultsDiv = document.getElementById('seriesScrapResults');
    const episodesList = document.getElementById('episodesList');
    const siteDetectionInfo = document.getElementById('siteDetectionInfo');
    const detectedSiteText = document.getElementById('detectedSiteText');
    const selectAllBtn = document.getElementById('selectAllEpisodes');
    const deselectAllBtn = document.getElementById('deselectAllEpisodes');
    const selectedCountBadge = document.getElementById('selectedEpisodesCount');
    
    let extractedEpisodes = [];

    // Initialiser Select2 pour le filtre de langues
    $(languageFilter).select2({
        placeholder: "Sélectionnez les langues à importer",
        allowClear: true,
        closeOnSelect: false
    });

    // Fonction pour extraire les épisodes
    extractBtn.addEventListener('click', async function() {
        const content = seriesContent.value.trim();
        const siteType = siteTypeSelect.value;
        const selectedLanguages = $(languageFilter).val() || ['VF'];
        
        if (!content) {
            showSeriesNotification('error', 'Veuillez entrer une URL ou du contenu HTML');
            return;
        }
        
        if (!selectedLanguages || selectedLanguages.length === 0) {
            showSeriesNotification('error', 'Veuillez sélectionner au moins une langue à importer');
            return;
        }

        // Afficher le loader
        extractBtn.innerHTML = '<i class="ph ph-circle-notch ph-spin me-2"></i>Extraction en cours...';
        extractBtn.disabled = true;

        try {
            const response = await fetch('{{ route("backend.series-scrapper.extract") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ 
                    input: content,
                    site_type: siteType,
                    languages: selectedLanguages
                })
            });

            const result = await response.json();

            if (result.success) {
                extractedEpisodes = result.data;
                displayExtractedEpisodes(extractedEpisodes);
                resultsDiv.classList.remove('d-none');
                importBtn.classList.remove('d-none');
                
                // Afficher les informations sur le site détecté
                detectedSiteText.textContent = `Site détecté : ${result.site_type.toUpperCase()}`;
                siteDetectionInfo.classList.remove('d-none');
                
                showSeriesNotification('success', result.message);
            } else {
                showSeriesNotification('error', result.message);
            }
        } catch (error) {
            showSeriesNotification('error', 'Erreur lors de l\'extraction: ' + error.message);
        } finally {
            // Restaurer le bouton
            extractBtn.innerHTML = '<i class="ph ph-magnifying-glass me-2"></i>Extraire les épisodes';
            extractBtn.disabled = false;
        }
    });

    // Fonction pour afficher les épisodes extraits
    function displayExtractedEpisodes(episodes) {
        episodesList.innerHTML = '';
        
        if (episodes.length === 0) {
            episodesList.innerHTML = '<div class="text-center text-muted p-3">Aucun épisode trouvé</div>';
            return;
        }

        episodes.forEach((episode, episodeIndex) => {
            const episodeDiv = document.createElement('div');
            episodeDiv.className = 'card mb-3';
            
            let languagesHtml = '';
            let totalSources = 0;
            
            Object.keys(episode.languages).forEach(language => {
                const sources = episode.languages[language];
                totalSources += sources.length;
                
                let sourcesHtml = sources.map(source => `
                    <div class="small text-muted ms-3">
                        <i class="ph ph-play-circle me-1"></i>
                        ${source.reader_name || source.source} (${source.type})
                        <br><span class="font-monospace">${source.url}</span>
                    </div>
                `).join('');
                
                languagesHtml += `
                    <div class="mt-2">
                        <span class="badge bg-primary me-2">${language}</span>
                        <small class="text-muted">${sources.length} source(s)</small>
                        ${sourcesHtml}
                    </div>
                `;
            });
            
            episodeDiv.innerHTML = `
                <div class="card-body">
                    <div class="form-check">
                        <input class="form-check-input episode-checkbox" type="checkbox" 
                               value="${episodeIndex}" id="episode_${episodeIndex}" checked>
                        <label class="form-check-label w-100" for="episode_${episodeIndex}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <i class="ph ph-play me-1"></i>
                                        Épisode ${episode.episode_number}
                                    </h6>
                                    <small class="text-muted">${totalSources} source(s) au total</small>
                                    ${languagesHtml}
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            `;
            
            episodesList.appendChild(episodeDiv);
        });
        
        updateSelectedCount();
    }

    // Gestion de la sélection multiple
    selectAllBtn.addEventListener('click', function() {
        episodesList.querySelectorAll('.episode-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        updateSelectedCount();
    });

    deselectAllBtn.addEventListener('click', function() {
        episodesList.querySelectorAll('.episode-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        updateSelectedCount();
    });

    // Mettre à jour le compteur de sélection
    function updateSelectedCount() {
        const selectedCheckboxes = episodesList.querySelectorAll('.episode-checkbox:checked');
        selectedCountBadge.textContent = `${selectedCheckboxes.length} épisode(s) sélectionné(s)`;
        
        // Mettre à jour le statut du bouton d'importation
        importBtn.disabled = selectedCheckboxes.length === 0;
    }

    // Observer les changements de sélection
    episodesList.addEventListener('change', function(e) {
        if (e.target.classList.contains('episode-checkbox')) {
            updateSelectedCount();
        }
    });

         // Fonction pour importer les épisodes sélectionnés
     importBtn.addEventListener('click', async function() {
         const selectedCheckboxes = episodesList.querySelectorAll('.episode-checkbox:checked');
         const selectedLanguages = $(languageFilter).val() || ['VF'];
         const seasonId = document.getElementById('targetSeasonSelect').value;
         const addToExisting = document.getElementById('addToExistingEpisodes').checked;
         const startFromEpisode = parseInt(document.getElementById('startFromEpisode').value) || 1;
         
         if (selectedCheckboxes.length === 0) {
             showSeriesNotification('error', 'Veuillez sélectionner au moins un épisode à importer');
             return;
         }
         
         if (!seasonId) {
             showSeriesNotification('error', 'Veuillez sélectionner une saison de destination');
             return;
         }
 
         // Afficher le loader
         importBtn.innerHTML = '<i class="ph ph-circle-notch ph-spin me-2"></i>Importation en cours...';
         importBtn.disabled = true;
         
         try {
             // Préparer les données à envoyer
             const episodesToImport = [];
             
             selectedCheckboxes.forEach((checkbox, index) => {
                 const episodeIndex = parseInt(checkbox.value);
                 const episode = extractedEpisodes[episodeIndex];
                 
                 // Filtrer : commencer seulement à partir de l'épisode spécifié
                 if (episode.episode_number < startFromEpisode) {
                     return; // Ignorer cet épisode
                 }
                 
                 const episodeData = {
                     episode_number: episode.episode_number, // Garder le numéro original
                     sources: []
                 };
                 
                 // Filtrer par langues sélectionnées
                 Object.keys(episode.languages).forEach(language => {
                     if (selectedLanguages.includes(language)) {
                         episode.languages[language].forEach(source => {
                             episodeData.sources.push({
                                 title: `${source.reader_name || source.source} (${language})`,
                                 url: source.url,
                                 source_type: 'both', // ou autre valeur par défaut
                                 url_type: 'embed',
                                 language: language
                             });
                         });
                     }
                 });
                 
                 if (episodeData.sources.length > 0) {
                     episodesToImport.push(episodeData);
                 }
             });
             
             // Appel AJAX pour créer les épisodes
             const response = await fetch('{{ route("backend.series-scrapper.import-episodes") }}', {
                 method: 'POST',
                 headers: {
                     'Content-Type': 'application/json',
                     'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                 },
                 body: JSON.stringify({
                     season_id: seasonId,
                     episodes: episodesToImport,
                     add_to_existing: addToExisting,
                     start_from_episode: startFromEpisode
                 })
             });
             
             const result = await response.json();
             
             if (result.success) {
                 // Fermer le modal
                 bootstrap.Modal.getInstance(seriesModal).hide();
                 
                 // Notification de succès
                 showSeriesNotification('success', result.message);
                 
                 // Recharger la page pour voir les nouveaux épisodes
                 setTimeout(() => {
                     location.reload();
                 }, 2000);
             } else {
                 showSeriesNotification('error', result.message || 'Erreur lors de l\'importation');
             }
             
         } catch (error) {
             showSeriesNotification('error', 'Erreur lors de l\'importation: ' + error.message);
         } finally {
             // Restaurer le bouton
             importBtn.innerHTML = '<i class="ph ph-check me-2"></i>Importer les épisodes sélectionnés';
             importBtn.disabled = false;
         }
     });

    

    // Fonction de notification
    function showSeriesNotification(type, message) {
        if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            alert(message);
        }
    }

         // Réinitialiser le modal à la fermeture
     seriesModal.addEventListener('hidden.bs.modal', function() {
         seriesContent.value = '';
         siteTypeSelect.value = 'auto';
         document.getElementById('targetSeasonSelect').value = '';
         $(languageFilter).val(['VF']).trigger('change');
         document.getElementById('addToExistingEpisodes').checked = false;
         document.getElementById('startFromEpisode').value = 1;
         resultsDiv.classList.add('d-none');
         importBtn.classList.add('d-none');
         siteDetectionInfo.classList.add('d-none');
         episodesList.innerHTML = '';
         extractedEpisodes = [];
         updateSelectedCount();
     });
});
</script>

<style>
.episode-checkbox {
    transform: scale(1.2);
}

.card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.font-monospace {
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
    word-break: break-all;
}
</style>