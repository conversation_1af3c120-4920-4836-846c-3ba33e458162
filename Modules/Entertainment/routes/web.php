<?php

use Illuminate\Support\Facades\Route;
use Modules\Entertainment\Http\Controllers\Backend\EntertainmentsController;
use Modules\Entertainment\Http\Controllers\Backend\MovieController;
use Modules\Entertainment\Http\Controllers\Backend\ReviewController;
use Modules\Entertainment\Http\Controllers\Backend\TVshowController;
use Modules\Entertainment\Http\Controllers\Backend\IptvSearchController;
use Modules\Entertainment\Http\Controllers\Backend\IptvTestController;
use Modules\Entertainment\Http\Controllers\Backend\IptvSimpleController;
use Modules\Entertainment\Http\Controllers\Backend\IptvPlainController;
use Modules\Entertainment\Http\Controllers\Backend\IptvBasicSearchController;
use Modules\Entertainment\Http\Controllers\Backend\IptvCorrectionController;
use Modules\Entertainment\Http\Controllers\Backend\UrlScrapperController;
use Modules\Entertainment\Http\Controllers\SeriesScrapperController;
use Modules\Entertainment\Http\Controllers\Backend\SourceController;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!

|
*/
/*
*
* Backend Routes
*
* --------------------------------------------------------------------
*/
Route::group(['prefix' => 'app', 'as' => 'backend.', 'middleware' => ['auth','admin']], function () {
    /*
    * These routes need view-backend permission
    * (good if you want to allow more than one group in the backend,
    * then limit the backend features by different roles or permissions)
    *
    * Note: Administrator has all permissions so you do not have to specify the administrator role everywhere.
    */

    /*
     *
     *  Backend Entertainments Routes
     *
     * ---------------------------------------------------------------------
     */

    Route::group(['prefix' => 'entertainments', 'as' => 'entertainments.'],function () {
      Route::get("index_list", [EntertainmentsController::class, 'index_list'])->name("index_list");
      Route::get("index_data", [EntertainmentsController::class, 'index_data'])->name("index_data");
      Route::get('export', [EntertainmentsController::class, 'export'])->name('export');
      Route::post('bulk-action', [EntertainmentsController::class, 'bulk_action'])->name('bulk_action');
      Route::post('restore/{id}', [EntertainmentsController::class, 'restore'])->name('restore');
      Route::post('update-status/{id}', [EntertainmentsController::class, 'update_status'])->name('update_status');
      Route::delete('force-delete/{id}', [EntertainmentsController::class, 'forceDelete'])->name('force_delete');
      Route::get('download-option/{id}', [EntertainmentsController::class, 'downloadOption'])->name('download-option');
      Route::Post('store-downloads/{id}', [EntertainmentsController::class, 'storeDownloads'])->name('store-downloads');
      Route::get('details/{id}', [EntertainmentsController::class, 'details'])->name("details");
      Route::post('send-notification/{id}', [EntertainmentsController::class, 'sendNotification'])->name('send-notification');
      Route::post('send-custom-notification/{id}', [EntertainmentsController::class, 'sendCustomNotification'])->name('send-custom-notification');
      Route::get('notification/{id}', [EntertainmentsController::class, 'notificationPage'])->name('notification-page');
      Route::get('test-notification/{id}', function($id) {
          return response()->json([
              'status' => true, 
              'message' => 'Test réussi pour ID: ' . $id,
              'method' => 'GET test'
          ]);
      })->name('test-notification');
    });
    Route::resource("entertainments", EntertainmentsController::class);


    Route::group(['prefix' => 'movies', 'as' => 'movies.'],function () {
      Route::get("index_list", [MovieController::class, 'index_list'])->name("index_list");
      Route::get('export', [MovieController::class, 'export'])->name('export');
      Route::get("index_data", [MovieController::class, 'index_data'])->name("index_data");
      Route::get('/import-movie/{id}', [MovieController::class, 'ImportMovie'])->name('import-movie');
      Route::post('/generate-description', [MovieController::class, 'GenerateDescription'])->name('generate-description');
    });
    Route::resource("movies", MovieController::class);

    Route::group(['prefix' => 'tvshows', 'as' => 'tvshows.'],function () {
      Route::get("index_list", [TVshowController::class, 'index_list'])->name("index_list");
      Route::get('export', [TVshowController::class, 'export'])->name('export');
      Route::get("index_data", [TVshowController::class, 'index_data'])->name("index_data");
      Route::post('bulk-action', [TVshowController::class, 'bulk_action'])->name('bulk_action');
      Route::get('/import-tvshow/{id}', [TVshowController::class, 'ImportTVshow'])->name('import-tvshow');
    });
    Route::resource("tvshows", TVshowController::class);

    Route::group(['prefix' => 'reviews', 'as' => 'reviews.'],function () {
      Route::post('restore/{id}', [ReviewController::class, 'restore'])->name('restore');
      Route::delete('force_delete/{id}', [ReviewController::class, 'forceDelete'])->name('force_delete');
      Route::get('export', [ReviewController::class, 'export'])->name('export');
      Route::post('bulk-action', [ReviewController::class, 'bulk_action'])->name('bulk_action');
      Route::get("index_data", [ReviewController::class, 'index_data'])->name("index_data");
    });
    Route::resource("reviews", ReviewController::class);

    // Routes pour le scrapping d'URLs
    Route::group(['prefix' => 'url-scrapper', 'as' => 'url-scrapper.'], function () {
        Route::post('/extract', [UrlScrapperController::class, 'extractUrls'])->name('extract');
    });

    // Routes pour le scrapping des séries
    Route::group(['prefix' => 'series-scrapper', 'as' => 'series-scrapper.'], function () {
        Route::post('/extract', [SeriesScrapperController::class, 'extractSeries'])->name('extract');
        Route::post('/import-episodes', [SeriesScrapperController::class, 'importEpisodes'])->name('import-episodes');
    });

    // Routes pour la recherche IPTV
    Route::group(['prefix' => 'iptv-search', 'as' => 'iptv-search.'], function () {
        Route::get('/', [IptvSearchController::class, 'index'])->name('index');
        Route::post('/search', [IptvSearchController::class, 'search'])->name('search');
    });
    
    // Routes pour la recherche IPTV basique intégrée à l'interface d'administration
    Route::group(['prefix' => 'iptv', 'as' => 'iptv.'], function () {
        Route::get('/basic-search', [IptvBasicSearchController::class, 'index'])->name('basic-search');
        Route::post('/basic-search/search', [IptvBasicSearchController::class, 'search'])->name('basic-search.search');
        Route::get('/basic-search/test-connection', [IptvBasicSearchController::class, 'testConnection'])->name('basic-search.test-connection');
        Route::post('/basic-search/search-test', [IptvBasicSearchController::class, 'searchTest'])->name('basic-search.search-test');
        Route::get('/basic-search/direct-test', [IptvBasicSearchController::class, 'directTest'])->name('basic-search.direct-test');
        Route::get('/basic-search/search-get', [IptvBasicSearchController::class, 'searchGet'])->name('basic-search.search-get');
        
        // Routes pour la correction des TMDB IDs
        Route::post('/correct-tmdb-id', [IptvCorrectionController::class, 'correctTmdbId'])->name('correct-tmdb-id');
        Route::get('/tmdb-search', [IptvCorrectionController::class, 'getTmdbSearchResults'])->name('tmdb-search');
        Route::post('/add-to-entertainments', [IptvCorrectionController::class, 'addToEntertainments'])->name('add-to-entertainments');
    });

    // Route de test pour IPTV
    Route::get('/iptv-test', [IptvTestController::class, 'index'])->name('iptv-test');

    // Route simple pour IPTV (sans middlewares complexes)
    Route::get('/iptv-simple', [IptvSimpleController::class, 'index'])->name('iptv-simple');

    // Ajouter la route pour l'ajout en masse de sources
    Route::post('sources/bulk-store', [SourceController::class, 'bulkStore'])->name('sources.bulk-store');

});

// Route publique pour tester l'accès au module IPTV (sans authentification)
Route::get('/iptv-public', [IptvSimpleController::class, 'index']);

// Route directe sans système de vue (sans authentification)
Route::get('/iptv-direct', [IptvPlainController::class, 'index'])->withoutMiddleware(['web', 'auth', 'admin']);

// Routes pour la recherche IPTV simplifiée (sans authentification)
Route::get('/iptv-basic', [IptvBasicSearchController::class, 'index'])->withoutMiddleware(['web', 'auth', 'admin']);
Route::post('/iptv-basic-search', [IptvBasicSearchController::class, 'search'])->withoutMiddleware(['web', 'auth', 'admin']);

// Route pour l'extraction d'URLs
Route::post('url-scrapper/extract', [UrlScrapperController::class, 'extractUrls'])->name('url-scrapper.extract');



