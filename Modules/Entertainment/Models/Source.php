<?php

namespace Modules\Entertainment\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Entertainment\Database\factories\SourceFactory;

class Source extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'source_type',
        'url_type',
        'url',
        'quality',
        'size',
        'description',
        'status',
        'sourceable_type',
        'sourceable_id',
    ];

    public function sourceable()
    {
        return $this->morphTo();
    }
    
    // protected static function newFactory(): SourceFactory
    // {
    //     //return SourceFactory::new();
    // }
}
