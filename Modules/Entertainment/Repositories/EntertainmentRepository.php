<?php

namespace Modules\Entertainment\Repositories;

use Modules\Entertainment\Models\Entertainment;
use Modules\Entertainment\Models\EntertainmentStreamContentMapping;
use Modules\Entertainment\Models\EntertainmentGenerMapping;
use Modules\Entertainment\Models\EntertainmentTalentMapping;
use Modules\Entertainment\Models\EntertainmnetDownloadMapping;
use Modules\Entertainment\Models\Source;

use Auth;
use Modules\Entertainment\Models\EntertainmentCountryMapping;

class EntertainmentRepository implements EntertainmentRepositoryInterface
{
    public function all()
    {
        return Entertainment::all();
    }
    public function movieGenres($id)
    {
        return EntertainmentGenerMapping::where('entertainment_id',$id)->with('genre')->get()->pluck('genre.name')->unique();
    }

    public function moviecountries($id)
    {
        return EntertainmentCountryMapping::where('entertainment_id',$id)->with('country')->get()->pluck('country.name')->unique();
    }


    public function find($id)
    {
        $entertainment = Entertainment::query();

        if (Auth::user()->hasRole('user')) {
            $entertainment->whereNull('deleted_at');
        }

        $genre = $entertainment->withTrashed()->findOrFail($id);

        return $genre;
    }

    public function create(array $data)
    {
        $entertainemnt = Entertainment::create($data);
        $this->saveOrUpdateSourceMappings($entertainemnt, $data);
        return $entertainemnt;
    }

    public function update($id, array $data)
    {
        $entertainment = Entertainment::findOrFail($id);

        // Log pour déboguer
        \Log::info('Repository Update - Données reçues:', [
            'id' => $id,
            'is_iptv_avant' => $entertainment->is_iptv,
            'status_avant' => $entertainment->status,
            'is_iptv_nouveau' => $data['is_iptv'] ?? 'non défini',
            'status_nouveau' => $data['status'] ?? 'non défini',
            'all_data' => $data
        ]);

        // S'assurer que les valeurs des checkboxes sont correctement définies
        $data['is_iptv'] = isset($data['is_iptv']) ? (bool)$data['is_iptv'] : false;
        $data['status'] = isset($data['status']) ? (bool)$data['status'] : false;

        if ($data['movie_access'] == 'free') {
            $data['plan_id'] = null;
        }

        $entertainment->update($data);
        
        // Log après la mise à jour
        $entertainment->refresh();
        \Log::info('Repository Update - Après mise à jour:', [
            'id' => $id,
            'is_iptv_final' => $entertainment->is_iptv,
            'status_final' => $entertainment->status
        ]);

        if (isset($data['genres'])) {
            $this->updateGenreMappings($entertainment->id, $data['genres']);
        }
        if (isset($data['countries'])) {
            $this->updateCountryMappings($entertainment->id, $data['countries']);
        }

        // Toujours supprimer les anciennes relations pour actors
        if (array_key_exists('actors', $data)) {
            if (!empty($data['actors'])) {
                $this->updateTalentMappings($entertainment->id, $data['actors'], 'actor');
            } else {
                \Log::info('Attempting to delete actors for entertainment_id: ' . $entertainment->id);
                $count = EntertainmentTalentMapping::where('entertainment_id', $entertainment->id)
                    ->whereHas('talentprofile', function ($query) {
                        $query->where('type', 'actor');
                    })
                    ->count();
                \Log::info('Found ' . $count . ' actors to delete');
                
                EntertainmentTalentMapping::where('entertainment_id', $entertainment->id)
                    ->whereHas('talentprofile', function ($query) {
                        $query->where('type', 'actor');
                    })
                    ->forceDelete();
            }
        }

        // Toujours supprimer les anciennes relations pour directors
        if (array_key_exists('directors', $data)) {
            if (!empty($data['directors'])) {
                $this->updateTalentMappings($entertainment->id, $data['directors'], 'director');
            } else {
                // Supprimer tous les réalisateurs si le tableau est vide
                EntertainmentTalentMapping::where('entertainment_id', $entertainment->id)
                    ->whereHas('talentprofile', function ($query) {
                        $query->where('type', 'director');
                    })
                    ->forceDelete();
            }
        }

        if (isset($data['enable_quality']) && $data['enable_quality'] == 1) {
            $this->updateQualityMappings($entertainment->id, $data);
        }

        $this->saveOrUpdateSourceMappings($entertainment, $data);

        return $entertainment;
    }

    public function delete($id)
    {
        $entertainment = Entertainment::findOrFail($id);
        $entertainment->delete();
        return $entertainment;
    }

    public function restore($id)
    {
        $entertainment = Entertainment::withTrashed()->findOrFail($id);
        $entertainment->restore();
        return $entertainment;
    }

    public function forceDelete($id)
    {
        $entertainment = Entertainment::withTrashed()->findOrFail($id);
        $entertainment->forceDelete();
        return $entertainment;
    }

    public function query()
    {

        $entertainemnt=Entertainment::query()->with('entertainmentGenerMappings')->withTrashed();

        if(Auth::user()->hasRole('user') ) {
            $entertainemnt->whereNull('deleted_at');
        }

        return $entertainemnt;

    }

    public function list($filters)
    {

        $query = Entertainment::with([
            'entertainmentGenerMappings',
            'plan',
            'entertainmentReviews',
            'entertainmentTalentMappings',
            'entertainmentStreamContentMappings',
            'entertainmentDownloadMappings'
        ])->where('status', 1);


        return $query;
    }


    public function saveGenreMappings(array $genres, $entertainmentId)
    {
        foreach ($genres as $genre) {
            $genre_data = [
                'entertainment_id' => $entertainmentId,
                'genre_id' => $genre
            ];

            EntertainmentGenerMapping::create($genre_data);
        }

    }
    public function saveCountryMappings(array $countries, $entertainmentId)
    {
        foreach ($countries as $country) {
            $country_data = [
                'entertainment_id' => $entertainmentId,
                'country_id' => $country
            ];

            EntertainmentCountryMapping::create($country_data);
        }

    }

    public function saveTalentMappings(array $talents, $entertainmentId)
    {
        foreach ($talents as $talent) {
            $talent_data = [
                'entertainment_id' => $entertainmentId,
                'talent_id' => $talent
            ];

            EntertainmentTalentMapping::create($talent_data);
        }
    }

    public function saveQualityMappings($entertainmentId, array $videoQuality, array $qualityVideoUrl, array $videoQualityType, array $qualityVideoFile)
    {

        foreach ($videoQuality as $index => $quality) {
            if ($quality != '' && ($qualityVideoUrl[$index] != '' || $qualityVideoFile[$index] != '') && $videoQualityType[$index] != '' ) {
                EntertainmentStreamContentMapping::create([
                    'entertainment_id' => $entertainmentId,
                    'url' => $qualityVideoUrl[$index] ?? extractFileNameFromUrl($qualityVideoFile[$index]),
                    'type' => $videoQualityType[$index],
                    'quality' => $quality,
                ]);
            }
        }
    }

    protected function updateGenreMappings($entertainmentId, $genres)
    {
        EntertainmentGenerMapping::where('entertainment_id', $entertainmentId)->forceDelete();
        $this->saveGenreMappings($genres, $entertainmentId);
    }
    protected function updateCountryMappings($entertainmentId, $countries)
    {
        EntertainmentCountryMapping::where('entertainment_id', $entertainmentId)->forceDelete();
        $this->saveCountryMappings($countries, $entertainmentId);
    }

    protected function updateTalentMappings($entertainmentId, $talents, $type)
    {
        EntertainmentTalentMapping::where('entertainment_id', $entertainmentId)
        ->whereHas('talentprofile', function ($query) use ($type) {
            $query->where('type', $type);
        })
        ->forceDelete();

        $this->saveTalentMappings($talents, $entertainmentId);
    }

    protected function  updateQualityMappings($entertainmentId, $requestData)
    {
        $qualityVideoUrlInput = $requestData['quality_video_url_input'] ?? [];
        $qualityVideo = $requestData['quality_video'] ?? [];

    $Quality_video_url = array_map(function($urlInput, $index) use ($qualityVideo) {
        return $urlInput !== null ? $urlInput : ($qualityVideo[$index] ?? null);
    }, $qualityVideoUrlInput, array_keys($qualityVideoUrlInput));
        $videoQuality = $requestData['video_quality'] ?? [];
        $videoQualityType = $requestData['video_quality_type'] ?? [];


        if (!empty($videoQuality) && !empty($Quality_video_url) && !empty($videoQualityType)) {
            EntertainmentStreamContentMapping::where('entertainment_id', $entertainmentId)->forceDelete();
            foreach ($videoQuality as $index => $videoquality) {

                if ($videoquality != '' && $Quality_video_url[$index] != '' && $videoQualityType[$index]) {
                    $url = isset($Quality_video_url[$index])
                    ? ($videoQualityType[$index] == 'Local'
                        ? extractFileNameFromUrl($Quality_video_url[$index])
                        : $Quality_video_url[$index])
                    : null;
                    $type = $videoQualityType[$index] ?? null;
                    $quality = $videoquality;

                    EntertainmentStreamContentMapping::create([
                        'entertainment_id' => $entertainmentId,
                        'url' => $url,
                        'type' => $type,
                        'quality' => $quality
                    ]);
                }
            }
        }
    }

    /**
     * Met à jour les mappings de sources pour un divertissement
     * 
     * @param int $entertainmentId
     * @param array $requestData
     * @return void
     */
    protected function saveOrUpdateSourceMappings(Entertainment $entertainment, $requestData)
    {
        // Supprime d'abord les anciennes sources
        $entertainment->sources()->delete();

        // Log pour déboguer les données reçues
        \Log::info('Données de sources reçues:', [
            'entertainment_id' => $entertainment->id,
            'titles' => $requestData['title'] ?? [],
            'sourceTypes' => $requestData['source_type'] ?? [],
            'urlTypes' => $requestData['source_url_type'] ?? [],
            'urls' => $requestData['source_url'] ?? [],
        ]);

        // Récupère les données des sources depuis la requête
        $titles = $requestData['title'] ?? [];
        $sourceTypes = $requestData['source_type'] ?? [];
        $urlTypes = $requestData['source_url_type'] ?? [];
        $urls = $requestData['source_url'] ?? [];

        // Crée les nouvelles sources
        foreach ($titles as $index => $title) {
            if (!empty($title) && !empty($sourceTypes[$index]) && !empty($urlTypes[$index]) && !empty($urls[$index])) {
                try {
                    $source = $entertainment->sources()->create([
                        'title' => $title,
                        'source_type' => $sourceTypes[$index],
                        'url_type' => $urlTypes[$index],
                        'url' => $urls[$index],
                        'status' => 'active'
                    ]);
                    \Log::info('Source créée avec succès:', [
                        'source_id' => $source->id,
                        'title' => $title,
                        'url' => $urls[$index]
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Erreur lors de la création de la source:', [
                        'error' => $e->getMessage(),
                        'title' => $title,
                        'url' => $urls[$index]
                    ]);
                }
            } else {
                \Log::warning('Données de source incomplètes:', [
                    'index' => $index,
                    'title' => $title ?? 'vide',
                    'source_type' => $sourceTypes[$index] ?? 'vide',
                    'url_type' => $urlTypes[$index] ?? 'vide',
                    'url' => $urls[$index] ?? 'vide'
                ]);
            }
        }
        
        // Log le nombre de sources créées
        \Log::info('Nombre de sources créées: ' . $entertainment->sources()->count());
    }

    public function storeDownloads(array $data, $id)
    {
        $entertainment = Entertainment::findOrFail($id);
        $entertainment->update($data);

        EntertainmnetDownloadMapping::where('entertainment_id', $id)->forceDelete();

        if (isset($data['enable_download_quality']) && $data['enable_download_quality'] == 1) {
            $quality_video_download_type = $data['quality_video_download_type'];
            $video_download_quality = $data['video_download_quality'];
            $download_quality_video_url = $data['download_quality_video_url'];

            if (!empty($quality_video_download_type) && !empty($video_download_quality) && !empty($download_quality_video_url)) {
                foreach ($quality_video_download_type as $index => $qualityType) {
                    if ($qualityType != '' && $video_download_quality[$index] != '' && $download_quality_video_url[$index] != '') {
                        EntertainmnetDownloadMapping::create([
                            'entertainment_id' => $entertainment->id,
                            'url' => $download_quality_video_url[$index],
                            'type' => $qualityType,
                            'quality' => $video_download_quality[$index]
                        ]);
                    }
                }
            }
        }
    }


}
