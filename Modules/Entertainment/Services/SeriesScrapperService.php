<?php

namespace Modules\Entertainment\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use DOMDocument;
use DOMXPath;

class SeriesScrapperService
{
    /**
     * Extrait les données de série à partir d'un contenu HTML ou d'une URL
     */
    public function extractSeriesData($input, $siteType = 'auto', $languages = ['VF'])
    {
        try {
            $html = $this->getHtmlContent($input);
            
            if (empty($html)) {
                return [
                    'success' => false,
                    'message' => 'Impossible de récupérer le contenu HTML',
                    'data' => []
                ];
            }

            // Détection automatique du type de site si non spécifié
            if ($siteType === 'auto') {
                $siteType = $this->detectSiteType($html, $input);
            }

            $seriesData = $this->parseSeriesData($html, $siteType, $languages);
            
            return [
                'success' => true,
                'message' => $this->generateSummaryMessage($seriesData),
                'site_type' => $siteType,
                'data' => $seriesData
            ];
            
        } catch (\Exception $e) {
            Log::error('Erreur dans SeriesScrapperService: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Erreur lors de l\'extraction: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Determines if the input is a URL or HTML and retrieves the content
     */
    private function getHtmlContent($input)
    {
        $input = trim($input);
        
        // Check if it's a URL
        if (filter_var($input, FILTER_VALIDATE_URL)) {
            return $this->fetchHtmlFromUrl($input);
        }
        
        // Otherwise, treat as direct HTML
        return $input;
    }

    /**
     * Retrieves HTML from a URL
     */
    private function fetchHtmlFromUrl($url)
    {
        try {
            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ])->timeout(30)->get($url);
            
            if ($response->successful()) {
                return $response->body();
            }
            
            Log::warning('Unable to retrieve content from URL: ' . $url . ' - Status: ' . $response->status());
            return null;
            
        } catch (\Exception $e) {
            Log::error('Error retrieving URL ' . $url . ': ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Automatically detects the site type
     */
    private function detectSiteType($html, $input = '')
    {
        // Check for specific patterns for each site
        if (strpos($html, 'data-episode') !== false && strpos($html, 'watch-button') !== false) {
            return 'french-stream';
        }
        
        if (preg_match('/class="ep\d+vf"/', $html)) {
            return 'wiflix';
        }
        
        // Check by URL if available
        if (filter_var($input, FILTER_VALIDATE_URL)) {
            $domain = parse_url($input, PHP_URL_HOST);
            if (strpos($domain, 'french-stream') !== false) {
                return 'french-stream';
            }
            if (strpos($domain, 'wiflix') !== false) {
                return 'wiflix';
            }
        }
        
        return 'unknown';
    }

    /**
     * Parses data according to the site type
     */
    private function parseSeriesData($html, $siteType, $languages = ['VF'])
    {
        switch ($siteType) {
            case 'wiflix':
                return $this->parseWiflixData($html, $languages);
            case 'french-stream':
                return $this->parseFrenchStreamData($html, $languages);
            default:
                return $this->parseGenericData($html);
        }
    }

    /**
     * Parses data for WIFLIX
     * Structure : <div class="ep1vf" style="display: none">
     */
    private function parseWiflixData($html, $languages = ['VF'])
    {
        $episodes = [];
        
        // Pattern to capture episode divs
        preg_match_all('/<div\s+class="ep(\d+)(vf|vs|vo)"[^>]*>(.*?)<\/div>/s', $html, $episodeMatches, PREG_SET_ORDER);
        
        foreach ($episodeMatches as $episodeMatch) {
            $episodeNumber = (int)$episodeMatch[1];
            $language = $episodeMatch[2]; // vf, vs, or vo
            $episodeContent = $episodeMatch[3];
            
            // Filtrer selon les langues sélectionnées
            $languageUpper = strtoupper($language);
            if (!in_array($languageUpper, $languages)) {
                continue; // Ignorer cette langue si elle n'est pas sélectionnée
            }
            
            // Extract sources for this episode
            $sources = $this->extractWiflixSources($episodeContent);
            
            if (!empty($sources)) {
                $episodes[] = [
                    'episode_number' => $episodeNumber,
                    'language' => strtoupper($language),
                    'sources' => $sources
                ];
            }
        }
        
        // Organize by episode and language
        return $this->organizeEpisodesByNumber($episodes);
    }

    /**
     * Extrait les sources pour WIFLIX à partir du contenu d'un épisode
     */
    private function extractWiflixSources($episodeContent)
    {
        $sources = [];
        
        // Pattern pour les onclick="loadVideo('URL')" avec gestion de la structure complexe
        preg_match_all('/onclick="[^"]*loadVideo\([\'"]([^\'"]+)[\'"]\)[^"]*"[^>]*>.*?<span[^>]*class="clichost"[^>]*>([^<]+)<\/span>/s', $episodeContent, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $url = html_entity_decode($match[1]);
            $readerName = trim($match[2]);
            
            if ($this->isValidStreamingUrl($url)) {
                $sourceInfo = $this->identifySource($url);
                $sources[] = [
                    'url' => $url,
                    'reader_name' => $readerName,
                    'source' => $sourceInfo['name'],
                    'type' => $sourceInfo['type']
                ];
            }
        }
        
        return $sources;
    }

    /**
     * Parses data for FRENCH-STREAM
     * Structure : <button class="watch-button" data-episode="1-vf" data-url="https://...">
     */
    private function parseFrenchStreamData($html, $languages = ['VF'])
    {
        $episodes = [];
        
        // Utiliser regex pour extraire les boutons watch-button avec data-episode et data-url
        // Pattern pour capturer: class="watch-button" data-episode="X-vf" data-url="URL"
        preg_match_all(
            '/<button[^>]*class="watch-button"[^>]*data-episode="([^"]*)"[^>]*data-url="([^"]*)"[^>]*>/',
            $html,
            $matches,
            PREG_SET_ORDER
        );
        
        foreach ($matches as $match) {
            $dataEpisode = $match[1];
            $dataUrl = $match[2];
            
            // Parse le format "1-vf", "1-vostfr"
            if (preg_match('/^(\d+)-(vf|vostfr)$/', $dataEpisode, $episodeMatches)) {
                $episodeNumber = (int)$episodeMatches[1];
                $language = $episodeMatches[2];
                
                // Filtrer selon les langues sélectionnées
                $languageUpper = strtoupper($language);
                if (!in_array($languageUpper, $languages)) {
                    continue; // Ignorer cette source si la langue n'est pas sélectionnée
                }
                
                // Accepter seulement uqload et vidzy
                if (!empty($dataUrl) && $this->isPreferredSource($dataUrl)) {
                    $sourceInfo = $this->identifySource($dataUrl);
                    
                    $episodes[] = [
                        'episode_number' => $episodeNumber,
                        'language' => strtoupper($language),
                        'sources' => [[
                            'url' => $dataUrl,
                            'reader_name' => $sourceInfo['name'],
                            'source' => $sourceInfo['name'],
                            'type' => $sourceInfo['type']
                        ]]
                    ];
                }
            }
        }
        
                return $this->organizeEpisodesByNumber($episodes);
    }

    /**
     * Vérifie si la source est préférée (uqload ou vidzy)
     */
    private function isPreferredSource($url)
    {
        $preferredDomains = ['uqload.net', 'vidzy.org'];
        
        foreach ($preferredDomains as $domain) {
            if (strpos($url, $domain) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Generic parse for unrecognized sites
     */
    private function parseGenericData($html)
    {
        // Use existing UrlScrapperService logic
        $urlScrapperService = new UrlScrapperService();
        $result = $urlScrapperService->extractStreamingUrls($html);
        
        if ($result['success']) {
            return [
                'episodes' => [[
                    'episode_number' => 1,
                    'language' => 'UNKNOWN',
                    'sources' => $result['urls']
                ]]
            ];
        }
        
        return [];
    }

    /**
     * Organizes episodes by number and combines languages
     */
    private function organizeEpisodesByNumber($episodes)
    {
        $organized = [];
        
        foreach ($episodes as $episode) {
            $epNum = $episode['episode_number'];
            $lang = $episode['language'];
            
            if (!isset($organized[$epNum])) {
                $organized[$epNum] = [
                    'episode_number' => $epNum,
                    'languages' => []
                ];
            }
            
            if (!isset($organized[$epNum]['languages'][$lang])) {
                $organized[$epNum]['languages'][$lang] = [];
            }
            
            $organized[$epNum]['languages'][$lang] = array_merge(
                $organized[$epNum]['languages'][$lang],
                $episode['sources']
            );
        }
        
        // Convert to indexed array and sort by episode number
        $result = array_values($organized);
        usort($result, function($a, $b) {
            return $a['episode_number'] - $b['episode_number'];
        });
        
        return $result;
    }

    /**
     * Checks if a URL is valid for streaming
     */
    private function isValidStreamingUrl($url)
    {
        if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // Approche plus flexible : accepter toute URL HTTPS avec des patterns courants de streaming
        $streamingPatterns = [
            '/embed/',
            '/player/',
            '/watch/',
            '/stream/',
            '/video/'
        ];
        
        foreach ($streamingPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }
        
        // Si aucun pattern n'est trouvé, on accepte quand même l'URL (plus flexible)
        return true;
    }

    /**
     * Identifies the source and type from the URL
     */
    private function identifySource($url)
    {
        $sources = [
            'ups2up.fun' => ['name' => 'USTR', 'type' => 'MP4'],
            'vide0.net' => ['name' => 'DdStream', 'type' => 'MP4'],
            'luluvdoo.com' => ['name' => 'LuLuTV', 'type' => 'MP4'],
            'jilliandescribecompany.com' => ['name' => 'Voe', 'type' => 'MP4'],
            'tipfly.xyz' => ['name' => 'One', 'type' => 'MP4'],
            'uqload.net' => ['name' => 'uqload', 'type' => 'MP4'],
            'waaw1.tv' => ['name' => 'netu', 'type' => 'MP4'],
            'vidmoly.to' => ['name' => 'Vmoly', 'type' => 'MP4'],
            'movearnpre.com' => ['name' => 'Filelions', 'type' => 'MP4'],
            'streamtape.com' => ['name' => 'stape', 'type' => 'MP4'],
            'dhcplay.com' => ['name' => 'Swish', 'type' => 'MP4'],
            'vidzy.org' => ['name' => 'Vidzy', 'type' => 'MP4']
        ];
        
        foreach ($sources as $domain => $info) {
            if (strpos($url, $domain) !== false) {
                return $info;
            }
        }
        
        // Default source if not recognized
        return ['name' => 'Unknown', 'type' => 'MP4'];
    }

    /**
     * Génère un message de résumé
     */
    private function generateSummaryMessage($seriesData)
    {
        if (empty($seriesData)) {
            return 'Aucun épisode trouvé';
        }
        
        $episodeCount = count($seriesData);
        $totalSources = 0;
        $languages = [];
        
        foreach ($seriesData as $episode) {
            foreach ($episode['languages'] as $lang => $sources) {
                $totalSources += count($sources);
                if (!in_array($lang, $languages)) {
                    $languages[] = $lang;
                }
            }
        }
        
        return sprintf(
            '%d épisode(s) trouvé(s) avec %d source(s) au total. Langues: %s',
            $episodeCount,
            $totalSources,
            implode(', ', $languages)
        );
    }
}