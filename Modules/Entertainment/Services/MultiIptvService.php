<?php

namespace Modules\Entertainment\Services;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Modules\Season\Models\Season;
use Modules\Episode\Models\Episode;
use Modules\Entertainment\Models\Entertainment;
use Modules\Entertainment\Models\Source;

class MultiIptvService
{
    /**
     * Configuration des connexions IPTV
     */
    private $iptvConnections = ['iptv_db', 'iptv_db2'];

    /**
     * Vérifie si l'intégration multi-IPTV est activée
     */
    public function isEnabled()
    {
        return config('entertainment.iptv_integration', false) && 
               config('entertainment.multi_iptv_enabled', false);
    }

    /**
     * Récupère les sources IPTV fusionnées pour un film
     */
    public function getMovieSources($tmdbId)
    {
        try {
            if (!$this->isEnabled() || !$tmdbId || !config('entertainment.iptv_movies_enabled', true)) {
                return collect();
            }

            $cacheKey = "multi_iptv_movie_{$tmdbId}";
            
            return Cache::remember($cacheKey, 3600, function() use ($tmdbId) {
                Log::info('MultiIptv Service: Searching for movie with TMDB ID: ' . $tmdbId);
                
                $allSources = collect();
                
                foreach ($this->iptvConnections as $connection) {
                    if ($this->testConnection($connection)) {
                        $sources = $this->getMovieSourcesFromDatabase($connection, $tmdbId);
                        $allSources = $allSources->merge($sources);
                    }
                }
                
                // Déduplication des sources
                $uniqueSources = $this->deduplicateSources($allSources);
                
                Log::info('MultiIptv Service: Found total of ' . $uniqueSources->count() . ' unique sources');
                
                return $uniqueSources;
            });
            
        } catch (\Exception $e) {
            Log::error('MultiIptv Service Error: ' . $e->getMessage());
            return collect();
        }
    }

    /**
     * Récupère les données fusionnées d'une série TV IPTV + Base standard
     */
    public function getTvShowData($tmdbId)
    {
        try {
            if (!$this->isEnabled() || !$tmdbId || !config('entertainment.iptv_series_enabled', true)) {
                return null;
            }

            $cacheKey = "multi_iptv_series_{$tmdbId}";
            
            return Cache::remember($cacheKey, 3600, function() use ($tmdbId) {
                Log::info('MultiIptv Service: Processing TV show with TMDB ID: ' . $tmdbId);
                
                $seriesData1 = null;
                $seriesData2 = null;
                $standardData = null;
                
                // Récupérer les données de chaque base IPTV
                if ($this->testConnection('iptv_db')) {
                    $seriesData1 = $this->getSeriesFromDatabase('iptv_db', $tmdbId);
                    Log::debug('MultiIptv Service: DB1 series data: ' . ($seriesData1 ? count($seriesData1) . ' seasons' : 'null'));
                }
                
                if ($this->testConnection('iptv_db2')) {
                    $seriesData2 = $this->getSeriesFromDatabase('iptv_db2', $tmdbId);
                    Log::debug('MultiIptv Service: DB2 series data: ' . ($seriesData2 ? count($seriesData2) . ' seasons' : 'null'));
                }
                
                // Récupérer les données de la base standard
                $standardData = $this->getSeriesFromStandardDatabase($tmdbId);
                Log::debug('MultiIptv Service: Standard DB series data: ' . ($standardData ? count($standardData) . ' seasons' : 'null'));
                
                // Fusionner les données IPTV d'abord
                $iptvMerged = null;
                if ($seriesData1 && $seriesData2) {
                    Log::info('MultiIptv Service: Merging IPTV databases...');
                    $iptvMerged = $this->mergeSeriesData($seriesData1, $seriesData2);
                } elseif ($seriesData1) {
                    $iptvMerged = $seriesData1;
                } elseif ($seriesData2) {
                    $iptvMerged = $seriesData2;
                }
                
                // Maintenant fusionner avec la base standard si nécessaire
                $finalResult = $this->mergeWithStandardData($iptvMerged, $standardData, $tmdbId);
                
                if (!$finalResult) {
                    Log::info('MultiIptv Service: No series data found in any database');
                    return null;
                }
                
                Log::info('MultiIptv Service: Final result has ' . count($finalResult) . ' seasons');
                return $finalResult;
            });
            
        } catch (\Exception $e) {
            Log::error('MultiIptv Service Error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Récupère les sources d'un film depuis une base de données spécifique
     */
    private function getMovieSourcesFromDatabase($connectionName, $tmdbId)
    {
        try {
            $iptvMovie = DB::connection($connectionName)
                        ->table('poster_iptv')
                        ->where('tmdb_id', $tmdbId)
                        ->where('type', 'movie')
                        ->first();
            
            if (!$iptvMovie) {
                return collect();
            }
            
            $sources = DB::connection($connectionName)
                      ->table('source_iptv')
                      ->where('poster_iptv_id', $iptvMovie->id)
                      ->get();
            
            return $sources->map(function ($source) use ($connectionName) {
                return [
                    'id' => (int) $source->id, // ID original en int
                    'url' => $source->url,
                    'quality' => $source->quality ?: 'default',
                    'language' => $source->language ?: 'default',
                    'source_type' => 'both',
                    'url_type' => 'MP4',
                    'status' => 'active',
                    'is_iptv' => true,
                    'iptv_source' => $connectionName,
                    'iptv_source_id' => $connectionName . '_' . $source->id // Info complète dans un champ séparé
                ];
            });
            
        } catch (\Exception $e) {
            Log::error("MultiIptv Service: Error fetching from {$connectionName}: " . $e->getMessage());
            return collect();
        }
    }

    /**
     * Récupère les données d'une série depuis la base de données standard
     */
    private function getSeriesFromStandardDatabase($tmdbId)
    {
        try {
            // Optimisation : Cache du nombre de saisons standard
            $cacheKey = "standard_series_count_{$tmdbId}";
            $standardSeasonCount = Cache::remember($cacheKey, 1800, function() use ($tmdbId) {
                return Entertainment::where('tmdb_id', $tmdbId)
                    ->where('type', 'tvshow')
                    ->with(['season' => function($query) {
                        $query->select('id', 'entertainment_id', 'season_index', 'name', 'short_desc', 'description', 'poster_url')
                              ->orderBy('season_index', 'asc');
                    }])
                    ->first()?->season?->count() ?? 0;
            });
            
            if ($standardSeasonCount === 0) {
                return null;
            }
            
            // Récupérer les données complètes seulement si nécessaire
            $entertainment = Entertainment::where('tmdb_id', $tmdbId)
                ->where('type', 'tvshow')
                ->with([
                    'season.episodes.sources' => function($query) {
                        $query->where('status', 'active');
                    }
                ])
                ->first();
                
            if (!$entertainment || !$entertainment->season) {
                return null;
            }
            
            return $this->transformStandardDataToIptvFormat($entertainment->season);
            
        } catch (\Exception $e) {
            Log::error("MultiIptv Service: Error fetching standard database: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Transforme les données standard au format IPTV
     */
    private function transformStandardDataToIptvFormat($seasons)
    {
        $result = [];
        $seasonCounter = 1; // Compteur pour les saisons sans season_index
        
        foreach ($seasons as $season) {
            // Utiliser season_index s'il existe, sinon utiliser le compteur
            $seasonNumber = $season->season_index ?: $seasonCounter;
            
            $seasonData = [
                'season_id' => (int) $season->id, // Garder l'ID original en int
                'season_number' => $seasonNumber,
                'name' => $season->name ?: ('Saison ' . $seasonNumber),
                'short_desc' => $season->short_desc ?: $season->name,
                'description' => $season->description ?: $season->short_desc,
                'poster_image' => $season->poster_url ?: '',
                'is_iptv' => false,
                'iptv_source' => 'standard',
                'iptv_source_id' => 'standard_' . $season->id, // Info complète dans un champ séparé
                'total_episodes' => $season->episodes->count(),
                'episodes' => []
            ];
            
            foreach ($season->episodes as $episode) {
                $sources = [];
                foreach ($episode->sources as $source) {
                    $sources[] = [
                        'id' => (int) $source->id, // ID original en int
                        'url' => $source->url,
                        'quality' => 'default',
                        'language' => 'default',
                        'source_type' => $source->source_type,
                        'url_type' => $source->url_type,
                        'status' => $source->status,
                        'is_iptv' => false,
                        'iptv_source' => 'standard',
                        'iptv_source_id' => 'standard_' . $source->id
                    ];
                }
                
                $seasonData['episodes'][] = [
                    'id' => (int) $episode->id, // ID original en int
                    'episode_number' => $episode->episode_number,
                    'name' => $episode->name ?: ('Episode ' . $episode->episode_number),
                    'description' => $episode->description ?: $episode->name,
                    'is_iptv' => false,
                    'iptv_source' => 'standard',
                    'iptv_source_id' => 'standard_' . $episode->id,
                    'sources' => $sources
                ];
            }
            
            $result[] = $seasonData;
            $seasonCounter++; // Incrémenter pour la prochaine saison
        }
        
        return $result;
    }

    /**
     * Fusionne les données IPTV avec les données standard
     * PRIORITÉ: IPTV > Standard (les données IPTV sont toujours préférées)
     */
    private function mergeWithStandardData($iptvData, $standardData, $tmdbId)
    {
        // Si pas de données IPTV, retourner seulement les données standard
        if (!$iptvData && $standardData) {
            Log::info('MultiIptv Service: No IPTV data, returning standard data only');
            return $standardData;
        }
        
        // Si pas de données standard, retourner seulement les données IPTV
        if ($iptvData && !$standardData) {
            Log::info('MultiIptv Service: No standard data, returning IPTV data only');
            return $iptvData;
        }
        
        // Si aucune donnée
        if (!$iptvData && !$standardData) {
            return null;
        }
        
        // Fusion intelligente : IPTV PRIORITAIRE + Standard pour combler les trous
        Log::info('MultiIptv Service: Merging IPTV (' . count($iptvData) . ' seasons) with Standard (' . count($standardData) . ' seasons)');
        
        // Commencer avec les données IPTV (priorité absolue)
        $mergedResult = $iptvData;
        
        // Identifier les saisons IPTV existantes
        $iptvSeasonNumbers = [];
        foreach ($iptvData as $iptvSeason) {
            $iptvSeasonNumbers[] = $iptvSeason['season_number'];
        }
        
        Log::debug('MultiIptv Service: IPTV seasons: ' . implode(', ', $iptvSeasonNumbers));
        
        // Ajouter SEULEMENT les saisons standard qui n'existent PAS dans IPTV
        foreach ($standardData as $standardSeason) {
            $seasonNumber = $standardSeason['season_number'];
            
            // PRIORITÉ ABSOLUE AUX DONNÉES IPTV
            if (!in_array($seasonNumber, $iptvSeasonNumbers)) {
                Log::debug('MultiIptv Service: Adding standard season ' . $seasonNumber . ' (missing from IPTV)');
                $mergedResult[] = $standardSeason;
            } else {
                Log::debug('MultiIptv Service: Skipping standard season ' . $seasonNumber . ' (IPTV version takes priority)');
            }
        }
        
        // Trier par numéro de saison
        usort($mergedResult, function($a, $b) {
            return $a['season_number'] <=> $b['season_number'];
        });
        
        // Log détaillé du résultat final
        $finalSeasons = array_column($mergedResult, 'season_number');
        $finalSources = [];
        foreach ($mergedResult as $season) {
            $finalSources[] = 'S' . $season['season_number'] . ':' . $season['iptv_source'];
        }
        
        Log::info('MultiIptv Service: Final merged result: ' . count($mergedResult) . ' seasons [' . implode(', ', $finalSeasons) . ']');
        Log::info('MultiIptv Service: Season sources: [' . implode(', ', $finalSources) . ']');
        
        return $mergedResult;
    }

    /**
     * Trouve le numéro de saison maximum dans un dataset
     */
    private function getMaxSeasonNumber($seriesData)
    {
        if (!$seriesData || !is_array($seriesData)) {
            return 0;
        }
        
        $maxSeason = 0;
        foreach ($seriesData as $season) {
            if (isset($season['season_number']) && $season['season_number'] > $maxSeason) {
                $maxSeason = $season['season_number'];
            }
        }
        
        return $maxSeason;
    }

    /**
     * Récupère une série depuis une base de données spécifique avec requêtes optimisées
     */
    private function getSeriesFromDatabase($connectionName, $tmdbId)
    {
        try {
            // 1. Récupérer le poster
            $poster = DB::connection($connectionName)
                ->table('poster_iptv')
                ->where('tmdb_id', $tmdbId)
                ->where('type', 'tvshow')
                ->first();
                
            if (!$poster) {
                return null;
            }
            
            // 2. Récupérer toutes les saisons
            $seasons = DB::connection($connectionName)
                ->table('season_iptv')
                ->where('poster_iptv_id', $poster->id)
                ->orderBy('season_number', 'asc')
                ->get();
                
            if ($seasons->isEmpty()) {
                return null;
            }
            
            $seasonIds = $seasons->pluck('id')->toArray();
            
            // 3. Récupérer tous les épisodes en une requête
            $episodes = DB::connection($connectionName)
                ->table('episode_iptv')
                ->whereIn('season_iptv_id', $seasonIds)
                ->orderBy('season_iptv_id', 'asc')
                ->orderBy('episode_number', 'asc')
                ->get()
                ->groupBy('season_iptv_id');
                
            $episodeIds = $episodes->flatten()->pluck('id')->toArray();
            
            // 4. Récupérer toutes les sources en une requête
            $sources = collect();
            if (!empty($episodeIds)) {
                $sources = DB::connection($connectionName)
                    ->table('source_episode_iptv')
                    ->whereIn('episode_iptv_id', $episodeIds)
                    ->get()
                    ->groupBy('episode_iptv_id');
            }
            
            // 5. Assembler les données
            return $this->assembleSeriesData($poster, $seasons, $episodes, $sources, $connectionName);
            
        } catch (\Exception $e) {
            Log::error("MultiIptv Service: Error in getSeriesFromDatabase for {$connectionName}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Assemble les données de série dans le format attendu
     */
    private function assembleSeriesData($poster, $seasons, $episodesGrouped, $sourcesGrouped, $connectionName)
    {
        $result = [];
        
        foreach ($seasons as $season) {
            $seasonEpisodes = $episodesGrouped->get($season->id, collect());
            
            $seasonData = [
                'season_id' => (int) $season->id, // ID original en int
                'season_number' => $season->season_number,
                'name' => 'Saison ' . $season->season_number,
                'short_desc' => $season->title ?: ('Saison ' . $season->season_number),
                'description' => $season->title ?: ('Saison ' . $season->season_number . ' de ' . $poster->title),
                'poster_image' => $poster->poster_image ?: '',
                'is_iptv' => true,
                'iptv_source' => $connectionName,
                'iptv_source_id' => $connectionName . '_' . $season->id, // Info complète dans un champ séparé
                'total_episodes' => $seasonEpisodes->count(),
                'episodes' => []
            ];
            
            foreach ($seasonEpisodes as $episode) {
                $episodeSources = $sourcesGrouped->get($episode->id, collect());
                
                $sources = [];
                foreach ($episodeSources as $source) {
                    $sources[] = [
                        'id' => (int) $source->id, // ID original en int
                        'url' => $source->url,
                        'quality' => $source->quality ?: 'default',
                        'language' => $source->language ?: 'default',
                        'source_type' => 'both',
                        'url_type' => 'MP4',
                        'status' => 'active',
                        'is_iptv' => true,
                        'iptv_source' => $connectionName,
                        'iptv_source_id' => $connectionName . '_' . $source->id
                    ];
                }
                
                $seasonData['episodes'][] = [
                    'id' => (int) $episode->id, // ID original en int
                    'episode_number' => $episode->episode_number,
                    'name' => 'Episode ' . $episode->episode_number,
                    'description' => $episode->description ?: $episode->title ?: ('Episode ' . $episode->episode_number),
                    'is_iptv' => true,
                    'iptv_source' => $connectionName,
                    'iptv_source_id' => $connectionName . '_' . $episode->id,
                    'sources' => $sources
                ];
            }
            
            $result[] = $seasonData;
        }
        
        return $result;
    }

    /**
     * Fusionne les données de deux séries
     */
    private function mergeSeriesData($series1, $series2)
    {
        Log::info('MultiIptv Service: mergeSeriesData - Input series1: ' . count($series1) . ' seasons');
        Log::info('MultiIptv Service: mergeSeriesData - Input series2: ' . count($series2) . ' seasons');
        
        $mergedSeries = $series1;
        
        foreach ($series2 as $season2) {
            $seasonFound = false;
            
            foreach ($mergedSeries as &$season1) {
                if ($season1['season_number'] == $season2['season_number']) {
                    Log::debug('MultiIptv Service: Merging season ' . $season2['season_number']);
                    // Fusionner les épisodes de cette saison
                    $season1['episodes'] = $this->mergeEpisodes(
                        $season1['episodes'], 
                        $season2['episodes']
                    );
                    $season1['total_episodes'] = count($season1['episodes']);
                    $seasonFound = true;
                    break;
                }
            }
            
            // Si la saison n'existe que dans la DB2
            if (!$seasonFound) {
                Log::debug('MultiIptv Service: Adding new season ' . $season2['season_number'] . ' from DB2');
                $mergedSeries[] = $season2;
            }
        }
        
        Log::info('MultiIptv Service: mergeSeriesData - Before sort: ' . count($mergedSeries) . ' seasons');
        
        // Trier les saisons par numéro
        usort($mergedSeries, function($a, $b) {
            return $a['season_number'] <=> $b['season_number'];
        });
        
        Log::info('MultiIptv Service: mergeSeriesData - Final result: ' . count($mergedSeries) . ' seasons');
        
        return $mergedSeries;
    }

    /**
     * Fusionne les épisodes de deux saisons
     */
    private function mergeEpisodes($episodes1, $episodes2)
    {
        foreach ($episodes2 as $episode2) {
            $episodeFound = false;
            
            foreach ($episodes1 as &$episode1) {
                if ($episode1['episode_number'] == $episode2['episode_number']) {
                    // Fusionner les sources de cet épisode
                    $episode1['sources'] = array_merge(
                        $episode1['sources'], 
                        $episode2['sources']
                    );
                    
                    // Déduplication des sources
                    $episode1['sources'] = $this->deduplicateSourcesArray($episode1['sources']);
                    $episodeFound = true;
                    break;
                }
            }
            
            // Si l'épisode n'existe que dans la DB2
            if (!$episodeFound) {
                $episodes1[] = $episode2;
            }
        }
        
        // Trier les épisodes par numéro
        usort($episodes1, function($a, $b) {
            return $a['episode_number'] <=> $b['episode_number'];
        });
        
        return $episodes1;
    }

    /**
     * Déduplique les sources (Collection) en gardant une source par base IPTV
     */
    private function deduplicateSources($sources)
    {
        $uniqueSources = collect();
        $seenUrlsBySource = [];
        
        foreach ($sources as $source) {
            $urlKey = md5($source['url']);
            $iptvSource = $source['iptv_source'] ?? 'unknown';
            
            // Créer une clé unique pour URL + source IPTV
            $combinedKey = $urlKey . '_' . $iptvSource;
            
            if (!isset($seenUrlsBySource[$combinedKey])) {
                $seenUrlsBySource[$combinedKey] = true;
                $uniqueSources->push($source);
            }
        }
        
        return $uniqueSources;
    }

    /**
     * Déduplique les sources (Array) en gardant une source par base IPTV
     */
    private function deduplicateSourcesArray($sources)
    {
        $uniqueSources = [];
        $seenUrlsBySource = []; // Garder trace par base IPTV
        
        foreach ($sources as $source) {
            $urlKey = md5($source['url']);
            $iptvSource = $source['iptv_source'] ?? 'unknown';
            
            // Créer une clé unique pour URL + source IPTV
            $combinedKey = $urlKey . '_' . $iptvSource;
            
            if (!isset($seenUrlsBySource[$combinedKey])) {
                $seenUrlsBySource[$combinedKey] = true;
                $uniqueSources[] = $source;
            } else {
                // Log pour debug : source dupliquée ignorée
                Log::debug('MultiIptv Service: Duplicate source URL found for ' . $iptvSource . ': ' . substr($source['url'], 0, 50));
            }
        }
        
        Log::debug('MultiIptv Service: Deduplicated sources: ' . count($sources) . ' -> ' . count($uniqueSources));
        return $uniqueSources;
    }

    /**
     * Test la connectivité à une base de données
     */
    private function testConnection($connectionName)
    {
        try {
            DB::connection($connectionName)->getPdo();
            return true;
        } catch (\Exception $e) {
            Log::warning("MultiIptv Service: Cannot connect to {$connectionName}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Vide le cache pour un contenu spécifique (Service + API)
     */
    public function clearCache($tmdbId, $type = 'both')
    {
        $clearedKeys = [];
        
        if ($type === 'movie' || $type === 'both') {
            Cache::forget("multi_iptv_movie_{$tmdbId}");
            $clearedKeys[] = "multi_iptv_movie_{$tmdbId}";
        }
        
        if ($type === 'series' || $type === 'both') {
            Cache::forget("multi_iptv_series_{$tmdbId}");
            Cache::forget("standard_series_count_{$tmdbId}");
            $clearedKeys[] = "multi_iptv_series_{$tmdbId}";
            $clearedKeys[] = "standard_series_count_{$tmdbId}";
            
            // NOUVEAU : Vider aussi le cache de l'API tvshow-details
            $this->clearApiTvshowCache($tmdbId);
            $clearedKeys[] = "API tvshow cache patterns";
        }
        
        Log::info("MultiIptv Service: Cache cleared for TMDB ID {$tmdbId} ({$type}): " . implode(', ', $clearedKeys));
    }

    /**
     * Vide le cache de l'API tvshow-details pour toutes les variations possibles
     */
    private function clearApiTvshowCache($tmdbId)
    {
        try {
            // Récupérer l'ID de la série depuis la base standard pour vider le cache API
            $entertainment = Entertainment::where('tmdb_id', $tmdbId)
                ->where('type', 'tvshow')
                ->first(['id']);
                
            if (!$entertainment) {
                Log::debug("MultiIptv Service: No standard entertainment found for TMDB {$tmdbId}, skipping API cache clear");
                return;
            }
            
            $tvshowId = $entertainment->id;
            $cleared = 0;
            
            // Vider le cache pour différents profils (on ne peut pas deviner le profile_id exact)
            // Pattern: tvshow_{tvshow_id}_{profile_id}_iptv
            $cachePatterns = [
                "tvshow_{$tvshowId}_*_iptv",
                "tvshow_{$tvshowId}_*",
                "tvshow_embed_seasons_{$tvshowId}"
            ];
            
            // Pour Laravel, on doit utiliser une approche différente car il n'y a pas de wildcard direct
            // On va essayer les profils les plus courants (1-10) + quelques variations
            $commonProfiles = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 0, null];
            
            foreach ($commonProfiles as $profileId) {
                $keys = [
                    "tvshow_{$tvshowId}_{$profileId}_iptv",
                    "tvshow_{$tvshowId}_{$profileId}",
                ];
                
                foreach ($keys as $key) {
                    if (Cache::forget($key)) {
                        $cleared++;
                        Log::debug("MultiIptv Service: Cleared API cache key: {$key}");
                    }
                }
            }
            
            // Cache embed seasons
            if (Cache::forget("tvshow_embed_seasons_{$tvshowId}")) {
                $cleared++;
                Log::debug("MultiIptv Service: Cleared embed seasons cache");
            }
            
            Log::info("MultiIptv Service: Cleared {$cleared} API cache keys for entertainment ID {$tvshowId} (TMDB: {$tmdbId})");
            
        } catch (\Exception $e) {
            Log::error("MultiIptv Service: Error clearing API cache: " . $e->getMessage());
        }
    }

    /**
     * Force la mise à jour des données d'une série en vidant le cache
     */
    public function refreshTvShowData($tmdbId)
    {
        Log::info("MultiIptv Service: Refreshing TV show data for TMDB ID {$tmdbId}");
        
        // Vider le cache
        $this->clearCache($tmdbId, 'series');
        
        // Récupérer les nouvelles données
        return $this->getTvShowData($tmdbId);
    }

    /**
     * Vide le cache API pour un entertainment ID spécifique
     */
    public function clearApiCacheByEntertainmentId($entertainmentId)
    {
        try {
            $cleared = 0;
            
            // Profils courants à tester
            $commonProfiles = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 0, null];
            
            foreach ($commonProfiles as $profileId) {
                $keys = [
                    "tvshow_{$entertainmentId}_{$profileId}_iptv",
                    "tvshow_{$entertainmentId}_{$profileId}",
                ];
                
                foreach ($keys as $key) {
                    if (Cache::forget($key)) {
                        $cleared++;
                        Log::debug("MultiIptv Service: Cleared API cache key: {$key}");
                    }
                }
            }
            
            // Cache embed seasons
            if (Cache::forget("tvshow_embed_seasons_{$entertainmentId}")) {
                $cleared++;
                Log::debug("MultiIptv Service: Cleared embed seasons cache");
            }
            
            Log::info("MultiIptv Service: Cleared {$cleared} API cache keys for entertainment ID {$entertainmentId}");
            return $cleared;
            
        } catch (\Exception $e) {
            Log::error("MultiIptv Service: Error clearing API cache by entertainment ID: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Récupère les données de série sans cache pour debug
     */
    public function getTvShowDataNoCache($tmdbId)
    {
        try {
            if (!$this->isEnabled() || !$tmdbId || !config('entertainment.iptv_series_enabled', true)) {
                return null;
            }

            Log::info('MultiIptv Service: Processing TV show with TMDB ID (NO CACHE): ' . $tmdbId);
            
            $seriesData1 = null;
            $seriesData2 = null;
            $standardData = null;
            
            // Récupérer les données de chaque base IPTV
            if ($this->testConnection('iptv_db')) {
                $seriesData1 = $this->getSeriesFromDatabase('iptv_db', $tmdbId);
                Log::debug('MultiIptv Service: DB1 series data: ' . ($seriesData1 ? count($seriesData1) . ' seasons' : 'null'));
            }
            
            if ($this->testConnection('iptv_db2')) {
                $seriesData2 = $this->getSeriesFromDatabase('iptv_db2', $tmdbId);
                Log::debug('MultiIptv Service: DB2 series data: ' . ($seriesData2 ? count($seriesData2) . ' seasons' : 'null'));
            }
            
            // Récupérer les données de la base standard
            $standardData = $this->getSeriesFromStandardDatabase($tmdbId);
            Log::debug('MultiIptv Service: Standard DB series data: ' . ($standardData ? count($standardData) . ' seasons' : 'null'));
            
            // Fusionner les données IPTV d'abord
            $iptvMerged = null;
            if ($seriesData1 && $seriesData2) {
                Log::info('MultiIptv Service: Merging IPTV databases...');
                $iptvMerged = $this->mergeSeriesData($seriesData1, $seriesData2);
            } elseif ($seriesData1) {
                $iptvMerged = $seriesData1;
            } elseif ($seriesData2) {
                $iptvMerged = $seriesData2;
            }
            
            // Maintenant fusionner avec la base standard si nécessaire
            $finalResult = $this->mergeWithStandardData($iptvMerged, $standardData, $tmdbId);
            
            if (!$finalResult) {
                Log::info('MultiIptv Service: No series data found in any database');
                return null;
            }
            
            Log::info('MultiIptv Service: Final result has ' . count($finalResult) . ' seasons');
            return $finalResult;
            
        } catch (\Exception $e) {
            Log::error('MultiIptv Service Error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return null;
        }
    }

    /**
     * Débugue les données brutes de chaque base pour une série
     */
    public function debugTvShowSources($tmdbId)
    {
        $debug = [
            'tmdb_id' => $tmdbId,
            'timestamp' => now()->toDateTimeString(),
            'sources' => []
        ];

        // Vérifier chaque base IPTV
        foreach ($this->iptvConnections as $connection) {
            if ($this->testConnection($connection)) {
                try {
                    $poster = DB::connection($connection)
                        ->table('poster_iptv')
                        ->where('tmdb_id', $tmdbId)
                        ->where('type', 'tvshow')
                        ->first();
                        
                    if ($poster) {
                        $seasons = DB::connection($connection)
                            ->table('season_iptv')
                            ->where('poster_iptv_id', $poster->id)
                            ->orderBy('season_number', 'asc')
                            ->get();
                            
                        $debug['sources'][$connection] = [
                            'connected' => true,
                            'poster_found' => true,
                            'seasons_count' => $seasons->count(),
                            'seasons' => $seasons->pluck('season_number')->toArray()
                        ];
                    } else {
                        $debug['sources'][$connection] = [
                            'connected' => true,
                            'poster_found' => false,
                            'seasons_count' => 0,
                            'seasons' => []
                        ];
                    }
                } catch (\Exception $e) {
                    $debug['sources'][$connection] = [
                        'connected' => false,
                        'error' => $e->getMessage()
                    ];
                }
            } else {
                $debug['sources'][$connection] = [
                    'connected' => false,
                    'error' => 'Connection failed'
                ];
            }
        }

        // Vérifier la base standard
        try {
            $entertainment = Entertainment::where('tmdb_id', $tmdbId)
                ->where('type', 'tvshow')
                ->with('season')
                ->first();
                
            if ($entertainment && $entertainment->season) {
                $debug['sources']['standard'] = [
                    'connected' => true,
                    'series_found' => true,
                    'seasons_count' => $entertainment->season->count(),
                    'seasons' => $entertainment->season->pluck('season_index')->filter()->toArray()
                ];
            } else {
                $debug['sources']['standard'] = [
                    'connected' => true,
                    'series_found' => false,
                    'seasons_count' => 0,
                    'seasons' => []
                ];
            }
        } catch (\Exception $e) {
            $debug['sources']['standard'] = [
                'connected' => false,
                'error' => $e->getMessage()
            ];
        }

        Log::info('MultiIptv Service: Debug data for TMDB ' . $tmdbId, $debug);
        return $debug;
    }

    /**
     * Statistiques des bases IPTV + Standard
     */
    public function getStats()
    {
        $stats = [];
        
        // Statistiques des bases IPTV
        foreach ($this->iptvConnections as $connection) {
            if ($this->testConnection($connection)) {
                try {
                    $movieCount = DB::connection($connection)
                        ->table('poster_iptv')
                        ->where('type', 'movie')
                        ->count();
                        
                    $seriesCount = DB::connection($connection)
                        ->table('poster_iptv')
                        ->where('type', 'tvshow')
                        ->count();
                    
                    $stats[$connection] = [
                        'connected' => true,
                        'movies' => $movieCount,
                        'series' => $seriesCount,
                        'total' => $movieCount + $seriesCount,
                        'type' => 'iptv'
                    ];
                } catch (\Exception $e) {
                    $stats[$connection] = [
                        'connected' => false,
                        'error' => $e->getMessage(),
                        'type' => 'iptv'
                    ];
                }
            } else {
                $stats[$connection] = [
                    'connected' => false,
                    'error' => 'Connection failed',
                    'type' => 'iptv'
                ];
            }
        }
        
        // Statistiques de la base standard
        try {
            $standardMovies = Entertainment::where('type', 'movie')->count();
            $standardSeries = Entertainment::where('type', 'tvshow')->count();
            
            $stats['standard'] = [
                'connected' => true,
                'movies' => $standardMovies,
                'series' => $standardSeries,
                'total' => $standardMovies + $standardSeries,
                'type' => 'standard'
            ];
        } catch (\Exception $e) {
            $stats['standard'] = [
                'connected' => false,
                'error' => $e->getMessage(),
                'type' => 'standard'
            ];
        }
        
        return $stats;
    }
} 