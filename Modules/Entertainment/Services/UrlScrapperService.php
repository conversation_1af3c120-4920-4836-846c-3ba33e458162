<?php

namespace Modules\Entertainment\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use DOMDocument;
use DOMXPath;

class UrlScrapperService
{
    /**
     * Extrait les URLs de streaming à partir d'un contenu HTML ou d'une URL
     */
    public function extractStreamingUrls($input)
    {
        try {
            $html = $this->getHtmlContent($input);
            
            if (empty($html)) {
                return [
                    'success' => false,
                    'message' => 'Impossible de récupérer le contenu HTML',
                    'urls' => []
                ];
            }

            $urls = $this->parseStreamingUrls($html);
            
            return [
                'success' => true,
                'message' => count($urls) . ' URLs trouvées',
                'urls' => $urls
            ];
            
        } catch (\Exception $e) {
            Log::error('Erreur dans UrlScrapperService: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Erreur lors de l\'extraction: ' . $e->getMessage(),
                'urls' => []
            ];
        }
    }

    /**
     * Détermine si l'input est une URL ou du HTML et récupère le contenu
     */
    private function getHtmlContent($input)
    {
        $input = trim($input);
        
        // Vérifier si c'est une URL
        if (filter_var($input, FILTER_VALIDATE_URL)) {
            return $this->fetchHtmlFromUrl($input);
        }
        
        // Sinon, traiter comme du HTML direct
        return $input;
    }

    /**
     * Récupère le HTML à partir d'une URL
     */
    private function fetchHtmlFromUrl($url)
    {
        try {
            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ])->timeout(30)->get($url);
            
            if ($response->successful()) {
                return $response->body();
            }
            
            Log::warning('Impossible de récupérer le contenu de l\'URL: ' . $url . ' - Status: ' . $response->status());
            return null;
            
        } catch (\Exception $e) {
            Log::error('Erreur lors de la récupération de l\'URL ' . $url . ': ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Parse le HTML pour extraire les URLs de streaming
     */
    private function parseStreamingUrls($html)
    {
        $urls = [];
        
        // Méthode 1: Recherche des attributs onclick avec loadVideo()
        $urls = array_merge($urls, $this->extractFromOnclickLoadVideo($html));
        
        // Méthode 2: Recherche des URLs dans les attributs href
        $urls = array_merge($urls, $this->extractFromHrefAttributes($html));
        
        // Méthode 3: Recherche des URLs dans le contenu JavaScript
        $urls = array_merge($urls, $this->extractFromJavaScript($html));
        
        // Nettoyer et dédupliquer les URLs
        $urls = $this->cleanAndDeduplicateUrls($urls);
        
        return $urls;
    }

    /**
     * Extrait les URLs des attributs onclick avec loadVideo()
     */
    private function extractFromOnclickLoadVideo($html)
    {
        $urls = [];
        
        // Pattern pour les onclick="loadVideo('URL')"
        preg_match_all('/onclick="[^"]*loadVideo\([\'"]([^\'"]+)[\'"]\)[^"]*"/', $html, $matches);
        
        if (!empty($matches[1])) {
            foreach ($matches[1] as $url) {
                $sourceInfo = $this->identifySource($url);
                $urls[] = [
                    'url' => $url,
                    'source' => $sourceInfo['name'],
                    'type' => $sourceInfo['type']
                ];
            }
        }
        
        return $urls;
    }

    /**
     * Extrait les URLs des attributs href
     */
    private function extractFromHrefAttributes($html)
    {
        $urls = [];
        
        // Utiliser DOMDocument pour parser le HTML plus proprement
        $dom = new DOMDocument();
        @$dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        
        $xpath = new DOMXPath($dom);
        $links = $xpath->query('//a[@href]');
        
        foreach ($links as $link) {
            $href = $link->getAttribute('href');
            if ($this->isStreamingUrl($href)) {
                $sourceInfo = $this->identifySource($href);
                $urls[] = [
                    'url' => $href,
                    'source' => $sourceInfo['name'],
                    'type' => $sourceInfo['type']
                ];
            }
        }
        
        return $urls;
    }

    /**
     * Extrait les URLs du contenu JavaScript
     */
    private function extractFromJavaScript($html)
    {
        $urls = [];
        
        // Pattern pour diverses façons d'écrire les URLs en JavaScript
        $patterns = [
            '/[\'"]https?:\/\/[^\'"]*(?:embed|e|v)\/[^\'"]+[\'"]/i',
            '/[\'"]https?:\/\/[^\'"]*\.(?:html|php)[^\'"]*[\'"]/i'
        ];
        
        foreach ($patterns as $pattern) {
            preg_match_all($pattern, $html, $matches);
            
            if (!empty($matches[0])) {
                foreach ($matches[0] as $match) {
                    $url = trim($match, '\'"');
                    if ($this->isStreamingUrl($url)) {
                        $sourceInfo = $this->identifySource($url);
                        $urls[] = [
                            'url' => $url,
                            'source' => $sourceInfo['name'],
                            'type' => $sourceInfo['type']
                        ];
                    }
                }
            }
        }
        
        return $urls;
    }

    /**
     * Vérifie si une URL est une URL de streaming
     */
    private function isStreamingUrl($url)
    {
        $streamingDomains = [
            'ups2up.fun',
            'vide0.net', 
            'luluvdoo.com',
            'jilliandescribecompany.com',
            'tipfly.xyz',
            'uqload.net',
            'waaw1.tv',
            'vidmoly.to',
            'movearnpre.com',
            'streamtape.com',
            'dhcplay.com',
            'fembed.com',
            'embedstream.me',
            'streamsb.net',
            'doodstream.com',
            'upstream.to'
        ];
        
        foreach ($streamingDomains as $domain) {
            if (strpos($url, $domain) !== false) {
                return true;
            }
        }
        
        // Vérifier les patterns d'URLs de streaming
        $patterns = [
            '/\/embed\-/',
            '/\/e\//',
            '/\/v\//',
            '/\/em\-/',
            '/embed\./',
            '/player\./'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Identifie la source et le type à partir de l'URL
     */
    private function identifySource($url)
    {
        $sources = [
            'ups2up.fun' => ['name' => 'USTR', 'type' => 'MP4'],
            'vide0.net' => ['name' => 'DdStream', 'type' => 'MP4'],
            'luluvdoo.com' => ['name' => 'LuLuTV', 'type' => 'MP4'],
            'jilliandescribecompany.com' => ['name' => 'Voe', 'type' => 'MP4'],
            'tipfly.xyz' => ['name' => 'One', 'type' => 'MP4'],
            'uqload.net' => ['name' => 'uqload', 'type' => 'MP4'],
            'waaw1.tv' => ['name' => 'netu', 'type' => 'MP4'],
            'vidmoly.to' => ['name' => 'Vmoly', 'type' => 'MP4'],
            'movearnpre.com' => ['name' => 'Filelions', 'type' => 'MP4'],
            'streamtape.com' => ['name' => 'stape', 'type' => 'MP4'],
            'dhcplay.com' => ['name' => 'Swish', 'type' => 'MP4']
        ];
        
        foreach ($sources as $domain => $info) {
            if (strpos($url, $domain) !== false) {
                return $info;
            }
        }
        
        // Source par défaut si non reconnue
        return ['name' => 'Unknown', 'type' => 'MP4'];
    }

    /**
     * Nettoie et déduplique les URLs
     */
    private function cleanAndDeduplicateUrls($urls)
    {
        $cleaned = [];
        $seen = [];
        
        foreach ($urls as $urlData) {
            $url = $urlData['url'];
            
            // Nettoyer l'URL
            $url = html_entity_decode($url);
            $url = trim($url);
            
            // Éviter les doublons
            if (!in_array($url, $seen) && !empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
                $seen[] = $url;
                $cleaned[] = [
                    'url' => $url,
                    'source' => $urlData['source'],
                    'type' => $urlData['type']
                ];
            }
        }
        
        return $cleaned;
    }
} 