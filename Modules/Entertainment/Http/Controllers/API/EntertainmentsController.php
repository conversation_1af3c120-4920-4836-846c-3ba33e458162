<?php

namespace Modules\Entertainment\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Entertainment\Models\Entertainment;
use Modules\Entertainment\Transformers\MoviesResource;
use Modules\Entertainment\Transformers\MovieDetailDataResource;
use Modules\Entertainment\Transformers\TvshowResource;
use Modules\Entertainment\Transformers\TvshowDetailResource;
use Modules\Entertainment\Models\Watchlist;
use Modules\Entertainment\Models\Like;
use Modules\Entertainment\Models\EntertainmentDownload;
use Modules\Episode\Models\Episode;
use Modules\Entertainment\Transformers\EpisodeResource;
use Modules\Entertainment\Transformers\EpisodeDetailResource;
use Modules\Entertainment\Transformers\SearchResource;
use Modules\Entertainment\Transformers\ComingSoonResource;
use Carbon\Carbon;
use Modules\Entertainment\Models\UserReminder;
use Modules\Entertainment\Models\EntertainmentView;
use Modules\Entertainment\Models\ContinueWatch;
use Illuminate\Support\Facades\Cache;
use Modules\Genres\Models\Genres;
use Modules\Video\Models\Video;
use Modules\Video\Transformers\VideoResource;
use Illuminate\Support\Facades\Auth;
use App\Models\UserSearchHistory;
use Modules\Season\Models\Season;
use Modules\Entertainment\Transformers\SeasonResource;
use Modules\CastCrew\Models\CastCrew;
use Modules\CastCrew\Transformers\CastCrewListResource;
use Modules\Entertainment\Services\IptvIntegrationService;
use Modules\Entertainment\Services\MultiIptvService;


class EntertainmentsController extends Controller
{
    protected $iptvService;
    protected $multiIptvService;
    
    public function __construct(IptvIntegrationService $iptvService, MultiIptvService $multiIptvService)
    {
        $this->iptvService = $iptvService;
        $this->multiIptvService = $multiIptvService;
    }

    public function movieList(Request $request)
    {
        $perPage = $request->input('per_page', 10);

        $movieList = Entertainment::where('status', 1);
        
        // Toujours filtrer par type 'movie' puisque c'est l'endpoint movie-list
        if (empty($request->language) && empty($request->genre_id )  && empty($request->actor_id )) {
            $movieList = $movieList->where('type','movie');
        }
        
        $movieList = $movieList->where('status', 1)
        ->whereDate('release_date', '<=', Carbon::now())  // Check release date is less than current date
        ->with([
            'entertainmentGenerMappings',
            'plan',
            'entertainmentReviews',
            'entertainmentTalentMappings',
            'entertainmentStreamContentMappings',
            'entertainmentDownloadMappings',
            'entertainmentView'
        ]);

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $movieList->where(function ($query) use ($searchTerm) {
                $query->where('name', 'like', "%{$searchTerm}%");
            });
        }
        
        if ($request->filled('genre_id')) {
            $genreId = $request->genre_id;
            $movieList->whereHas('entertainmentGenerMappings', function ($query) use ($genreId) {
                $query->where('genre_id', $genreId);
            });
        }
        
        // Nouveau filtre : Année de sortie
        if ($request->filled('release_year')) {
            $releaseYear = $request->release_year;
            $movieList->whereYear('release_date', $releaseYear);
        }
        
        // Nouveau filtre : Note IMDB minimale
        if ($request->filled('min_imdb_rating')) {
            $minRating = $request->min_imdb_rating;
            $movieList->where('IMDb_rating', '>=', $minRating);
        }
        
        if ($request->filled('actor_id')) {

            $actorId = $request->actor_id;

            $isMovieModuleEnabled = isenablemodule('movie');
            $isTVShowModuleEnabled = isenablemodule('tvshow');

            $movies = $movieList->where(function ($query) use ($actorId, $isMovieModuleEnabled, $isTVShowModuleEnabled) {
                if ($isMovieModuleEnabled && $isTVShowModuleEnabled) {

                    $query->where('type', 'movie')
                          ->orWhere('type', 'tvshow');
                } elseif ($isMovieModuleEnabled) {
                    $query->where('type', 'movie');
                } elseif ($isTVShowModuleEnabled) {
                    $query->where('type', 'tvshow');
                }
            })
            ->whereHas('entertainmentTalentMappings', function ($query) use ($actorId) {
                $query->where('talent_id', $actorId);
            });
        }
        if ($request->filled('language')) {
            $movieList->where('language', $request->language);
        }
        
        // Vérifier IPTV actif si l'intégration IPTV est activée
        if ($this->iptvService->isEnabled()) {
            \Log::info('IPTV Integration enabled in movieList');
            // On vérifie s'il y a des films avec IPTV actif
            $moviesWithIptv = $movieList->clone()->where('is_iptv', true)->count();
            \Log::info('Movies with IPTV active: ' . $moviesWithIptv);
        }
        
        // Nouveau tri : Par nombre de vues
        if ($request->filled('sort_by_views') && $request->sort_by_views === 'desc') {
            // Utiliser une sous-requête pour éviter les problèmes de GROUP BY en production
            $movieList = $movieList
                ->withCount(['entertainmentView as views_count'])
                ->orderBy('views_count', 'desc');
        } else {
            $movieList = $movieList->orderBy('created_at', 'desc');
        }
        
        $movies = $movieList->paginate($perPage);
        
        // Log pour déboguer les valeurs is_iptv
        if ($this->iptvService->isEnabled()) {
            $iptv_status = [];
            foreach ($movies as $movie) {
                $iptv_status[] = [
                    'id' => $movie->id,
                    'name' => $movie->name,
                    'is_iptv' => $movie->is_iptv ? 'true' : 'false'
                ];
            }
            \Log::info('IPTV status for movies in list: ' . json_encode($iptv_status));
        }
        
        $responseData = MoviesResource::collection($movies);

        if ($request->has('is_ajax') && $request->is_ajax == 1) {
            $html = '';
            foreach ($responseData->toArray($request) as $movieData) {

             if(isenablemodule($movieData['type'])==1){

                $userId = auth()->id();
                if($userId) {
                    $isInWatchList = WatchList::where('entertainment_id', $movieData['id'])
                    ->where('user_id', $userId)
                    ->exists();

                $movieData['is_watch_list'] = $isInWatchList ? true : false;

                }
                $html .= view('frontend::components.card.card_entertainment', ['value' => $movieData])->render();

             }

            }

            $hasMore = $movies->hasMorePages();

            return response()->json([
                'status' => true,
                'html' => $html,
                'message' => __('movie.movie_list'),
                'hasMore' => $hasMore,
            ], 200);
        }

        return response()->json([
            'status' => true,
            'data' => $responseData,
            'message' => __('movie.movie_list'),
        ], 200);
    }

    public function movieDetails(Request $request)
    {
        try {
            $movieId = $request->movie_id;
            $cacheKey = 'movie_' . $movieId . '_'.$request->profile_id;
            
            // Modifier la clé de cache si l'intégration IPTV est activée
            if ($this->iptvService->isEnabled()) {
                $cacheKey .= '_iptv';
            }
            
            // Stocker la clé de cache pour pouvoir la vider plus tard
            $cacheKeys = Cache::get('movie_cache_keys_' . $movieId, []);
            if (!in_array($cacheKey, $cacheKeys)) {
                $cacheKeys[] = $cacheKey;
                Cache::put('movie_cache_keys_' . $movieId, $cacheKeys, 60*24*7); // 1 semaine
            }

            // Force le rafraîchissement du cache si demandé
            if ($request->has('refresh_cache') || $request->has('force_refresh')) {
                Cache::forget($cacheKey);
                \Log::info('Cache forcibly refreshed for key: ' . $cacheKey);
            }
            
            $responseData = Cache::get($cacheKey);

            if (!$responseData) {
                \Log::info('Cache miss for key: ' . $cacheKey . ', generating new response');
                
                $movie = Entertainment::where('id', $movieId)
                    ->with(['entertainmentGenerMappings', 'plan', 'entertainmentReviews', 'entertainmentTalentMappings', 'entertainmentStreamContentMappings', 'entertainmentDownloadMappings', 'sources'])
                    ->first();
                
                if (!$movie) {
                    return response()->json([
                        'status' => false,
                        'message' => __('movie.movie_not_found'),
                    ], 404);
                }
                
                // Log pour déboguer les sources
                \Log::info('Movie ID: ' . $movie->id . ', Name: ' . $movie->name);
                \Log::info('Movie sources count: ' . $movie->sources->count());
                if ($movie->sources->count() > 0) {
                    \Log::info('Movie sources data: ' . json_encode($movie->sources->toArray()));
                } else {
                    \Log::info('No sources found for this movie');
                }
                
                // Vérifier si l'intégration multi-IPTV pour les films est activée ET si ce film a is_iptv activé
                if (config('entertainment.multi_iptv_enabled') && config('entertainment.iptv_movies_enabled') && !empty($movie->tmdb_id) && $movie->is_iptv) {
                    try {
                        \Log::info('Multi-IPTV Integration: Processing movie with TMDB ID: ' . $movie->tmdb_id . ' (is_iptv=true)');
                        
                        $multiIptvSources = $this->multiIptvService->getMovieSources($movie->tmdb_id);
                        \Log::info('Multi-IPTV Sources found: ' . $multiIptvSources->count());
                        
                        if ($multiIptvSources->isNotEmpty()) {
                            // Les sources sont déjà transformées dans MultiIptvService
                            $movie['iptv_sources'] = $multiIptvSources->toArray();
                            \Log::info('Multi-IPTV sources stored in movie array: ' . $multiIptvSources->count());
                        } else {
                            // Si nous n'avons pas trouvé de sources, désactiver is_iptv
                            $movie->is_iptv = false;
                            $movie->save();
                            \Log::info('Multi-IPTV Integration: Movie is_iptv flag updated to false - no sources available');
                        }
                    } catch (\Exception $e) {
                        \Log::error('Error in Multi-IPTV integration: ' . $e->getMessage());
                        // Continuer sans les sources IPTV en cas d'erreur
                    }
                }
                // Fallback sur l'ancien service IPTV simple si multi-IPTV n'est pas activé ET si is_iptv est activé
                else if ($this->iptvService->isEnabled() && config('entertainment.iptv_movies_enabled') && !empty($movie->tmdb_id) && $movie->is_iptv) {
                    try {
                        \Log::info('IPTV Integration: Processing movie with TMDB ID: ' . $movie->tmdb_id . ' (is_iptv=true)');
                        
                        $iptvSources = $this->iptvService->getMovieSources($movie->tmdb_id);
                        \Log::info('IPTV Sources found: ' . $iptvSources->count());
                        
                        if ($iptvSources->isNotEmpty()) {
                            // Transformer les sources IPTV au format compatible
                            $transformedSources = $this->iptvService->transformMovieSourcesToStreamMappings($iptvSources);
                            \Log::info('IPTV Sources transformed: ' . count($transformedSources));
                            
                            // Stocker les sources IPTV dans une propriété séparée
                            $movie['iptv_sources'] = $transformedSources;
                            \Log::info('IPTV sources stored in movie array: ' . count($transformedSources));
                        } else {
                            // Si nous n'avons pas trouvé de sources, désactiver is_iptv
                            $movie->is_iptv = false;
                            $movie->save();
                            \Log::info('IPTV Integration: Movie is_iptv flag updated to false - no sources available');
                        }
                    } catch (\Exception $e) {
                        \Log::error('Error in IPTV integration: ' . $e->getMessage());
                        // Continuer sans les sources IPTV en cas d'erreur
                    }
                }
                
                $movie['reviews'] = $movie->entertainmentReviews ?? null;
                $movie['hls_server_type'] = $movie->hls_format;

                if ($request->has('user_id')) {
                    $user_id = $request->user_id;
                    $movie['is_watch_list'] = WatchList::where('entertainment_id', $movieId)->where('user_id', $user_id)->where('profile_id', $request->profile_id)->exists();
                    $movie['is_likes'] = Like::where('entertainment_id', $movieId)->where('user_id', $user_id)->where('profile_id', $request->profile_id)->where('is_like', 1)->exists();
                    $movie['is_download'] = EntertainmentDownload::where('entertainment_id', $movieId)->where('device_id',$request->device_id)->where('user_id', $user_id)
                    ->where('entertainment_type', 'movie')->where('is_download', 1)->exists();
                    $movie['your_review'] = $movie->entertainmentReviews ? optional($movie->entertainmentReviews)->where('user_id', $user_id)->first() : null;
                   

                    if ($movie['your_review']) {
                        $movie['reviews'] = $movie['reviews']->where('user_id', '!=', $user_id);
                    }

                    $continueWatch = ContinueWatch::where('entertainment_id', $movie->id)->where('user_id', $user_id)->where('profile_id', $request->profile_id)->where('entertainment_type', 'movie')->first();
                    $movie['continue_watch'] = $continueWatch;
                }
                
                $responseData = new MovieDetailDataResource($movie);
                Cache::put($cacheKey, $responseData);
            }

            return response()->json([
                'status' => true,
                'data' => $responseData,
                'message' => __('movie.movie_details'),
            ], 200);
        } catch (\Exception $e) {
            \Log::error('MovieDetails Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Une erreur est survenue lors de la récupération des détails du film.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function tvshowList(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $tvshowList = Entertainment::where('status', 1);
        
        // Toujours filtrer par type 'tvshow' puisque c'est l'endpoint tvshow-list
        $tvshowList = $tvshowList->where('type', 'tvshow');
        
        $tvshowList = $tvshowList->where('status', 1)
            ->whereDate('release_date', '<=', Carbon::now())
            ->with([
                'entertainmentGenerMappings', 
                'plan', 
                'entertainmentReviews', 
                'entertainmentTalentMappings', 
                'season', 
                'episode',
                'entertainmentView'
            ]);

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $tvshowList->where(function($query) use ($searchTerm) {
                $query->where('name', 'like', "%{$searchTerm}%")
                      ->orWhereHas('entertainmentGenerMappings.genre', function ($subquery) use ($searchTerm) {
                          $subquery->where('name', 'like', "%{$searchTerm}%");
                      });
            });
        }
        
        // Filtrer par genre
        if ($request->filled('genre_id')) {
            $genreId = $request->genre_id;
            $tvshowList->whereHas('entertainmentGenerMappings', function ($query) use ($genreId) {
                $query->where('genre_id', $genreId);
            });
        }
        
        // Nouveau filtre : Année de sortie
        if ($request->filled('release_year')) {
            $releaseYear = $request->release_year;
            $tvshowList->whereYear('release_date', $releaseYear);
        }
        
        // Nouveau filtre : Note IMDB minimale
        if ($request->filled('min_imdb_rating')) {
            $minRating = $request->min_imdb_rating;
            $tvshowList->where('IMDb_rating', '>=', $minRating);
        }

        // Vérifier IPTV actif si l'intégration IPTV est activée
        if ($this->iptvService->isEnabled()) {
            \Log::info('IPTV Integration enabled in tvshowList');
            // On vérifie s'il y a des séries avec IPTV actif
            $showsWithIptv = $tvshowList->clone()->where('is_iptv', true)->count();
            \Log::info('TV shows with IPTV active: ' . $showsWithIptv);
        }

        // Log pour débogage
        \Log::info('TV Show Query: ' . $tvshowList->toSql());
        \Log::info('TV Show Params: ' . json_encode($tvshowList->getBindings()));

        // Nouveau tri : Par nombre de vues
        if ($request->filled('sort_by_views') && $request->sort_by_views === 'desc') {
            // Utiliser une sous-requête pour éviter les problèmes de GROUP BY en production
            $tvshowList = $tvshowList
                ->withCount(['entertainmentView as views_count'])
                ->orderBy('views_count', 'desc');
        } else {
            $tvshowList = $tvshowList->orderBy('created_at', 'desc');
        }
        
        $tvshowList = $tvshowList->paginate($perPage);
        
        // Log pour déboguer les valeurs is_iptv
        if ($this->iptvService->isEnabled()) {
            $iptv_status = [];
            foreach ($tvshowList as $tvshow) {
                $iptv_status[] = [
                    'id' => $tvshow->id,
                    'name' => $tvshow->name,
                    'is_iptv' => $tvshow->is_iptv ? 'true' : 'false'
                ];
            }
            \Log::info('IPTV status for TV shows in list: ' . json_encode($iptv_status));
        }

        $responseData = TvshowResource::collection($tvshowList);


        if ($request->has('is_ajax') && $request->is_ajax == 1) {
            $html = '';

            foreach($responseData->toArray($request) as $tvShowData) {
                $userId = auth()->id();
                if($userId) {
                    $isInWatchList = WatchList::where('entertainment_id', $tvShowData['id'])
                    ->where('user_id', $userId)
                    ->exists();

                // Set the flag in the movie data
                $tvShowData['is_watch_list'] = $isInWatchList ? true : false;
                }
                $html .= view('frontend::components.card.card_entertainment', ['value' => $tvShowData])->render();
            }

            $hasMore = $tvshowList->hasMorePages();

            return response()->json([
                'status' => true,
                'html' => $html,
                'message' => __('movie.tvshow_list'),
                'hasMore' => $hasMore,
            ], 200);
        }


        return response()->json([
            'status' => true,
            'data' => $responseData,
            'message' => __('movie.tvshow_list'),
        ], 200);
    }

    public function tvshowDetails(Request $request)
    {
        try {
            $tvshow_id = $request->tvshow_id;
            $cacheKey = 'tvshow_' . $tvshow_id . '_' . $request->profile_id;
            
            // Modifier la clé de cache si l'intégration IPTV est activée
            if ($this->iptvService->isEnabled()) {
                $cacheKey .= '_iptv';
            }
            
            // Force le rafraîchissement du cache si demandé
            if ($request->has('refresh_cache') || $request->has('force_refresh')) {
                Cache::forget($cacheKey);
                \Log::info('Cache forcibly refreshed for key: ' . $cacheKey);
            }

            $responseData = Cache::get($cacheKey);

            if (!$responseData) {
                \Log::info('Cache miss for key: ' . $cacheKey . ', generating new response');

                $tvshow = Entertainment::where('id', $tvshow_id)
                    ->with('entertainmentGenerMappings', 'plan', 'entertainmentReviews', 'entertainmentTalentMappings', 'season', 'episode')
                    ->first();
                    
                if (!$tvshow) {
                    return response()->json([
                        'status' => false,
                        'message' => __('movie.tvshow_not_found'),
                    ], 404);
                }
                
                // Vérifier si l'intégration multi-IPTV pour les séries est activée ET si cette série a is_iptv activé
                if (config('entertainment.multi_iptv_enabled') && config('entertainment.iptv_series_enabled') && !empty($tvshow->tmdb_id) && $tvshow->is_iptv) {
                    try {
                        \Log::info('Multi-IPTV Integration: Processing TV show with TMDB ID: ' . $tvshow->tmdb_id . ' (is_iptv=true)');
                        $iptvData = $this->multiIptvService->getTvShowData($tvshow->tmdb_id);
                        
                        if ($iptvData) {
                            $tvshow['iptv_seasons'] = $iptvData;
                        } else {
                            // Si aucune donnée n'est trouvée, désactiver is_iptv
                            $tvshow->is_iptv = false;
                            $tvshow->save();
                            \Log::info('Multi-IPTV Integration: TV show is_iptv flag updated to false - no data available');
                            $tvshow['iptv_seasons'] = [];
                        }
                    } catch (\Exception $e) {
                        \Log::error('Error in Multi-IPTV integration for series: ' . $e->getMessage());
                        $tvshow['iptv_seasons'] = [];
                        // Désactiver is_iptv en cas d'erreur également
                        $tvshow->is_iptv = false;
                        $tvshow->save();
                    }
                }
                // Fallback sur l'ancien service IPTV simple ET si is_iptv est activé
                else if ($this->iptvService->isEnabled() && config('entertainment.iptv_series_enabled') && !empty($tvshow->tmdb_id) && $tvshow->is_iptv) {
                    \Log::info('IPTV Integration enabled for TV Show details (is_iptv=true)');
                    try {
                        $iptvData = $this->iptvService->getTvShowData($tvshow->tmdb_id);
                        
                        if ($iptvData) {
                            $tvshow['iptv_seasons'] = $iptvData;
                        } else {
                            // Si aucune donnée n'est trouvée, désactiver is_iptv
                            $tvshow->is_iptv = false;
                            $tvshow->save();
                            \Log::info('IPTV Integration: TV show is_iptv flag updated to false - no data available');
                            $tvshow['iptv_seasons'] = [];
                        }
                    } catch (\Exception $e) {
                        \Log::error('Error in IPTV integration for series: ' . $e->getMessage());
                        $tvshow['iptv_seasons'] = [];
                        // Désactiver is_iptv en cas d'erreur également
                        $tvshow->is_iptv = false;
                        $tvshow->save();
                    }
                } else {
                    $tvshow['iptv_seasons'] = [];
                    // S'assurer que is_iptv est false si aucune intégration IPTV n'est activée
                    if ($tvshow->is_iptv && (!config('entertainment.multi_iptv_enabled') && !$this->iptvService->isEnabled())) {
                        $tvshow->is_iptv = false;
                        $tvshow->save();
                        \Log::info('IPTV Integration: TV show is_iptv flag updated to false - integration disabled');
                    }
                }

                $tvshow['reviews'] = $tvshow->entertainmentReviews ?? null;

                if ($request->has('user_id')) {
                    $user_id = $request->user_id;
                    $tvshow['user_id'] = $user_id;
                    $tvshow['is_watch_list'] = WatchList::where('entertainment_id', $request->tvshow_id)->where('user_id', $user_id)->where('profile_id', $request->profile_id)->exists();
                    $tvshow['is_likes'] = Like::where('entertainment_id', $request->tvshow_id)->where('user_id', $user_id)->where('profile_id', $request->profile_id)->where('is_like', 1)->exists();
                    $tvshow['your_review'] =  $tvshow->entertainmentReviews ? $tvshow->entertainmentReviews->where('user_id', $user_id)->first() :null;

                    if ($tvshow['your_review']) {
                        $tvshow['reviews'] = $tvshow['reviews']->where('user_id', '!=', $user_id);
                    }
                }

                $responseData = new TvshowDetailResource($tvshow);
                Cache::put($cacheKey, $responseData);
            }

            return response()->json([
                'status' => true,
                'data' => $responseData,
                'message' => __('movie.tvshow_details'),
            ], 200);
        } catch (\Exception $e) {
            \Log::error('TvshowDetails Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Une erreur est survenue lors de la récupération des détails de la série.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function tvshowEmbedSeasons(Request $request)
    {
        try {
            $tvshow_id = $request->tvshow_id;
            
            if (!$tvshow_id) {
                return response()->json([
                    'status' => false,
                    'message' => 'L\'ID de la série TV est requis',
                ], 400);
            }

            // Clé de cache spécifique pour les saisons embed
            $cacheKey = 'tvshow_embed_seasons_' . $tvshow_id;
            
            // Force le rafraîchissement du cache si demandé
            if ($request->has('refresh_cache') || $request->has('force_refresh')) {
                Cache::forget($cacheKey);
                \Log::info('Cache forcibly refreshed for key: ' . $cacheKey);
            }
            
            // Récupérer les données du cache
            $responseData = Cache::get($cacheKey);

            if (!$responseData) {
                \Log::info('Cache miss for embed seasons key: ' . $cacheKey . ', generating new response');

                // Vérifier si la série existe
                $tvshow = Entertainment::where('id', $tvshow_id)->first();
                
                if (!$tvshow) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Série TV non trouvée',
                    ], 404);
                }

                // Récupérer uniquement les saisons et épisodes de la base de données standard
                $seasons = Season::where('entertainment_id', $tvshow_id)
                    ->where('status', 1)
                    ->with(['episodes' => function($query) {
                        $query->where('status', 1)
                              ->with(['plan', 'EpisodeStreamContentMapping', 'episodeDownloadMappings', 'sources'])
                              ->orderBy('episode_number', 'asc');
                    }, 'plan'])
                    ->orderBy('season_index', 'asc')
                    ->get();

                // Structurer les données de réponse
                $responseData = [
                    'tvshow_id' => $tvshow_id,
                    'tvshow_name' => $tvshow->name,
                    'seasons' => $seasons->map(function ($season) {
                        return [
                            'id' => $season->id,
                            'name' => $season->name,
                            'season_index' => $season->season_index,
                            'poster_url' => $season->poster_url,
                            'description' => $season->description,
                            'short_desc' => $season->short_desc,
                            'access' => $season->access,
                            'episodes' => $season->episodes->map(function ($episode) {
                                return [
                                    'id' => $episode->id,
                                    'name' => $episode->name,
                                    'episode_number' => $episode->episode_number,
                                    'poster_url' => $episode->poster_url,
                                    'description' => $episode->description,
                                    'short_desc' => $episode->short_desc,
                                    'duration' => $episode->duration,
                                    'release_date' => $episode->release_date,
                                    'air_date' => $episode->air_date,
                                    'still_path' => $episode->still_path,
                                    'access' => $episode->access,
                                    'plan_id' => $episode->plan_id,
                                    'IMDb_rating' => $episode->IMDb_rating,
                                    'content_rating' => $episode->content_rating,
                                    'video_upload_type' => $episode->video_upload_type,
                                    'video_url_input' => $episode->video_url_input,
                                    'download_status' => $episode->download_status,
                                    'enable_download_quality' => $episode->enable_download_quality,
                                    'plan' => $episode->plan,
                                    'sources' => $episode->sources,
                                    'video_links' => $episode->EpisodeStreamContentMapping
                                ];
                            })
                        ];
                    })
                ];

                // Mettre en cache pour 30 minutes
                Cache::put($cacheKey, $responseData, 30);
            }

            return response()->json([
                'status' => true,
                'data' => $responseData,
                'message' => 'Saisons et épisodes récupérés avec succès',
            ], 200);

        } catch (\Exception $e) {
            \Log::error('TvshowEmbedSeasons Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Une erreur est survenue lors de la récupération des saisons et épisodes.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function saveDownload(Request $request)
    {
        $user = auth()->user();
        $download_data = $request->all();
        $download_data['user_id'] = $user->id;

        $download = EntertainmentDownload::where('entertainment_id', $request->entertainment_id)->where('user_id', $user->id)->where('entertainment_type', $request->entertainment_type)->first();

        if (!$download) {
            $result = EntertainmentDownload::create($download_data);

            if ($request->entertainment_type == 'movie') {

                Cache::flush();

            } else if ($request->entertainment_type == 'episode') {
                Cache::flush();

            }

            return response()->json(['status' => true, 'message' => __('movie.movie_download')]);
        } else {
            return response()->json(['status' => true, 'message' => __('movie.already_download')]);
        }
    }

    public function episodeList(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $user_id = $request->user_id;
        $episodeList = Episode::where('status', 1)->with('entertainmentdata', 'plan', 'EpisodeStreamContentMapping', 'episodeDownloadMappings');

        if ($request->has('tvshow_id')) {
            $episodeList = $episodeList->where('entertainment_id', $request->tvshow_id);
        }
        if ($request->has('season_id')) {
            $episodeList = $episodeList->where('season_id', $request->season_id);
        }

        if ($request->has('search')) {
            $searchTerm = $request->search;
            $episodeList->where(function ($query) use ($searchTerm) {
                $query->where('name', 'like', "%{$searchTerm}%");
            });
        }

        $episodes = $episodeList->orderBy('id', 'asc')->paginate($perPage);
        
        // Récupérer la collection standard d'épisodes
        $standardEpisodes = $episodes->getCollection();
        
        // Vérifier si nous devons ajouter des épisodes IPTV (si is_iptv est activé pour la série)
        $iptvEpisodes = collect([]);
        
        if ($this->iptvService->isEnabled() && $request->has('tvshow_id') && $request->has('season_id')) {
            // Vérifier si la série est compatible IPTV
            $tvshow = Entertainment::select('id', 'is_iptv')->find($request->tvshow_id);
            
            if ($tvshow && $tvshow->is_iptv) {
                \Log::info('IPTV Integration: Fetching IPTV episodes for tvshow_id=' . $request->tvshow_id . ', season_id=' . $request->season_id);
                
                // Récupérer les épisodes IPTV de cette saison
                $iptvEpisodesArray = $this->iptvService->iptvEpisodeList(
                    $request->tvshow_id,
                    $request->season_id
                );
                
                if (!empty($iptvEpisodesArray)) {
                    \Log::info('IPTV Integration: Found ' . count($iptvEpisodesArray) . ' IPTV episodes');
                    
                    // Convertir en collection pour une manipulation plus facile
                    $iptvEpisodes = collect($iptvEpisodesArray)->map(function ($episode) use ($user_id) {
                        // Transforme chaque tableau d'épisode en objet pour être compatible avec EpisodeResource
                        return (object)$episode;
                    });
                }
            }
        }
        
        // Fusionner les épisodes standards et IPTV
        $allEpisodes = $standardEpisodes->concat($iptvEpisodes);
        
        // Trier par numéro d'épisode
        $allEpisodes = $allEpisodes->sortBy('episode_number');
        
        // Remplacer la collection de la pagination avec notre collection fusionnée
        $episodes->setCollection($allEpisodes);

        $responseData = EpisodeResource::collection(
            $episodes->getCollection()->map(function ($episode) use ($user_id) {
                return new EpisodeResource($episode, $user_id);
            })
        );

        if ($request->has('is_ajax') && $request->is_ajax == 1) {
            $html = '';

            foreach ($responseData->toArray($request) as $index => $value) {
                $html .= view('frontend::components.card.card_episode', [
                    'data' => $value,
                    'index' => $index
                ])->render();
            }

            $hasMore = $episodes->hasMorePages();

            return response()->json([
                'status' => true,
                'html' => $html,
                'message' => __('movie.episode_list'),
                'hasMore' => $hasMore,
            ], 200);
        }

        return response()->json([
            'status' => true,
            'data' => $responseData,
            'message' => __('movie.episode_list'),
        ], 200);
    }

    public function episodeDetails(Request $request)
    {
        $user_id = $request->user_id;
        $episode_id = $request->episode_id;

        $cacheKey = 'episode_' . $episode_id .'_'.$request->profile_id;
        
        // Force le rafraîchissement du cache si demandé
        if ($request->has('refresh_cache') || $request->has('force_refresh')) {
            Cache::forget($cacheKey);
            \Log::info('Cache forcibly refreshed for episode key: ' . $cacheKey);
        }

        $responseData = Cache::get($cacheKey);

        if (!$responseData) {
            // Essayer de trouver l'épisode dans la table épisode standard
            $episode = Episode::where('id', $episode_id)->with('entertainmentdata', 'plan', 'EpisodeStreamContentMapping', 'episodeDownloadMappings')->first();

            // Si aucun épisode trouvé dans la table standard, essayer comme épisode IPTV
            if (!$episode && $this->iptvService->isEnabled()) {
                \Log::info('Episode not found in standard table, checking IPTV episode ID=' . $episode_id);
                
                // Récupérer les détails de l'épisode IPTV
                $iptvEpisodeData = $this->iptvService->iptvEpisodeDetails($episode_id);
                
                if ($iptvEpisodeData) {
                    \Log::info('IPTV episode found with ID=' . $episode_id);
                    
                    // Convertir en objet pour être compatible avec EpisodeDetailResource
                    $episode = (object)$iptvEpisodeData;
                    
                    // Formater la réponse directement car EpisodeDetailResource attend une structure spécifique
                    $responseData = $iptvEpisodeData;
                    
                    Cache::put($cacheKey, $responseData);
                    
                    return response()->json([
                        'status' => true,
                        'data' => $responseData,
                        'message' => __('movie.episode_details'),
                    ], 200);
                } else {
                    \Log::info('Episode not found in IPTV table either for ID=' . $episode_id);
                    return response()->json([
                        'status' => false,
                        'message' => __('movie.episode_not_found'),
                    ], 404);
                }
            }
            
            // Si aucun épisode n'est trouvé (ni standard ni IPTV)
            if (!$episode) {
                return response()->json([
                    'status' => false,
                    'message' => __('movie.episode_not_found'),
                ], 404);
            }

            if ($request->has('user_id')) {
                $continueWatch = ContinueWatch::where('entertainment_id', $episode->id)->where('user_id', $user_id)->where('profile_id', $request->profile_id)->where('entertainment_type', 'episode')->first();
                $episode['continue_watch'] = $continueWatch;

                $episode['is_download'] = EntertainmentDownload::where('entertainment_id', $episode->id)->where('user_id',  $user_id)->where('entertainment_type', 'episode')->where('is_download', 1)->exists();

                $genre_ids = $episode->entertainmentData->entertainmentGenerMappings->pluck('genre_id');

                $episode['moreItems'] = Entertainment::where('type', 'tvshow')
                    ->whereHas('entertainmentGenerMappings', function ($query) use ($genre_ids) {
                        $query->whereIn('genre_id', $genre_ids);
                    })
                    ->where('id', '!=', $episode->id)
                    ->orderBy('id', 'desc')
                    ->get();

                $episode['genre_data'] = Genres::whereIn('id', $genre_ids)->get();
            }

            $responseData = new EpisodeDetailResource($episode);
            Cache::put($cacheKey, $responseData);
        }

        return response()->json([
            'status' => true,
            'data' => $responseData,
            'message' => __('movie.episode_details'),
        ], 200);
    }

    public function searchList(Request $request)
    {

        $perPage = $request->input('per_page', 10);
        $movieList = Entertainment::query()->with('entertainmentGenerMappings', 'plan', 'entertainmentReviews', 'entertainmentTalentMappings', 'entertainmentStreamContentMappings')->where('type', 'movie');

        $movieList = $movieList->where('status', 1);

        $movies = $movieList->orderBy('updated_at', 'desc');
        $movies = $movies->paginate($perPage);

        $responseData = new SearchResource($movies);
        if(isenablemodule('movie') == 1){
            $responseData = $responseData;

        }else{
            $responseData = [];
        }

        return response()->json([
            'status' => true,
            'data' => $responseData,
            'message' => __('movie.search_list'),
        ], 200);
    }

    public function getSearch(Request $request)
    {
        $perPage = $request->input('per_page', 10);

        $movieList = Entertainment::query()->whereDate('release_date', '<=', Carbon::now())->with('entertainmentGenerMappings', 'plan', 'entertainmentReviews', 'entertainmentTalentMappings', 'entertainmentStreamContentMappings')->where('type', 'movie')->where('status', 1);

        if ($request->has('search') && $request->search !='') {

            $searchTerm = $request->search;

            if (strtolower($searchTerm) == 'movie' || strtolower($searchTerm) == 'movies') {
                $movieList->where('type', 'movie');
            } else {

                $movieList->where('name', 'like', "%{$searchTerm}%")
                    ->orWhereHas('entertainmentGenerMappings.genre', function ($query) use ($searchTerm) {
                        $query->where('name', 'like', "%{$searchTerm}%");
                    });
            }

        }

        $movieList = $movieList->orderBy('created_at', 'desc')->paginate($perPage);


        $movieData = (isenablemodule('movie') == 1) ? MoviesResource::collection($movieList) : [];
        
        $tvshowList = Entertainment::where('status', 1)->where('type', 'tvshow')
            ->whereDate('release_date', '<=', Carbon::now())
            ->with('entertainmentGenerMappings', 'plan', 'entertainmentReviews', 'entertainmentTalentMappings', 'season', 'episode');
           // ->whereHas('episode');

        if ($request->has('search') && $request->search !='') {
            $searchTerm = $request->search;
            $tvshowList->where(function($query) use ($searchTerm) {
                $query->where('name', 'like', "%{$searchTerm}%")
                      ->orWhereHas('entertainmentGenerMappings.genre', function ($subquery) use ($searchTerm) {
                          $subquery->where('name', 'like', "%{$searchTerm}%");
                      });
            });
        }

        // Log pour débogage
        \Log::info('TV Show Query: ' . $tvshowList->toSql());
        \Log::info('TV Show Params: ' . json_encode($tvshowList->getBindings()));

        $tvshowList = $tvshowList->orderBy('created_at', 'desc')->where('type', 'tvshow')->paginate($perPage);
        $tvshowData = (isenablemodule('tvshow') == 1) ? TvshowResource::collection($tvshowList) : [];


        $videoList = Video::query()->whereDate('release_date', '<=', Carbon::now())->with('VideoStreamContentMappings', 'plan');

        if ($request->has('search') && $request->search !='') {

            $searchTerm = $request->search;
            $videoList->where('name', 'like', "%{$searchTerm}%");
        }

        $videoList = $videoList->where('status', 1)->orderBy('created_at', 'desc')->take(6)->get();
        $videoData = (isenablemodule('video') == 1) ? VideoResource::collection($videoList) : [];


        $seasonList = Season::query()->with('episodes');

        if ($request->has('search') && $request->search !='') {

            $searchTerm = $request->search;
            $seasonList->where('name', 'like', "%{$searchTerm}%");
        }

        $seasonList = $seasonList->where('status', 1)->orderBy('created_at', 'desc')->get();
        $seasonData = (isenablemodule('tvshow') == 1) ? SeasonResource::collection($seasonList) : [];


        $episodeList = Episode::query()->whereDate('release_date', '<=', Carbon::now())->with('seasondata');

        if ($request->has('search') && $request->search !='') {

            $searchTerm = $request->search;
            $episodeList->where('name', 'like', "%{$searchTerm}%");
        }

        $episodeList = $episodeList->where('status', 1)->orderBy('created_at', 'desc')->get();
        $episodeData = (isenablemodule('tvshow') == 1) ? EpisodeResource::collection($episodeList) : [];


        $actorList = CastCrew::query()->where('type', 'actor')->with('entertainmentTalentMappings');

        if ($request->has('search') && $request->search !='') {

            $searchTerm = $request->search;
            $actorList->where('name', 'like', "%{$searchTerm}%");
        }

        $actorList = $actorList->orderBy('created_at', 'desc')->get();
        $actorData = CastCrewListResource::collection($actorList);


        $directorList = CastCrew::query()->where('type', 'director')->with('entertainmentTalentMappings');

        if ($request->has('search') && $request->search !='') {

            $searchTerm = $request->search;
            $directorList->where('name', 'like', "%{$searchTerm}%");
        }

        $directorList = $directorList->orderBy('created_at', 'desc')->take(6)->get();
        $directorData = CastCrewListResource::collection($directorList);



        if ($request->has('is_ajax') && $request->is_ajax == 1) {

            $html = '';

            if($movieData && $movieData->isNotEmpty()) {

                foreach ($movieData->toArray($request) as $index => $value) {

                    $html .= view('frontend::components.card.card_entertainment', [
                        'value' => $value,
                        'index' => $index,
                        'is_search'=>1,
                    ])->render();
                }
            }
            if ($tvshowData && $tvshowData->isNotEmpty()) {

                foreach ($tvshowData->toArray($request) as $index => $value) {
                    $html .= view('frontend::components.card.card_entertainment', [
                        'value' => $value,
                        'index' => $index,
                        'is_search'=>1,
                    ])->render();
                }
            }
            if ($videoData && $videoData->isNotEmpty()) {

                foreach ($videoData->toArray($request) as $index => $value) {
                    $html .= view('frontend::components.card.card_video', [
                        'data' => $value,
                        'index' => $index,
                        'is_search'=>1,
                    ])->render();
                }
            }
            if ($seasonData && $seasonData->isNotEmpty()) {

                foreach ($seasonData->toArray($request) as $index => $value) {
                    $html .= view('frontend::components.card.card_season', [
                        'value' => $value,
                        'index' => $index,
                        'is_search'=>1,
                    ])->render();
                }
            }
            if ($episodeData && $episodeData->isNotEmpty()) {

                foreach ($episodeData->toArray($request) as $index => $value) {
                    $html .= view('frontend::components.card.card_season', [
                        'value' => $value,
                        'index' => $index,
                        'is_search'=>1,
                    ])->render();
                }
            }
            if ($actorData && $actorData->isNotEmpty()) {

                foreach ($actorData->toArray($request) as $index => $value) {
                    $html .= view('frontend::components.card.card_castcrew', [
                        'data' => $value,
                        'index' => $index,
                        'is_search'=>1,
                    ])->render();
                }
            }
            if ($directorData && $directorData->isNotEmpty()) {

                foreach ($directorData->toArray($request) as $index => $value) {
                    $html .= view('frontend::components.card.card_castcrew', [
                        'data' => $value,
                        'index' => $index,
                        'is_search'=>1,
                    ])->render();
                }
            }

            if (empty($movieData) && empty($tvshowData) && empty($videoData) && empty($seasonData) && empty($episodeData) && empty($actorData) && empty($directorData)) {
                $html .= '';
            }


            return response()->json([
                'status' => true,
                'html' => $html,
                'message' => __('movie.search_list'),

            ], 200);
        }

        return response()->json([
            'status' => true,
            'movieList' => $movieData,
            'tvshowList' => $tvshowData,
            'videoList' => $videoData,
            'seasonList' => $seasonData,
            'message' => __('movie.search_list'),
        ], 200);
    }


    public function comingSoon(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $todayDate = Carbon::today()->format('Y-m-d');

        $entertainmentList = Entertainment::where('release_date', '>=', $todayDate)->where('status', 1)
        ->with([
            'UserReminder' => function ($query) use ($request) {
                $query->where('user_id', $request->user_id);
            },
            'entertainmentGenerMappings',
            'plan',
            'entertainmentReviews',
            'entertainmentTalentMappings',
            'entertainmentStreamContentMappings',
            'season'

        ]);

        $entertainment = $entertainmentList->paginate($perPage);

        $responseData = ComingSoonResource::collection($entertainment);

        if ($request->has('is_ajax') && $request->is_ajax == 1) {
            $html = '';

            $entertainmentList->when(Auth::check(), function ($query) {
                $query->with(['UserRemind' => function ($query) {
                    $query->where('user_id', Auth::id());
                }]);
            })->get();
            $entertainment = $entertainmentList->paginate($perPage);
            $responseData = ComingSoonResource::collection($entertainment);

            foreach ($responseData->toArray($request) as $comingSoonData) {

               if(isenablemodule( $comingSoonData['type'])==1){

                $html .= view('frontend::components.card.card_comingsoon', ['data' => $comingSoonData])->render();

               }

            }

            $hasMore = $entertainment->hasMorePages();

            return response()->json([
                'status' => true,
                'html' => $html,
                'message' => __('movie.coming_soon_list'),
                'hasMore' => $hasMore,
            ], 200);
        }

        return response()->json([
            'status' => true,
            'data' => $responseData,
            'message' => __('movie.coming_soon_list'),
        ], 200);
    }

    public function saveReminder(Request $request)
    {
        $user = auth()->user();
        $reminderData = $request->all();
        $reminderData['user_id'] = $user->id;

        $profile_id=$request->has('profile_id') && $request->profile_id
        ? $request->profile_id
        : getCurrentProfile($user->id, $request);

        $reminderData['profile_id'] = $profile_id;



        $entertainment = $request->entertainment_id ? Entertainment::where('id', $request->entertainment_id)->first() : null;
        if($entertainment != null){
            $reminderData['release_date'] = $request->release_date ?? $entertainment->release_date;
        }


        $reminders = UserReminder::updateOrCreate(
            ['entertainment_id' => $request->entertainment_id, 'user_id' => $user->id, 'profile_id'=>$profile_id],
            $reminderData
        );

        Cache::flush();

        $message = $reminders->wasRecentlyCreated ? __('movie.reminder_add') : __('movie.reminder_update');
        $result = $reminders;

        return response()->json(['status' => true, 'message' => $message]);
    }

    public function saveEntertainmentViews(Request $request)
    {
        $user = auth()->user();
        $data = $request->all();
        $data['user_id'] = $user->id;
        $viewData = EntertainmentView::where('entertainment_id', $request->entertainment_id)->where('user_id', $user->id)->first();

        Cache::flush();

        if (!$viewData) {
            $views = EntertainmentView::create($data);
            $message = __('movie.view_add');
        } else {
            $message = __('movie.already_added');
        }

        return response()->json(['status' => true, 'message' => $message]);
    }

    /**
     * Enregistre une vue publique sans authentification (anonyme)
     * Crée un enregistrement EntertainmentView avec user_id = null pour les vues anonymes
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function savePublicViews(Request $request)
    {
        try {
            // Validation simple
            $request->validate([
                'entertainment_id' => 'required|integer|exists:entertainments,id'
            ]);

            $entertainmentId = $request->entertainment_id;
            
            // Récupérer le contenu pour vérifier qu'il existe
            $entertainment = Entertainment::find($entertainmentId);
            
            if (!$entertainment) {
                return response()->json([
                    'status' => false, 
                    'message' => 'Contenu introuvable'
                ], 404);
            }

            // Créer une vue anonyme (user_id = null)
            $data = [
                'entertainment_id' => $entertainmentId,
                'user_id' => null, // Vue anonyme
                'created_at' => now(),
                'updated_at' => now()
            ];

            EntertainmentView::create($data);
            
            // Compter le total de vues pour ce contenu
            $totalViews = EntertainmentView::where('entertainment_id', $entertainmentId)->count();
            
            // Optionnel : Log pour debug
            \Log::info("Vue publique ajoutée pour entertainment_id: {$entertainmentId}, total: {$totalViews}");
            
            // Vider le cache
            Cache::flush();

            return response()->json([
                'status' => true, 
                'message' => 'Vue enregistrée',
                'total_views' => $totalViews
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'status' => false,
                'message' => 'Paramètres invalides',
                'errors' => $e->errors()
            ], 422);
            
        } catch (\Exception $e) {
            \Log::error('Erreur savePublicViews: ' . $e->getMessage());
            
            return response()->json([
                'status' => false,
                'message' => 'Erreur lors de l\'enregistrement de la vue'
            ], 500);
        }
    }

    public function deleteReminder(Request $request)
    {
        $user = auth()->user();

        $ids = $request->is_ajax == 1 ? $request->id : explode(',', $request->id);

        $entertainment = Entertainment::whereIn('id',$ids)->get();

        $reminders = UserReminder::whereIn('entertainment_id', $ids)->where('user_id', $user->id)->forceDelete();

        Cache::flush();

        if ($reminders == null) {

            $message = __('movie.reminder_add');

            return response()->json(['status' => false, 'message' => $message]);
        }

        $message = __('movie.reminder_remove');


        return response()->json(['status' => true, 'message' => $message]);
    }
    public function deleteDownload(Request $request)
    {
        $user = auth()->user();

        $ids = explode(',', $request->id);

        $download = EntertainmentDownload::whereIn('id', $ids)->forceDelete();

        Cache::flush();

        if ($download == null) {

            $message = __('movie.download');

            return response()->json(['status' => false, 'message' => $message]);
        }

        $message = __('movie.download');


        return response()->json(['status' => true, 'message' => $message]);
    }
    
    /**
     * Active ou désactive l'option IPTV pour un contenu
     */
    public function toggleIptv(Request $request)
    {
        try {
            // Valider les données reçues
            $validated = $request->validate([
                'id' => 'required|integer',
                'type' => 'required|string|in:movie,tvshow',
                'is_iptv' => 'required|boolean'
            ]);
            
            // Chercher le contenu
            $entertainment = Entertainment::findOrFail($validated['id']);
            
            // Vérifier si l'utilisateur est authentifié
            $user = auth()->user();
            if (!$user) {
                return response()->json([
                    'status' => false,
                    'message' => __('movie.auth_required')
                ], 401);
            }
            
            // Mettre à jour le statut is_iptv
            $entertainment->is_iptv = $validated['is_iptv'];
            $entertainment->save();
            
            // Vider le cache pour ce contenu
            if ($entertainment->type == 'movie') {
                $cacheKey = 'movie_' . $entertainment->id . '_*';
            } else {
                $cacheKey = 'tvshow_' . $entertainment->id . '_*';
            }
            
            Cache::flush(); // On pourrait être plus spécifique, mais pour s'assurer que tout est à jour
            
            return response()->json([
                'status' => true,
                'message' => $validated['is_iptv'] 
                    ? __('movie.iptv_enabled') 
                    : __('movie.iptv_disabled'),
                'is_iptv' => $entertainment->is_iptv
            ]);
            
        } catch (\Exception $e) {
            \Log::error('ToggleIptv Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Une erreur est survenue lors de la mise à jour du statut IPTV.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupère la liste des épisodes IPTV pour une série et une saison spécifique
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function iptvEpisodeList(Request $request)
    {
        try {
            // Valider les paramètres requis
            $validated = $request->validate([
                'tvshow_id' => 'required|integer|exists:entertainments,id',
                'season_id' => 'required|integer',
            ]);
            
            $tvshowId = $request->tvshow_id;
            $seasonId = $request->season_id;
            $perPage = $request->input('per_page', 10);
            $user_id = $request->user_id;
            
            // Vérifier que l'intégration IPTV est activée
            if (!$this->iptvService->isEnabled()) {
                \Log::info('IPTV Integration is disabled');
                return response()->json([
                    'status' => false,
                    'message' => __('movie.iptv_disabled'),
                ], 404);
            }
            
            // Vérifier que la série est compatible IPTV
            $tvshow = Entertainment::select('id', 'is_iptv')->find($tvshowId);
            if (!$tvshow || !$tvshow->is_iptv) {
                \Log::info('TV show is not IPTV enabled, ID=' . $tvshowId);
                return response()->json([
                    'status' => false,
                    'message' => __('movie.iptv_not_enabled'),
                ], 404);
            }
            
            \Log::info('Getting IPTV episodes for tvshow_id=' . $tvshowId . ', season_id=' . $seasonId);
            
            // Récupérer les épisodes IPTV
            $iptvEpisodesArray = $this->iptvService->iptvEpisodeList($tvshowId, $seasonId);
            
            if (empty($iptvEpisodesArray)) {
                \Log::info('No IPTV episodes found for tvshow_id=' . $tvshowId . ', season_id=' . $seasonId);
                return response()->json([
                    'status' => true,
                    'data' => [],
                    'message' => __('movie.no_episodes_found'),
                ], 200);
            }
            
            \Log::info('Found ' . count($iptvEpisodesArray) . ' IPTV episodes');
            
            // Convertir en collection pour une manipulation plus facile
            $iptvEpisodes = collect($iptvEpisodesArray)->map(function ($episode) use ($user_id) {
                // Convertir en objet pour la cohérence avec le reste de l'API
                return (object)$episode;
            });
            
            // Paginer manuellement
            $page = $request->input('page', 1);
            $offset = ($page - 1) * $perPage;
            $total = $iptvEpisodes->count();
            $lastPage = ceil($total / $perPage);
            
            $paginatedEpisodes = $iptvEpisodes->slice($offset, $perPage)->values();
            
            if ($request->has('is_ajax') && $request->is_ajax == 1) {
                $html = '';
                
                foreach ($paginatedEpisodes as $index => $episode) {
                    $html .= view('frontend::components.card.card_episode', [
                        'data' => $episode,
                        'index' => $index
                    ])->render();
                }
                
                $hasMore = $page < $lastPage;
                
                return response()->json([
                    'status' => true,
                    'html' => $html,
                    'message' => __('movie.iptv_episode_list'),
                    'hasMore' => $hasMore,
                ], 200);
            }
            
            return response()->json([
                'status' => true,
                'data' => $paginatedEpisodes,
                'message' => __('movie.iptv_episode_list'),
                'current_page' => $page,
                'last_page' => $lastPage,
                'per_page' => $perPage,
                'total' => $total
            ], 200);
            
        } catch (\Exception $e) {
            \Log::error('Error in iptvEpisodeList: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => __('movie.error_fetching_episodes'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Récupère les détails d'un épisode IPTV spécifique
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function iptvEpisodeDetails(Request $request)
    {
        try {
            // Valider les paramètres requis
            $validated = $request->validate([
                'episode_id' => 'required|integer',
            ]);
            
            $episodeId = $request->episode_id;
            $user_id = $request->user_id;
            
            // Vérifier que l'intégration IPTV est activée
            if (!$this->iptvService->isEnabled()) {
                \Log::info('IPTV Integration is disabled');
                return response()->json([
                    'status' => false,
                    'message' => __('movie.iptv_disabled'),
                ], 404);
            }
            
            $cacheKey = 'iptv_episode_' . $episodeId . '_' . $request->profile_id;
            
            // Force le rafraîchissement du cache si demandé
            if ($request->has('refresh_cache') || $request->has('force_refresh')) {
                Cache::forget($cacheKey);
                \Log::info('Cache forcibly refreshed for IPTV episode key: ' . $cacheKey);
            }
            
            $responseData = Cache::get($cacheKey);
            
            if (!$responseData) {
                \Log::info('Getting IPTV episode details for episode_id=' . $episodeId);
                
                // Récupérer les détails de l'épisode IPTV
                $iptvEpisodeData = $this->iptvService->iptvEpisodeDetails($episodeId);
                
                if (!$iptvEpisodeData) {
                    \Log::info('No IPTV episode found with ID=' . $episodeId);
                    return response()->json([
                        'status' => false,
                        'message' => __('movie.episode_not_found'),
                    ], 404);
                }
                
                // Utiliser les données directement
                $responseData = $iptvEpisodeData;
                Cache::put($cacheKey, $responseData);
            }
            
            return response()->json([
                'status' => true,
                'data' => $responseData,
                'message' => __('movie.iptv_episode_details'),
            ], 200);
            
        } catch (\Exception $e) {
            \Log::error('Error in iptvEpisodeDetails: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => __('movie.error_fetching_episode_details'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function clearMovieCache(Request $request)
    {
        try {
            $movieId = $request->movie_id;
            
            if (!$movieId) {
                return response()->json([
                    'status' => false,
                    'message' => 'L\'ID du film est requis',
                ], 400);
            }
            
            $cacheKey = 'movie_' . $movieId;
            Cache::forget($cacheKey);
            
            // Vider également les variantes du cache (avec profile_id)
            $keys = Cache::get('movie_cache_keys_' . $movieId, []);
            foreach ($keys as $key) {
                Cache::forget($key);
            }
            
            return response()->json([
                'status' => true,
                'message' => 'Cache du film vidé avec succès',
            ], 200);
        } catch (\Exception $e) {
            \Log::error('ClearMovieCache Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Une erreur est survenue lors du vidage du cache.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function addMovieSources(Request $request)
    {
        try {
            $movieId = $request->movie_id;
            
            if (!$movieId) {
                return response()->json([
                    'status' => false,
                    'message' => 'L\'ID du film est requis',
                ], 400);
            }
            
            $movie = Entertainment::find($movieId);
            
            if (!$movie) {
                return response()->json([
                    'status' => false,
                    'message' => 'Film non trouvé',
                ], 404);
            }
            
            $urls = $request->urls;
            
            if (!$urls || !is_array($urls) || empty($urls)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Aucune URL fournie',
                ], 400);
            }
            
            $addedCount = 0;
            $errors = [];
            
            foreach ($urls as $url) {
                if (empty($url) || !filter_var($url, FILTER_VALIDATE_URL)) {
                    $errors[] = "URL invalide ou vide: " . $url;
                    continue;
                }
                
                // Extraire le nom de domaine pour le titre
                $parsedUrl = parse_url($url);
                $domain = isset($parsedUrl['host']) ? $parsedUrl['host'] : 'embed';
                $domainParts = explode('.', $domain);
                $extractedTitle = $domainParts[0] ?? 'embed';
                
                try {
                    // Vérifier si la source existe déjà
                    $existingSource = \Modules\Entertainment\Models\Source::where('sourceable_type', 'Modules\Entertainment\Models\Entertainment')
                        ->where('sourceable_id', $movieId)
                        ->where('url', $url)
                        ->first();
                    
                    if ($existingSource) {
                        $errors[] = "La source existe déjà: " . $url;
                        continue;
                    }
                    
                    // Ajouter la nouvelle source
                    $source = new \Modules\Entertainment\Models\Source([
                        'title' => $extractedTitle,
                        'url' => $url,
                        'url_type' => 'embed',
                        'source_type' => 'both',
                        'status' => 'active'
                    ]);
                    
                    $movie->sources()->save($source);
                    $addedCount++;
                    
                    \Log::info("Source ajoutée avec succès: {$url} pour le film ID {$movieId}");
                } catch (\Exception $e) {
                    $errors[] = "Erreur lors de l'ajout de la source {$url}: " . $e->getMessage();
                    \Log::error("Erreur lors de l'ajout de la source {$url} pour le film ID {$movieId}: " . $e->getMessage());
                }
            }
            
            // Vider le cache pour ce film
            $cacheKey = 'movie_' . $movieId;
            Cache::forget($cacheKey);
            
            // Vider également les variantes du cache (avec profile_id)
            $keys = Cache::get('movie_cache_keys_' . $movieId, []);
            foreach ($keys as $key) {
                Cache::forget($key);
            }
            
            return response()->json([
                'status' => true,
                'message' => $addedCount . ' source(s) ajoutée(s) avec succès',
                'added_count' => $addedCount,
                'errors' => $errors
            ], 200);
            
        } catch (\Exception $e) {
            \Log::error('AddMovieSources Error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Une erreur est survenue lors de l\'ajout des sources.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

