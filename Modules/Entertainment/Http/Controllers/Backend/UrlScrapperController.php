<?php

namespace Modules\Entertainment\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Entertainment\Services\UrlScrapperService;
use Illuminate\Http\JsonResponse;

class UrlScrapperController extends Controller
{
    protected $scrapperService;

    public function __construct(UrlScrapperService $scrapperService)
    {
        $this->scrapperService = $scrapperService;
    }

    /**
     * Extrait les URLs de streaming à partir d'un contenu HTML ou d'une URL
     */
    public function extractUrls(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'content' => 'required|string|min:10'
            ]);

            $content = $request->input('content');
            $result = $this->scrapperService->extractStreamingUrls($content);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'urls' => $result['urls'],
                        'count' => count($result['urls'])
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur serveur: ' . $e->getMessage()
            ], 500);
        }
    }
} 