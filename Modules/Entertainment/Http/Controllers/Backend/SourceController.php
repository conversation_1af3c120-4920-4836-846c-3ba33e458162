<?php

namespace Modules\Entertainment\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Entertainment\Models\Entertainment;
use Modules\Entertainment\Models\Source;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class SourceController extends Controller
{
    /**
     * Ajoute des sources en masse à un divertissement
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkStore(Request $request)
    {
        try {
            // Valider la requête
            $validated = $request->validate([
                'entertainment_id' => 'required|exists:entertainments,id',
                'sources' => 'required|array',
                'sources.*.title' => 'required|string',
                'sources.*.source_type' => 'required|string',
                'sources.*.url_type' => 'required|string',
                'sources.*.url' => 'required|string|url',
            ]);

            // Récupérer le divertissement
            $entertainment = Entertainment::findOrFail($request->entertainment_id);
            
            // Compteur de sources ajoutées
            $count = 0;
            
            // Ajouter chaque source
            foreach ($request->sources as $sourceData) {
                try {
                    $source = $entertainment->sources()->create([
                        'title' => $sourceData['title'],
                        'source_type' => $sourceData['source_type'],
                        'url_type' => $sourceData['url_type'],
                        'url' => $sourceData['url'],
                        'status' => 'active'
                    ]);
                    
                    Log::info('Source créée avec succès:', [
                        'source_id' => $source->id,
                        'title' => $sourceData['title'],
                        'url' => $sourceData['url']
                    ]);
                    
                    $count++;
                } catch (\Exception $e) {
                    Log::error('Erreur lors de la création d\'une source:', [
                        'error' => $e->getMessage(),
                        'source_data' => $sourceData
                    ]);
                }
            }
            
            // Vider le cache
            Cache::flush();
            
            return response()->json([
                'success' => true,
                'count' => $count,
                'message' => "{$count} source(s) ajoutée(s) avec succès"
            ]);
        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'ajout en masse des sources:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'ajout des sources: ' . $e->getMessage()
            ], 500);
        }
    }
} 