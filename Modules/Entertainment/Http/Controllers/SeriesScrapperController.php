<?php

namespace Modules\Entertainment\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Entertainment\Services\SeriesScrapperService;
use Modules\Season\Models\Season;
use Modules\Episode\Models\Episode;
use Modules\Entertainment\Models\Source;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SeriesScrapperController extends Controller
{
    protected $seriesScrapperService;

    public function __construct(SeriesScrapperService $seriesScrapperService)
    {
        $this->seriesScrapperService = $seriesScrapperService;
    }

    /**
     * Extrait les données de série à partir d'une URL ou d'un contenu HTML
     */
    public function extractSeries(Request $request): JsonResponse
    {
        $request->validate([
            'input' => 'required|string',
            'site_type' => 'nullable|string|in:auto,wiflix,french-stream',
            'languages' => 'nullable|array',
            'languages.*' => 'string|in:VF,VOSTFR,VS,VO'
        ]);

        $input = $request->input('input');
        $siteType = $request->input('site_type', 'auto');
        $languages = $request->input('languages', ['VF']); // Par défaut VF

        $result = $this->seriesScrapperService->extractSeriesData($input, $siteType, $languages);

                return response()->json($result);
    }

    /**
     * Importe les épisodes extraits dans la saison spécifiée
     */
    public function importEpisodes(Request $request): JsonResponse
    {
        $request->validate([
            'season_id' => 'required|exists:seasons,id',
            'episodes' => 'required|array',
            'episodes.*.episode_number' => 'required|integer|min:1',
            'episodes.*.sources' => 'required|array|min:1',
            'episodes.*.sources.*.title' => 'required|string',
            'episodes.*.sources.*.url' => 'required|url',
            'episodes.*.sources.*.source_type' => 'required|string',
            'episodes.*.sources.*.url_type' => 'required|string',
            'add_to_existing' => 'nullable|boolean',
            'start_from_episode' => 'nullable|integer|min:1',
        ]);

        try {
            DB::beginTransaction();

            $season = Season::findOrFail($request->season_id);
            $addToExisting = $request->input('add_to_existing', false);
            $importedCount = 0;
            $skippedCount = 0;
            $addedSourcesCount = 0;

            foreach ($request->episodes as $episodeData) {
                $episodeNumber = $episodeData['episode_number'];
                
                // Vérifier si l'épisode existe déjà
                $existingEpisode = Episode::where('season_id', $season->id)
                    ->where('episode_number', $episodeNumber)
                    ->first();

                if ($existingEpisode && !$addToExisting) {
                    $skippedCount++;
                    continue; // Passer à l'épisode suivant si il existe déjà et qu'on ne veut pas ajouter aux existants
                }

                // Si l'épisode existe et qu'on veut ajouter aux existants
                if ($existingEpisode && $addToExisting) {
                    $episode = $existingEpisode;
                } else {
                    // Créer le nouvel épisode
                    $episode = Episode::create([
                        'entertainment_id' => $season->entertainment_id,
                        'season_id' => $season->id,
                        'episode_number' => $episodeNumber,
                        'name' => "Épisode {$episodeNumber}",
                        'status' => 1,
                        'access' => 'free', // Valeur par défaut
                        'duration' => '00:45:00', // Durée par défaut
                        'release_date' => now(),
                    ]);
                    $importedCount++;
                }

                // Ajouter les sources à l'épisode
                foreach ($episodeData['sources'] as $sourceData) {
                    // Vérifier si la source existe déjà pour éviter les doublons
                    $existingSource = Source::where('sourceable_type', 'Modules\Episode\Models\Episode')
                        ->where('sourceable_id', $episode->id)
                        ->where('url', $sourceData['url'])
                        ->first();
                    
                    if (!$existingSource) {
                        // Extraire le nom du domaine de l'URL pour le titre
                        $parsedUrl = parse_url($sourceData['url']);
                        $domain = $parsedUrl['host'] ?? '';
                        $domainParts = explode('.', $domain);
                        $extractedTitle = $domainParts[0] ?? 'source';
                        
                        Source::create([
                            'sourceable_type' => 'Modules\Episode\Models\Episode',
                            'sourceable_id' => $episode->id,
                            'title' => $extractedTitle,
                            'url' => $sourceData['url'],
                            'source_type' => 'both', // Cohérent avec les films
                            'url_type' => 'embed',
                            'status' => 'active',
                        ]);
                        $addedSourcesCount++;
                    }
                }
            }

            DB::commit();

            $message = "Importation terminée : ";
            if ($importedCount > 0) {
                $message .= "{$importedCount} épisode(s) créé(s)";
            }
            if ($addedSourcesCount > 0) {
                if ($importedCount > 0) $message .= ", ";
                $message .= "{$addedSourcesCount} source(s) ajoutée(s)";
            }
            if ($skippedCount > 0) {
                $message .= ", {$skippedCount} épisode(s) ignoré(s) (déjà existants)";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'imported_count' => $importedCount,
                'skipped_count' => $skippedCount,
                'added_sources_count' => $addedSourcesCount
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Erreur lors de l\'importation des épisodes: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'importation: ' . $e->getMessage()
            ], 500);
        }
    }
} 