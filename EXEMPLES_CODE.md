# 💻 Exemples de Code pour utiliser les APIs

## 🔌 **JavaScript/AJAX - Frontend**

### 1. Films populaires par genre
```javascript
// Fonction pour récupérer films par genre avec tri par vues
async function getPopularMoviesByGenre(genreId, year = null, minRating = null) {
    const params = new URLSearchParams({
        content_type: 'movie',
        genre_id: genreId,
        sort_by_views: 'desc',
        per_page: 20
    });
    
    if (year) params.append('release_year', year);
    if (minRating) params.append('min_imdb_rating', minRating);
    
    try {
        const response = await fetch(`/api/movie-list?${params}`);
        const data = await response.json();
        
        if (data.status) {
            return data.data;
        }
        throw new Error(data.message);
    } catch (error) {
        console.error('Erreur API:', error);
        return [];
    }
}

// Utilisation
getPopularMoviesByGenre(28, 2023, 7.0).then(movies => {
    console.log('Films d\'action 2023 populaires:', movies);
    displayMovies(movies);
});
```

### 2. Films par année avec note minimale
```javascript
// Récupérer les meilleurs films d'une année
async function getBestMoviesOfYear(year, minRating = 7.5) {
    const url = `/api/movie-list?content_type=movie&release_year=${year}&min_imdb_rating=${minRating}&sort_by_views=desc&per_page=15`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    return data.status ? data.data : [];
}

// Affichage dans une section
async function loadBestMovies2023() {
    const movies = await getBestMoviesOfYear(2023, 7.5);
    const container = document.getElementById('best-movies-2023');
    
    movies.forEach(movie => {
        const movieCard = createMovieCard(movie);
        container.appendChild(movieCard);
    });
}
```

### 3. Séries TV populaires
```javascript
// Récupérer séries populaires
function getPopularTVShows(genre = null, minRating = null) {
    return new Promise((resolve, reject) => {
        const params = {
            content_type: 'tvshow',
            sort_by_views: 'desc',
            per_page: 12
        };
        
        if (genre) params.genre_id = genre;
        if (minRating) params.min_imdb_rating = minRating;
        
        $.ajax({
            url: '/api/tvshow-list',
            method: 'GET',
            data: params,
            success: function(response) {
                if (response.status) {
                    resolve(response.data);
                } else {
                    reject(response.message);
                }
            },
            error: function(xhr, status, error) {
                reject(error);
            }
        });
    });
}
```

### 4. Pagination infinie
```javascript
class MovieLoader {
    constructor(containerId, apiEndpoint) {
        this.container = document.getElementById(containerId);
        this.apiEndpoint = apiEndpoint;
        this.currentPage = 1;
        this.loading = false;
        this.hasMore = true;
        this.filters = {};
    }
    
    setFilters(filters) {
        this.filters = filters;
        this.currentPage = 1;
        this.hasMore = true;
        this.container.innerHTML = '';
    }
    
    async loadMovies() {
        if (this.loading || !this.hasMore) return;
        
        this.loading = true;
        this.showLoader();
        
        const params = {
            ...this.filters,
            page: this.currentPage,
            per_page: 12
        };
        
        try {
            const response = await fetch(`${this.apiEndpoint}?${new URLSearchParams(params)}`);
            const data = await response.json();
            
            if (data.status && data.data.length > 0) {
                this.displayMovies(data.data);
                this.currentPage++;
                this.hasMore = data.data.length === params.per_page;
            } else {
                this.hasMore = false;
            }
        } catch (error) {
            console.error('Erreur chargement:', error);
        } finally {
            this.loading = false;
            this.hideLoader();
        }
    }
    
    displayMovies(movies) {
        movies.forEach(movie => {
            const movieElement = this.createMovieElement(movie);
            this.container.appendChild(movieElement);
        });
    }
    
    createMovieElement(movie) {
        const div = document.createElement('div');
        div.className = 'movie-card';
        div.innerHTML = `
            <img src="${movie.poster_url}" alt="${movie.name}">
            <h3>${movie.name}</h3>
            <p>IMDB: ${movie.imdb_rating}/10</p>
            <p>Vues: ${movie.views_count || 0}</p>
        `;
        return div;
    }
}

// Utilisation
const movieLoader = new MovieLoader('movies-container', '/api/movie-list');

// Charger films d'action populaires 2023
movieLoader.setFilters({
    content_type: 'movie',
    genre_id: 28,
    release_year: 2023,
    sort_by_views: 'desc'
});
movieLoader.loadMovies();
```

---

## 🐘 **PHP/Laravel - Backend**

### 1. Service pour les APIs films
```php
<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class MovieAPIService
{
    private string $baseUrl;
    
    public function __construct()
    {
        $this->baseUrl = config('app.url') . '/api';
    }
    
    /**
     * Récupérer films populaires par genre
     */
    public function getPopularMoviesByGenre(
        int $genreId, 
        ?int $year = null, 
        ?float $minRating = null, 
        int $perPage = 20
    ): array {
        $params = [
            'content_type' => 'movie',
            'genre_id' => $genreId,
            'sort_by_views' => 'desc',
            'per_page' => $perPage
        ];
        
        if ($year) $params['release_year'] = $year;
        if ($minRating) $params['min_imdb_rating'] = $minRating;
        
        $response = Http::get($this->baseUrl . '/movie-list', $params);
        
        if ($response->successful()) {
            $data = $response->json();
            return $data['status'] ? $data['data'] : [];
        }
        
        return [];
    }
    
    /**
     * Films les mieux notés d'une année
     */
    public function getBestMoviesOfYear(int $year, float $minRating = 7.5): array
    {
        return $this->getPopularMoviesByGenre(null, $year, $minRating);
    }
    
    /**
     * Top films par vues
     */
    public function getTopMoviesByViews(int $limit = 10): array
    {
        $params = [
            'content_type' => 'movie',
            'sort_by_views' => 'desc',
            'per_page' => $limit
        ];
        
        $response = Http::get($this->baseUrl . '/movie-list', $params);
        
        if ($response->successful()) {
            $data = $response->json();
            return $data['status'] ? $data['data'] : [];
        }
        
        return [];
    }
}
```

### 2. Contrôleur pour pages frontend
```php
<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Services\MovieAPIService;
use Illuminate\Http\Request;

class MoviePageController extends Controller
{
    private MovieAPIService $movieService;
    
    public function __construct(MovieAPIService $movieService)
    {
        $this->movieService = $movieService;
    }
    
    /**
     * Page d'accueil avec différentes sections
     */
    public function homepage()
    {
        $data = [
            // Top 5 films populaires pour le hero
            'hero_movies' => $this->movieService->getTopMoviesByViews(5),
            
            // Films 2023 bien notés
            'new_movies_2023' => $this->movieService->getBestMoviesOfYear(2023, 7.0),
            
            // Action populaires
            'action_movies' => $this->movieService->getPopularMoviesByGenre(28, null, 7.0, 10),
            
            // Comédies populaires
            'comedy_movies' => $this->movieService->getPopularMoviesByGenre(35, null, 7.0, 10),
        ];
        
        return view('frontend.homepage', $data);
    }
    
    /**
     * Page genre spécifique
     */
    public function genrePage(Request $request, int $genreId)
    {
        $year = $request->get('year');
        $minRating = $request->get('min_rating', 6.0);
        $perPage = $request->get('per_page', 24);
        
        $movies = $this->movieService->getPopularMoviesByGenre(
            $genreId, 
            $year, 
            $minRating, 
            $perPage
        );
        
        return view('frontend.genre', [
            'movies' => $movies,
            'genre_id' => $genreId,
            'current_year' => $year,
            'min_rating' => $minRating
        ]);
    }
    
    /**
     * API pour filtres dynamiques
     */
    public function filterMovies(Request $request)
    {
        $genreId = $request->get('genre_id');
        $year = $request->get('year');
        $minRating = $request->get('min_rating');
        $sortBy = $request->get('sort_by', 'views');
        
        $movies = [];
        
        if ($sortBy === 'views') {
            $movies = $this->movieService->getPopularMoviesByGenre(
                $genreId, $year, $minRating
            );
        }
        
        return response()->json([
            'success' => true,
            'movies' => $movies,
            'count' => count($movies)
        ]);
    }
}
```

### 3. Blade Template avec AJAX
```blade
{{-- resources/views/frontend/homepage.blade.php --}}
@extends('layouts.app')

@section('content')
<div class="homepage">
    {{-- Hero Section --}}
    <section class="hero">
        <div id="hero-movies" class="hero-slider">
            @foreach($hero_movies as $movie)
                <div class="hero-slide" data-movie-id="{{ $movie['id'] }}">
                    <img src="{{ $movie['poster_url'] }}" alt="{{ $movie['name'] }}">
                    <div class="hero-content">
                        <h1>{{ $movie['name'] }}</h1>
                        <p>IMDB: {{ $movie['imdb_rating'] }}/10</p>
                        <p>{{ $movie['views_count'] ?? 0 }} vues</p>
                    </div>
                </div>
            @endforeach
        </div>
    </section>
    
    {{-- Films récents 2023 --}}
    <section class="movies-section">
        <h2>Nouveautés 2023</h2>
        <div class="movies-grid" id="new-movies-2023">
            @foreach($new_movies_2023 as $movie)
                @include('frontend.partials.movie-card', ['movie' => $movie])
            @endforeach
        </div>
        <button onclick="loadMoreMovies('2023')" class="load-more-btn">
            Voir plus de films 2023
        </button>
    </section>
    
    {{-- Films d'action --}}
    <section class="movies-section">
        <h2>Action Populaires</h2>
        <div class="movies-grid" id="action-movies">
            @foreach($action_movies as $movie)
                @include('frontend.partials.movie-card', ['movie' => $movie])
            @endforeach
        </div>
    </section>
</div>

<script>
function loadMoreMovies(year) {
    fetch(`/api/movie-list?content_type=movie&release_year=${year}&sort_by_views=desc&per_page=12`)
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            const container = document.getElementById('new-movies-2023');
            data.data.forEach(movie => {
                const movieCard = createMovieCard(movie);
                container.appendChild(movieCard);
            });
        }
    })
    .catch(error => console.error('Erreur:', error));
}

function createMovieCard(movie) {
    const div = document.createElement('div');
    div.className = 'movie-card';
    div.innerHTML = `
        <a href="/movie-details/${movie.id}">
            <img src="${movie.poster_url}" alt="${movie.name}">
            <h3>${movie.name}</h3>
            <p>IMDB: ${movie.imdb_rating}/10</p>
        </a>
    `;
    return div;
}
</script>
@endsection
```

---

## ⚡ **Optimisations et bonnes pratiques**

### 1. Cache côté client
```javascript
class CachedMovieAPI {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 30 * 60 * 1000; // 30 minutes
    }
    
    async getMovies(params) {
        const cacheKey = JSON.stringify(params);
        const cached = this.cache.get(cacheKey);
        
        if (cached && (Date.now() - cached.timestamp < this.cacheTimeout)) {
            return cached.data;
        }
        
        const data = await this.fetchMovies(params);
        
        this.cache.set(cacheKey, {
            data: data,
            timestamp: Date.now()
        });
        
        return data;
    }
    
    async fetchMovies(params) {
        const response = await fetch(`/api/movie-list?${new URLSearchParams(params)}`);
        const result = await response.json();
        return result.status ? result.data : [];
    }
}
```

### 2. Gestion d'erreurs
```javascript
async function safeAPICall(url, params = {}) {
    try {
        const response = await fetch(`${url}?${new URLSearchParams(params)}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (!data.status) {
            throw new Error(data.message || 'Erreur API');
        }
        
        return data.data;
    } catch (error) {
        console.error('Erreur API:', error);
        
        // Afficher message d'erreur à l'utilisateur
        showErrorMessage('Erreur de chargement des films');
        
        return [];
    }
}
```

Ces exemples vous donnent une base solide pour intégrer vos nouvelles APIs dans votre frontend ! 🚀 