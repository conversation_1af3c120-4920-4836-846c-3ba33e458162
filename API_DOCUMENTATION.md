# Documentation des APIs Films et Séries TV

## 🎬 API Films - movie-list

### Endpoint
```
GET /api/movie-list
```

### Paramètres disponibles

| Paramètre | Type | Description | Exemple |
|-----------|------|-------------|---------|
| `genre_id` | integer | ID du genre pour filtrer | `5` |
| `release_year` | integer | Année de sortie | `2023` |
| `min_imdb_rating` | float | Note IMDB minimale | `7.5` |
| `sort_by_views` | string | Tri par vues (desc) | `desc` |
| `per_page` | integer | Nombre d'éléments par page | `10` |
| `search` | string | Recherche par nom | `Avengers` |
| `language` | string | Filtrer par langue | `fr` |
| `actor_id` | integer | Filtrer par acteur | `123` |

### Exemple d'appel
```
GET /api/movie-list?genre_id=5&release_year=2023&min_imdb_rating=7.5&sort_by_views=desc&per_page=10
```

### Réponse
```json
{
    "status": true,
    "data": [
        {
            "id": 1,
            "name": "Film Example",
            "imdb_rating": 8.2,
            "release_date": "2023-05-15",
            "type": "movie",
            "genres": [...],
            "views_count": 1250,
            // ... autres champs
        }
    ],
    "message": "Liste des films",
    "meta": {
        "current_page": 1,
        "per_page": 10,
        "total": 50
    }
}
```

## 📺 API Séries TV - tvshow-list

### Endpoint
```
GET /api/tvshow-list
```

### Paramètres disponibles

| Paramètre | Type | Description | Exemple |
|-----------|------|-------------|---------|
| `genre_id` | integer | ID du genre pour filtrer | `5` |
| `release_year` | integer | Année de sortie | `2023` |
| `min_imdb_rating` | float | Note IMDB minimale | `7.5` |
| `sort_by_views` | string | Tri par vues (desc) | `desc` |
| `per_page` | integer | Nombre d'éléments par page | `10` |
| `search` | string | Recherche par nom ou genre | `Breaking Bad` |

### Exemple d'appel
```
GET /api/tvshow-list?genre_id=5&release_year=2023&min_imdb_rating=7.5&sort_by_views=desc&per_page=10
```

### Réponse
```json
{
    "status": true,
    "data": [
        {
            "id": 1,
            "name": "Série Example",
            "imdb_rating": 9.1,
            "release_date": "2023-03-20",
            "type": "tvshow",
            "genres": [...],
            "seasons": [...],
            "episodes": [...],
            "views_count": 3450,
            // ... autres champs
        }
    ],
    "message": "Liste des séries TV"
}
```

## 🔧 Fonctionnalités

### 1. Endpoints spécialisés
- `/api/movie-list` : Exclusivement pour les **films**
- `/api/tvshow-list` : Exclusivement pour les **séries TV**

### 2. Filtrage par genre
- `genre_id=5` : Filtre par ID de genre spécifique

### 3. Filtrage par année
- `release_year=2023` : Affiche uniquement le contenu sorti en 2023

### 4. Filtrage par note IMDB
- `min_imdb_rating=7.5` : Affiche uniquement le contenu avec une note ≥ 7.5

### 5. Tri par popularité
- `sort_by_views=desc` : Trie par nombre de vues décroissant
- Si non spécifié : Tri par date de création décroissante

## 📊 Optimisations

### Performance
- Utilisation de jointures optimisées pour le tri par vues
- Eager loading des relations nécessaires
- Mise en cache des requêtes fréquentes

### Base de données
- Indexation recommandée sur les colonnes :
  - `entertainments.IMDb_rating`
  - `entertainments.release_date`
  - `entertainment_views.entertainment_id`
  - `entertainment_gener_mappings.genre_id`

## 🎯 Cas d'usage

### Films populaires de 2023 avec bonne note
```
GET /api/movie-list?release_year=2023&min_imdb_rating=7.0&sort_by_views=desc&per_page=20
```

### Séries d'action les plus regardées
```
GET /api/tvshow-list?genre_id=28&sort_by_views=desc&per_page=15
```

### Nouveaux films de qualité
```
GET /api/movie-list?min_imdb_rating=8.0&sort_by_views=desc&per_page=10
``` 