<?php

namespace App\Notifications;

use App\Broadcasting\FcmChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomMessageNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $title;
    public $message;
    public $sendPush;
    public $sendEmail;
    public $largeIcon;
    public $bigImage;
    public $actionUrl;
    public $isBroadcast;

    /**
     * Create a new notification instance.
     */
    public function __construct($data)
    {
        $this->title = $data['title'];
        $this->message = $data['message'];
        $this->sendPush = $data['send_push'] ?? false;
        $this->sendEmail = $data['send_email'] ?? false;
        $this->largeIcon = $data['large_icon'] ?? null;
        $this->bigImage = $data['big_image'] ?? null;
        $this->actionUrl = $data['action_url'] ?? null;
        $this->isBroadcast = $data['is_broadcast'] ?? false;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable)
    {
        $channels = ['database']; // Toujours enregistrer en base

        if ($this->sendPush) {
            $channels[] = FcmChannel::class;
        }

        if ($this->sendEmail) {
            $channels[] = 'mail';
        }

        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        $mail = (new MailMessage)
                    ->subject($this->title)
                    ->greeting('Bonjour ' . $notifiable->first_name . ',')
                    ->line($this->message);

        // Ajouter le lien d'action si présent
        if ($this->actionUrl) {
            $mail->action('Voir plus', $this->actionUrl);
        }

        return $mail->line('Merci d\'utiliser notre application !')
                   ->salutation('L\'équipe ' . config('app.name'));
    }

    /**
     * Get the FCM representation of the notification.
     */
    public function toFcm($notifiable)
    {
        // Construire les données de notification
        $notificationData = [
            'title' => $this->title,
            'body' => $this->message,
        ];

        // Ajouter l'icône large si présente
        if ($this->largeIcon) {
            $notificationData['icon'] = $this->largeIcon;
        }

        // Ajouter la grande image si présente
        if ($this->bigImage) {
            $notificationData['image'] = $this->bigImage;
        }

        // Construire les données personnalisées
        $customData = [
            'type' => 'custom_message',
            'title' => $this->title,
            'message' => $this->message,
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
        ];

        // Ajouter l'URL d'action si présente
        if ($this->actionUrl) {
            $customData['action_url'] = $this->actionUrl;
            $customData['click_action'] = $this->actionUrl;
        }

        // Configuration FCM de base
        $fcmConfig = [
            'notification' => $notificationData,
            'data' => $customData,
            'android' => [
                'notification' => [
                    'icon' => $this->largeIcon ?? 'ic_notification',
                    'color' => '#FF6B35',
                    'sound' => 'default',
                    'channel_id' => 'voir_films_hd_channel',
                    'click_action' => $this->actionUrl ?? 'FLUTTER_NOTIFICATION_CLICK'
                ]
            ],
            'apns' => [
                'payload' => [
                    'aps' => [
                        'sound' => 'default',
                        'badge' => 1,
                        'category' => 'voir_films_hd'
                    ]
                ]
            ]
        ];

        // Si c'est une notification broadcast, utiliser le topic voir_films_hd
        if ($this->isBroadcast) {
            $fcmConfig['to'] = '/topics/voir_films_hd';
        } else {
            // Sinon, envoyer à l'utilisateur spécifique
            $fcmConfig['to'] = 'user_' . $notifiable->id;
        }

        // Utiliser la fonction helper fcm()
        return fcm($fcmConfig);
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable)
    {
        return [
            'title' => $this->title,
            'message' => $this->message,
            'send_push' => $this->sendPush,
            'send_email' => $this->sendEmail,
            'large_icon' => $this->largeIcon,
            'big_image' => $this->bigImage,
            'action_url' => $this->actionUrl,
            'is_broadcast' => $this->isBroadcast,
            'type' => 'custom_message'
        ];
    }
} 