<?php

namespace App\Http\Controllers\Backend\API;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Modules\Currency\Models\Currency;
use Modules\Tax\Models\Tax;
use App\Models\MobileSetting;
use Modules\Subscriptions\Models\Subscription;
use App\Models\Device;

class SettingController extends Controller
{
    public function appConfiguraton(Request $request)
    {
        $settings = Setting::all()->pluck('val', 'name');

        $response = [];

        // Add default ads settings if they don't exist
        $defaultAdsSettings = [
            // Ads Types
            'interstitial_ads_type' => 'disable',
            'banner_ads_type' => 'disable',
            'reward_ads_type' => 'disable',
            'embed_ads_type' => 'disable',
            'player_ads_type' => 'disable',
            'reward_ads_type_for_embed' => 'disable',
            'reward_ads_type_for_player' => 'disable',
            // Ads Configuration
            'click_between_interstitial_ad' => '5',
            'force_ads_for_download' => '0',
            'show_scrolling_ads' => '0',
            'scrolling_ads_pixel' => '300',
            'show_interval_ads_in_video' => '0',
            'enabled_vod_download' => '0',
            // Unity Ads IDs
            'unity_game_id' => '',
            'unity_interstitial_id' => '',
            'unity_banner_id' => '',
            'unity_reward_id' => '',
            // AdMob Ads IDs
            'admob_interstitial_id' => '',
            'admob_banner_id' => '',
            'admob_reward_id' => '',
            'admob_openad_id' => ''
        ];

        foreach ($defaultAdsSettings as $key => $defaultValue) {
            if (!$settings->has($key)) {
                $settings[$key] = $defaultValue;
            }
        }

        // Define the specific names you want to include
        $specificNames = [
            'server_hls_url',
            'update_message',
            'release_notes',
            'release_code',
            'forcing_update',
            'android_url',
            'ios_url',
            'frembed_api_url',
            'telegram_url',
            'inapp_message',
            'inapp_link',
            'inapp_button_text',
            'apk_downloader_website',
            'google_play_by_pass_1',
            'telegram_group_url',
            'alerte_sms_google_play',
            'frenchtv_url',
            
            'app_name', 'footer_text', 'primary','razorpay_secretkey', 'razorpay_publickey', 'stripe_secretkey', 'stripe_publickey', 'paystack_secretkey', 'paystack_publickey', 'paypal_secretkey', 'paypal_clientid', 'flutterwave_secretkey', 'flutterwave_publickey', 'onesignal_app_id', 'onesignal_rest_api_key', 'onesignal_channel_id', 'google_maps_key', 'helpline_number', 'copyright', 'inquriy_email', 'site_description', 'customer_app_play_store', 'customer_app_app_store', 'isForceUpdate', 'version_code','cinet_siteid','cinet_api_key','cinet_Secret_key','sadad_Sadadkey','sadad_id_key','sadad_Domain','airtel_money_secretkey','airtel_money_client_id','phonepe_App_id','phonepe_Merchant_id','phonepe_salt_key','phonepe_salt_index','midtrans_client_id', 'interstitial_ads_type', 'banner_ads_type', 'reward_ads_type', 'embed_ads_type', 'player_ads_type', 'reward_ads_type_for_embed', 'reward_ads_type_for_player', 'click_between_interstitial_ad', 'force_ads_for_download', 'show_scrolling_ads', 'scrolling_ads_pixel', 'show_interval_ads_in_video', 'unity_game_id', 'unity_interstitial_id', 'unity_banner_id', 'unity_reward_id', 'admob_interstitial_id', 'admob_banner_id', 'admob_reward_id', 'admob_openad_id'];
        foreach ($settings as $name => $value) {
            if (in_array($name, $specificNames)) {
                if (strpos($name, 'onesignal_') === 0 && $request->is_authenticated == 1) {
                    $nestedKey = 'onesignal_customer_app';
                    $nestedName = str_replace('', 'onesignal_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                } elseif (strpos($name, 'razorpay_') === 0 && $request->is_authenticated == 1 && $settings['razor_payment_method'] == 1) {
                    $nestedKey = 'razor_pay';

                    $nestedName = str_replace('', 'razorpay_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                } elseif (strpos($name, 'stripe_') === 0 && $request->is_authenticated == 1 && $settings['str_payment_method'] == 1) {
                    $nestedKey = 'stripe_pay';
                    $nestedName = str_replace('', 'stripe_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                } elseif (strpos($name, 'paystack_') === 0 && $request->is_authenticated == 1 && $settings['paystack_payment_method'] == 1) {
                    $nestedKey = 'paystack_pay';
                    $nestedName = str_replace('', 'paystack_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                } elseif (strpos($name, 'paypal_') === 0 && $request->is_authenticated == 1 && $settings['paypal_payment_method'] == 1) {
                    $nestedKey = 'paypal_pay';
                    $nestedName = str_replace('', 'paypal_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                } elseif (strpos($name, 'flutterwave_') === 0 && $request->is_authenticated == 1 && $settings['flutterwave_payment_method'] == 1) {
                    $nestedKey = 'flutterwave_pay';
                    $nestedName = str_replace('', 'flutterwave_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;

                }elseif (strpos($name, 'cinet_') === 0 && $request->is_authenticated == 1 && $settings['cinet_payment_method'] == 1) {
                    $nestedKey = 'cinet_pay';
                    $nestedName = str_replace('', 'cinet_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                }elseif (strpos($name, 'sadad_') === 0 && $request->is_authenticated == 1 && $settings['sadad_payment_method'] == 1) {
                    $nestedKey = 'sadad_pay';
                    $nestedName = str_replace('', 'sadad_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                }elseif (strpos($name, 'airtel_') === 0 && $request->is_authenticated == 1 && $settings['airtel_payment_method'] == 1) {
                    $nestedKey = 'airtel_pay';
                    $nestedName = str_replace('', 'airtel_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                }elseif (strpos($name, 'phonepe_') === 0 && $request->is_authenticated == 1 && $settings['phonepe_payment_method'] == 1) {
                    $nestedKey = 'phonepe_pay';
                    $nestedName = str_replace('', 'phonepe_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                }elseif (strpos($name, 'midtrans_') === 0 && $request->is_authenticated == 1 && $settings['midtrans_payment_method'] == 1) {
                    $nestedKey = 'midtrans_pay';
                    $nestedName = str_replace('', 'midtrans_', $name);
                    if (!isset($response[$nestedKey])) {
                        $response[$nestedKey] = [];
                    }
                    $response[$nestedKey][$nestedName] = $value;
                } elseif (strpos($name, '_ads_type') !== false ||
                          strpos($name, 'click_between_interstitial_ad') !== false ||
                          strpos($name, 'force_ads_for_download') !== false ||
                          strpos($name, 'show_scrolling_ads') !== false ||
                          strpos($name, 'scrolling_ads_pixel') !== false ||
                          strpos($name, 'show_interval_ads_in_video') !== false ||
                          strpos($name, 'enabled_vod_download') !== false ||
                          strpos($name, 'unity_') === 0 ||
                          strpos($name, 'admob_') === 0) {
                    // Handle ads settings
                    if (!isset($response['ads_settings'])) {
                        $response['ads_settings'] = [];
                    }
                    $response['ads_settings'][$name] = $value ?: (strpos($name, '_ads_type') !== false ? 'disable' : $value);
                }

                // Only add to general response if not already handled by specific logic above
                if (strpos($name, 'onesignal_') !== 0 &&
                    strpos($name, 'stripe_') !== 0 &&
                    strpos($name, 'razorpay_') !== 0 &&
                    strpos($name, 'paystack_') !== 0 &&
                    strpos($name, 'paypal_') !== 0 &&
                    strpos($name, 'flutterwave_') !== 0 &&
                    strpos($name, 'cinet_') !== 0 &&
                    strpos($name, 'sadad_') !== 0 &&
                    strpos($name, 'airtel_') !== 0 &&
                    strpos($name, 'phonepe_') !== 0 &&
                    strpos($name, 'midtrans_') !== 0 &&
                    strpos($name, '_ads_type') === false &&
                    strpos($name, 'click_between_interstitial_ad') === false &&
                    strpos($name, 'force_ads_for_download') === false &&
                    strpos($name, 'show_scrolling_ads') === false &&
                    strpos($name, 'scrolling_ads_pixel') === false &&
                    strpos($name, 'show_interval_ads_in_video') === false &&
                    strpos($name, 'enabled_vod_download') === false &&
                    strpos($name, 'unity_') !== 0 &&
                    strpos($name, 'admob_') !== 0) {
                    $response[$name] = $value;
                }
            }
        }
        // Fetch currency data
        $currency = Currency::where('is_primary',1)->first();

        $currencyData = null;
        if ($currency) {

            $currencyData = [
                'currency_name' => $currency->currency_name,
                'currency_symbol' => $currency->currency_symbol,
                'currency_code' => $currency->currency_code,
                'currency_position' => $currency->currency_position,
                'no_of_decimal' => $currency->no_of_decimal,
                'thousand_separator' => $currency->thousand_separator,
                'decimal_separator' => $currency->decimal_separator,
            ];
        }

        $taxes = Tax::active()->get();
        $ads_val= MobileSetting::where('slug', 'banner')->first();
        $rate_our_app= MobileSetting::where('slug', 'rate-our-app')->first();
        $ads_val= MobileSetting::where('slug', 'banner')->first();
        $continue_watch= MobileSetting::where('slug', 'continue-watching')->first();
       

      
        if (isset($settings['force_update']) && isset($settings['version_code'])) {
            $response['isForceUpdate'] = intval($settings['force_update']);

            $response['version_code'] = intval($settings['version_code']);
        } else {
            $response['isForceUpdate'] = 0;

            $response['version_code'] = 0;
        }
        if (isset($settings['click_between_interstitial_ad'])) {
            $response['click_between_interstitial_ad'] = intval($settings['click_between_interstitial_ad']);
        } else {
            $response['click_between_interstitial_ad'] = 0;
        }

        // Conversion numérique pour unity_game_id uniquement
        $response['unity_game_id'] = isset($settings['unity_game_id']) 
            ? intval($settings['unity_game_id']) 
            : 0;

        // Les autres IDs Unity restent en string
        $response['unity_interstitial_id'] = $settings['unity_interstitial_id'] ?? '';
        $response['unity_banner_id'] = $settings['unity_banner_id'] ?? '';
        $response['unity_reward_id'] = $settings['unity_reward_id'] ?? '';

        $response['release_notes'] = $settings['release_notes'] ?? '';
        $response['release_code'] = $settings['release_code'] ?? '';
        $response['force_update'] = intval($settings['forcing_update'] ?? 0);
        $response['android_url'] = $settings['android_url'] ?? '';
        $response['telegram_url'] = $settings['telegram_url'] ?? '';
        $response['ios_url'] = $settings['ios_url'] ?? '';
        $response['frembed_api_url'] = $settings['frembed_api_url'] ?? '';
        $response['force_ads_for_download'] = intval($settings['force_ads_for_download'] ?? 0);
        $response['show_scrolling_ads'] = intval($settings['show_scrolling_ads'] ?? 0);
        $response['scrolling_ads_pixel'] = intval($settings['scrolling_ads_pixel'] ?? 0);
        $response['show_interval_ads_in_video'] = intval($settings['show_interval_ads_in_video'] ?? 0);
        $response['inapp_message'] = $settings['inapp_message'] ?? '';
        $response['inapp_title'] = $settings['inapp_title'] ?? '';
        $response['inapp_link'] = $settings['inapp_link'] ?? '';
        $response['apk_downloader_website'] = $settings['apk_downloader_website'] ?? '';
        $response['inapp_button_text'] = $settings['inapp_button_text'] ?? '';
        $response['google_play_by_pass_1'] = intval($settings['google_play_by_pass_1'] ?? 0);
        $response['telegram_group_url'] = $settings['telegram_group_url'] ?? '';
        $response['alerte_sms_google_play'] = $settings['alerte_sms_google_play'] ?? '';
        $response['enabled_vod_download'] = intval($settings['enabled_vod_download'] ?? 0);
        $response['frenchtv_url'] = $settings['frenchtv_url'] ?? '';

        if(!empty($request->user_id)){
            $getDeviceTypeData = Subscription::checkPlanSupportDevice($request->user_id);
            $deviceTypeResponse = json_decode($getDeviceTypeData->getContent(), true);
        }

        $response['tax'] = $taxes;

        $response['currency'] = $currencyData;
        $response['google_login_status'] = 'false';
        $response['apple_login_status'] = 'false';
        $response['otp_login_status'] = 'false';
        $response['site_description'] = $settings['site_description'] ?? null;
       
        // Add locale language to the response
        $response['application_language'] = app()->getLocale();
        $response['status'] = true;
        $response['enable_movie'] = isset($settings['movie']) ? intval($settings['movie']) : 0;
        $response['enable_livetv'] = isset($settings['livetv']) ? intval($settings['livetv']) : 0;
        $response['enable_tvshow'] = isset($settings['tvshow']) ? intval($settings['tvshow']) : 0;
        $response['enable_video'] = isset($settings['video']) ? intval($settings['video']) : 0;
        $response['enable_ads'] = isset($ads_val->value) ? (int) $ads_val->value : 0;
        $response['continue_watch'] = isset($continue_watch->value) ? (int) $continue_watch->value : 0;
        $response['enable_rate_us'] = isset($rate_our_app->value) ? (int) $rate_our_app->value : 0;
        $response['enable_in_app'] = isset($settings['iap_payment_method']) ? intval($settings['iap_payment_method']) : 0;
        $response['entitlement_id'] = isset($settings['entertainment_id']) ? $settings['entertainment_id'] : null;
        $response['apple_api_key'] = isset($settings['apple_api_key']) ? $settings['apple_api_key'] : null;
        $response['google_api_key'] = isset($settings['google_api_key']) ? $settings['google_api_key'] : null;
        $response['is_login'] = 0;


        if ($request->has('device_id') && $request->device_id != null && $request->has('user_id') && $request->user_id) {
            $device = Device::where('user_id', $request->user_id)
                ->where('device_id', $request->device_id)
                ->first();

            $response['is_login'] = $device ? 1 : 0;
        }
        if(!empty($request->user_id)){
            $response['is_device_supported'] = $deviceTypeResponse['isDeviceSupported'];
        }
        return response()->json($response);
    }

    public function Configuraton(Request $request)
    {
        $googleMeetSettings = Setting::whereIn('name', ['google_meet_method', 'google_clientid', 'google_secret_key'])
            ->pluck('val', 'name');
        $settings = $googleMeetSettings->toArray();
        return $settings;
    }
}
