<?php

namespace App\Http\Controllers\Backend\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\MobileSetting;
use Modules\Entertainment\Models\Entertainment;
use Modules\Banner\Models\Banner;
use Modules\Entertainment\Models\ContinueWatch;
use Modules\Banner\Transformers\SliderResource;
use Modules\Entertainment\Transformers\ContinueWatchResource;
use Modules\LiveTV\Models\LiveTvChannel;
use Modules\LiveTV\Transformers\LiveTvChannelResource;
use Modules\CastCrew\Models\CastCrew;
use Modules\CastCrew\Transformers\CastCrewListResource;
use Modules\Genres\Transformers\GenresResource;
use Modules\Genres\Models\Genres;
use Modules\Video\Models\Video;
use App\Services\RecommendationService;
use Modules\Entertainment\Transformers\MoviesResource;
use Modules\Entertainment\Transformers\CommanResource;
use Modules\Constant\Models\Constant;
use Modules\Video\Transformers\VideoResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;


class DashboardController extends Controller
{
    protected $recommendationService;
    public function __construct(RecommendationService $recommendationService)
    {
        $this->recommendationService = $recommendationService;

    }
    public function DashboardDetail(Request $request){
        // Créer une clé de cache unique basée sur l'utilisateur
        $cacheKey = 'dashboard_detail_' . ($request->user_id ?? 'guest') . '_' . ($request->profile_id ?? 'default');
        
        // Récupérer les données depuis le cache ou les générer
        return Cache::remember($cacheKey, 1800, function () use ($request) {
            $user_id = !empty($request->user_id) ? $request->user_id : null;
            $continueWatch = [];

            // Optimisation: Utiliser select() pour limiter les colonnes récupérées
            if($request->has('user_id')){
                $continueWatchList = ContinueWatch::where('user_id', $user_id)
                    ->where('profile_id', $request->profile_id)
                    ->select(['id', 'user_id', 'profile_id', 'entertainment_id', 'watched_time', 'entertainment_type', 'total_watched_time', 'episode_id'])
                    ->get();
                $continueWatch = ContinueWatchResource::collection($continueWatchList);
            }

            $isBanner = MobileSetting::getValueBySlug('banner');
            // Correction: utiliser les colonnes qui existent réellement dans la table banners
            $sliderList = $isBanner
                ? Banner::where('status', 1)
                    ->select(['id', 'title', 'file_url', 'poster_url', 'type', 'type_id', 'type_name', 'status'])
                    ->get()
                : collect();

            $sliders = SliderResource::collection(
                $sliderList->map(fn($slider) => new SliderResource($slider, $user_id))
            );

            $topMovieIds = MobileSetting::getValueBySlug('top-10');
            // Optimisation: Ajouter select() et limiter les jointures
            $topMovies = !empty($topMovieIds) 
                ? Entertainment::whereIn('id', json_decode($topMovieIds, true))
                    ->with(['entertainmentGenerMappings:id,entertainment_id,genre_id', 'entertainmentGenerMappings.genre:id,name'])
                    ->where('status', 1)
                    ->whereDate('release_date', '<=', Carbon::now())
                    ->select(['id', 'name', 'type', 'movie_access', 'plan_id', 'description', 'trailer_url_type', 
                            'release_date', 'is_restricted', 'language', 'IMDb_rating', 'content_rating', 
                            'duration', 'trailer_url', 'video_upload_type', 'video_url_input', 'poster_url', 'thumbnail_url'])
                    ->get() 
                : collect();
            $top_10 = CommanResource::collection($topMovies)->toArray(request());

            $responseData = [
                'slider' => $sliders,
                'continue_watch' => $continueWatch,
                'top_10' => $top_10
            ];

            return response()->json([
                'status' => true,
                'data' => $responseData,
                'message' => __('messages.dashboard_detail'),
            ], 200);
        });
    }

public function DashboardDetailData(Request $request){
    // Créer une clé de cache unique basée sur l'utilisateur et le profil
    $cacheKey = 'dashboard_detail_data_' . ($request->user_id ?? 'guest') . '_' . ($request->profile_id ?? 'default');
    
    try {
        // Essayer de récupérer depuis le cache avec gestion d'erreur
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            return $cachedData;
        }
    } catch (\Exception $e) {
        // Log l'erreur de cache mais continuer sans cache
        \Log::warning('Cache read error in DashboardDetailData: ' . $e->getMessage());
    }
    
    // Générer les données directement sans cache en cas de problème
    $user_id = !empty($request->user_id) ? $request->user_id : null;
    
    // Initialiser toutes les variables comme collections vides par défaut
    $likedMovies = collect();
    $viewedMovies = collect();
    $favorite_gener = collect();
    $FavoriteGener = [];
    $favorite_personality = collect();
    $trendingMovies = collect();
    $Lastwatchrecommendation = collect();
    $continueWatch = collect();
    $popular_language = collect();
    $top_channel = collect();
    $personality = [];
    $free_movie = collect();
    $popular_videos = collect();

    if($request->has('user_id')){
        // Optimisation: Sélectionner uniquement les colonnes nécessaires
        $continueWatchList = ContinueWatch::where('user_id', $user_id)
            ->where('profile_id', $request->profile_id)
            ->select(['id', 'user_id', 'profile_id', 'entertainment_id', 'watched_time', 'entertainment_type', 'total_watched_time', 'episode_id'])
            ->get();
        $continueWatch = ContinueWatchResource::collection($continueWatchList);
    }

    // Sections ACTIVÉES - récupérer les données
    $latest_movie = $this->getLatestMovies();
    $popular_movie = $this->getPopularMovies();
    $genres = $this->getGenres();
    $popular_tvshow = $this->getPopularTvShows();
    
    // Sections DÉSACTIVÉES - renvoyer des collections vides
    // $popular_language = $this->getPopularLanguages();
    // $top_channel = $this->getTopChannels();
    // $personality = $this->getPersonalities();
    // $free_movie = $this->getFreeMovies();
    // $popular_videos = $this->getPopularVideos();
    
    // Désactiver aussi la section trending movies
    $tranding_movie = collect();

    // Conserver la structure JSON intacte
    $responseData = [
        'latest_movie' => $latest_movie,
        'popular_language' => $popular_language, // Collection vide
        'popular_movie' => $popular_movie,
        'top_channel' => $top_channel, // Collection vide
        'personality' => $personality, // Tableau vide
        'tranding_movie' => $tranding_movie, // Collection vide
        'free_movie' => $free_movie, // Collection vide
        'genres' => $genres,
        'popular_tvshow' => $popular_tvshow,
        'popular_videos' => $popular_videos, // Collection vide
        'likedMovies' => $likedMovies, // Collection vide
        'viewedMovies' => $viewedMovies, // Collection vide
        'trendingMovies' => $trendingMovies, // Collection vide
        'favorite_gener' => $FavoriteGener, // Tableau vide
        'favorite_personality' => $favorite_personality, // Collection vide
        'base_on_last_watch' => $Lastwatchrecommendation, // Collection vide
    ];

    $response = response()->json([
        'status' => true,
        'data' => $responseData,
        'message' => __('messages.dashboard_detail'),
    ], 200);

    // Essayer de mettre en cache, mais ne pas échouer si ça ne marche pas
    try {
        Cache::put($cacheKey, $response, 1800); // 30 minutes
    } catch (\Exception $e) {
        // Log l'erreur de cache mais continuer normalement
        \Log::warning('Cache write error in DashboardDetailData: ' . $e->getMessage());
    }

    return $response;
}

/**
 * Récupérer les derniers films
 */
private function getLatestMovies()
{
    $cacheKey = 'latest_movies_data';
    
    try {
        // Essayer de récupérer depuis le cache
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            return $cachedData;
        }
    } catch (\Exception $e) {
        \Log::warning('Cache read error in getLatestMovies: ' . $e->getMessage());
    }
    
    // Générer les données
    $latestMovieIds = MobileSetting::getValueBySlug('latest-movies');
    $latestMovieIdsArray = json_decode($latestMovieIds, true);
    
    if (empty($latestMovieIdsArray)) {
        return collect();
    }
    
    $result = CommanResource::collection(
        Entertainment::whereIn('id', $latestMovieIdsArray)
            ->with(['entertainmentGenerMappings:id,entertainment_id,genre_id', 'entertainmentGenerMappings.genre:id,name'])
            ->where('status', 1)
            ->whereDate('release_date', '<=', Carbon::now())
            ->select(['id', 'name', 'type', 'movie_access', 'plan_id', 'description', 'trailer_url_type', 
                'release_date', 'is_restricted', 'language', 'IMDb_rating', 'content_rating', 
                'duration', 'trailer_url', 'video_upload_type', 'video_url_input', 'poster_url', 'thumbnail_url'])
            ->get()
    );
    
    // Essayer de mettre en cache
    try {
        Cache::put($cacheKey, $result, 3600);
    } catch (\Exception $e) {
        \Log::warning('Cache write error in getLatestMovies: ' . $e->getMessage());
    }
    
    return $result;
}

/**
 * Récupérer les langues populaires
 */
private function getPopularLanguages()
{
    $cacheKey = 'popular_languages_data';
    
    return Cache::remember($cacheKey, 3600, function () {
        $languageIds = MobileSetting::getValueBySlug('enjoy-in-your-native-tongue');
        $languageIdsArray = json_decode($languageIds, true);
        
        if (empty($languageIdsArray)) {
            return collect();
        }
        
        return Constant::whereIn('id', $languageIdsArray)
            ->select(['id', 'name', 'type', 'slug'])
            ->get();
    });
}

/**
 * Récupérer les films populaires
 */
private function getPopularMovies()
{
    $cacheKey = 'popular_movies_data';
    
    try {
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            return $cachedData;
        }
    } catch (\Exception $e) {
        \Log::warning('Cache read error in getPopularMovies: ' . $e->getMessage());
    }
    
    $popularMovieIds = MobileSetting::getValueBySlug('popular-movies');
    $popularMovieIdsArray = json_decode($popularMovieIds, true);
    
    if (empty($popularMovieIdsArray)) {
        return collect();
    }
    
    $result = CommanResource::collection(
        Entertainment::whereIn('id', $popularMovieIdsArray)
            ->with(['entertainmentGenerMappings:id,entertainment_id,genre_id', 'entertainmentGenerMappings.genre:id,name'])
            ->where('status', 1)
            ->whereDate('release_date', '<=', Carbon::now())
            ->select(['id', 'name', 'type', 'movie_access', 'plan_id', 'description', 'trailer_url_type', 
                'release_date', 'is_restricted', 'language', 'IMDb_rating', 'content_rating', 
                'duration', 'trailer_url', 'video_upload_type', 'video_url_input', 'poster_url', 'thumbnail_url'])
            ->get()
    );
    
    try {
        Cache::put($cacheKey, $result, 3600);
    } catch (\Exception $e) {
        \Log::warning('Cache write error in getPopularMovies: ' . $e->getMessage());
    }
    
    return $result;
}

/**
 * Récupérer les chaînes populaires
 */
private function getTopChannels()
{
    $cacheKey = 'top_channels_data';
    
    return Cache::remember($cacheKey, 3600, function () {
        $channelIds = MobileSetting::getValueBySlug('top-channels');
        $channelIdsArray = json_decode($channelIds, true);
        
        if (empty($channelIdsArray)) {
            return collect();
        }
        
        return LiveTvChannelResource::collection(
            LiveTvChannel::whereIn('id', $channelIdsArray)
                ->where('status', 1)
                ->select(['id', 'name', 'url', 'description', 'status', 'image_url'])
                ->get()
        );
    });
}

/**
 * Récupérer les personnalités
 */
private function getPersonalities()
{
    $cacheKey = 'personalities_data';
    
    return Cache::remember($cacheKey, 3600, function () {
        $castIds = MobileSetting::getValueBySlug('your-favorite-personality');
        $castIdsArray = json_decode($castIds, true);
        $personality = [];
        
        if (!empty($castIdsArray)) {
            $casts = CastCrew::whereIn('id', $castIdsArray)
                ->select(['id', 'name', 'type', 'file_url'])
                ->get();
                
            foreach ($casts as $value) {
                $personality[] = [
                    'id' => $value->id,
                    'name' => $value->name,
                    'type' => $value->type,
                    'profile_image' => setBaseUrlWithFileName($value->file_url),
                ];
            }
        }
        
        return $personality;
    });
}

/**
 * Récupérer les films gratuits
 */
private function getFreeMovies()
{
    $cacheKey = 'free_movies_data';
    
    return Cache::remember($cacheKey, 3600, function () {
        $movieIds = MobileSetting::getValueBySlug('500-free-movies');
        $movieIdsArray = json_decode($movieIds, true);
        
        if (empty($movieIdsArray)) {
            return collect();
        }
        
        return CommanResource::collection(
            Entertainment::whereIn('id', $movieIdsArray)
                ->with(['entertainmentGenerMappings:id,entertainment_id,genre_id', 'entertainmentGenerMappings.genre:id,name'])
                ->where('status', 1)
                ->whereDate('release_date', '<=', Carbon::now())
                ->select(['id', 'name', 'type', 'movie_access', 'plan_id', 'description', 'trailer_url_type', 
                    'release_date', 'is_restricted', 'language', 'IMDb_rating', 'content_rating', 
                    'duration', 'trailer_url', 'video_upload_type', 'video_url_input', 'poster_url', 'thumbnail_url'])
                ->get()
        );
    });
}

/**
 * Récupérer les genres
 */
private function getGenres()
{
    $cacheKey = 'genres_data';
    
    try {
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            return $cachedData;
        }
    } catch (\Exception $e) {
        \Log::warning('Cache read error in getGenres: ' . $e->getMessage());
    }
    
    $genreIds = MobileSetting::getValueBySlug('genre');
    $genreIdsArray = json_decode($genreIds, true);
    
    if (empty($genreIdsArray)) {
        return collect();
    }
    
    $result = GenresResource::collection(
        Genres::whereIn('id', $genreIdsArray)
            ->where('status', 1)
            ->select(['id', 'name', 'status', 'file_url'])
            ->get()
    );
    
    try {
        Cache::put($cacheKey, $result, 3600);
    } catch (\Exception $e) {
        \Log::warning('Cache write error in getGenres: ' . $e->getMessage());
    }
    
    return $result;
}

/**
 * Récupérer les émissions TV populaires
 */
private function getPopularTvShows()
{
    $cacheKey = 'popular_tvshows_data';
    
    try {
        $cachedData = Cache::get($cacheKey);
        if ($cachedData) {
            return $cachedData;
        }
    } catch (\Exception $e) {
        \Log::warning('Cache read error in getPopularTvShows: ' . $e->getMessage());
    }
    
    $popular_tvshowIds = MobileSetting::getValueBySlug('popular-tvshows');
    $popular_tvshowIdsArray = json_decode($popular_tvshowIds, true);
    
    if (empty($popular_tvshowIdsArray)) {
        return collect();
    }
    
    $result = CommanResource::collection(
        Entertainment::whereIn('id', $popular_tvshowIdsArray)
            ->with(['entertainmentGenerMappings:id,entertainment_id,genre_id', 'entertainmentGenerMappings.genre:id,name'])
            ->where('status', 1)
            ->select(['id', 'name', 'type', 'movie_access', 'plan_id', 'description', 'trailer_url_type', 
                'release_date', 'is_restricted', 'language', 'IMDb_rating', 'content_rating', 
                'duration', 'trailer_url', 'video_upload_type', 'video_url_input', 'poster_url', 'thumbnail_url'])
            ->get()
    );
    
    try {
        Cache::put($cacheKey, $result, 3600);
    } catch (\Exception $e) {
        \Log::warning('Cache write error in getPopularTvShows: ' . $e->getMessage());
    }
    
    return $result;
}

/**
 * Récupérer les vidéos populaires
 */
private function getPopularVideos()
{
    $cacheKey = 'popular_videos_data';
    
    return Cache::remember($cacheKey, 3600, function () {
        $videoIds = MobileSetting::getValueBySlug('popular-videos');
        $videoIdsArray = json_decode($videoIds, true);
        
        if (empty($videoIdsArray)) {
            return collect();
        }
        
        return VideoResource::collection(
            Video::whereIn('id', $videoIdsArray)
                ->where('status', 1)
                ->select(['id', 'title', 'description', 'video_upload_type', 'status', 'image', 'video', 'uploaded_by'])
                ->get()
        );
    });
}

/**
 * Récupérer les films tendance
 */
private function getTrandingMovies()
{
    $cacheKey = 'tranding_movies_data';
    
    return Cache::remember($cacheKey, 3600, function () {
        // Limiter à 20 films tendance au lieu de tous
        return CommanResource::collection(
            Entertainment::with([
                'entertainmentReviews' => function ($query) {
                    $query->whereBetween('rating', [4, 5])->take(6);
                }
            ])
            ->where('status', 1)
            ->where('type', 'movie')
            ->whereDate('release_date', '<=', Carbon::now())
            ->select(['id', 'name', 'type', 'movie_access', 'plan_id', 'description', 'trailer_url_type', 
                'release_date', 'is_restricted', 'language', 'IMDb_rating', 'content_rating', 
                'duration', 'trailer_url', 'video_upload_type', 'video_url_input', 'poster_url', 'thumbnail_url'])
            ->take(20)  // Limitation à 20 films
            ->get()
        );
    });
}

    public function getTrandingData(Request $request){


        if ($request->has('is_ajax') && $request->is_ajax == 1) {

            $popularMovieIds = MobileSetting::getValueBySlug(slug: 'popular-movies');
            $movieList = Entertainment::whereIn('id',json_decode($popularMovieIds))->where('status',1)->get();

            $html = '';
            if($request->has('section')&& $request->section == 'tranding_movie'){
                $movieData = (isenablemodule('movie') == 1) ? MoviesResource::collection($movieList) : [];
                if(!empty( $movieData)){

                    foreach( $movieData->toArray(request()) as $index => $movie){
                        $html .= view('frontend::components.card.card_entertainment',['value' => $movie])->render();
                    }
                }
            }


        return response()->json([
                'status' => true,
                'html' => $html,
                'message' => __('movie.tvshow_list'),
            ], 200);
        }



    }
}
