<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\CustomMessageNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Ya<PERSON>ra\DataTables\DataTables;

class CustomNotificationController extends Controller
{
    public function __construct()
    {
        // Page Title
        $this->module_title = 'Messages personnalisés';

        // module name
        $this->module_name = 'custom-notifications';

        // module icon
        $this->module_icon = 'fas fa-paper-plane';

        $this->middleware(['permission:view_notification'])->only('index');
        $this->middleware(['permission:add_notification'])->only('create', 'store');
    }

    /**
     * Afficher le formulaire d'envoi de message personnalisé
     */
    public function create()
    {
        $module_action = 'Envoyer un message';
        $users = User::where('status', 1)->get(['id', 'first_name', 'last_name', 'email']);
        
        return view('backend.custom-notifications.create', compact('module_action', 'users'));
    }

    /**
     * Envoyer le message personnalisé
     */
    public function store(Request $request)
    {
        // Nettoyer les données avant validation
        if ($request->recipient_type !== 'token') {
            $request->request->remove('test_token');
        }
        
        $rules = [
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'recipient_type' => 'required|in:all,token',
            'send_push' => 'boolean',
            'send_email' => 'boolean',
            'large_icon' => 'nullable|url',
            'big_image' => 'nullable|url',
            'action_url' => 'nullable|url',
        ];
        
        // Ajouter la validation du test_token seulement si nécessaire
        if ($request->recipient_type === 'token') {
            $rules['test_token'] = 'required|string';
        }
        
        $request->validate($rules);

        // Construire les données de notification
        $notificationData = [
            'title' => $request->title,
            'body' => $request->message
        ];

        // Ajouter l'image si fournie
        if ($request->big_image) {
            $notificationData['image'] = $request->big_image;
        }

        // Construire les données personnalisées
        $customData = [
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
            'type' => 'custom_notification'
        ];

        if ($request->action_url) {
            $customData['url'] = $request->action_url;
        }

        // Construire la structure FCM selon le type de destinataire
        $fcmData = [
            'message' => [
                'notification' => $notificationData,
                'data' => $customData,
                'android' => [
                    'notification' => [
                        'sound' => 'default',
                        'icon' => $request->large_icon ?? 'ic_notification',
                        'color' => '#FF6B35',
                        'channel_id' => 'voir_films_hd_channel'
                    ]
                ],
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'sound' => 'default',
                            'badge' => 1,
                            'category' => 'voir_films_hd'
                        ]
                    ]
                ]
            ]
        ];

        // Définir le destinataire (topic ou token)
        if ($request->recipient_type === 'token') {
            $fcmData['message']['token'] = $request->test_token;
            $recipientText = "votre appareil de test";
        } else {
            $fcmData['message']['topic'] = 'voir_films_hd';
            $recipientText = "le topic voir_films_hd";
        }

        // Log pour debug
        \Log::info('Envoi de notification personnalisée');
        \Log::info('Structure FCM:', $fcmData);

        // Envoyer la notification FCM
        $fcmResult = fcm($fcmData);

        if ($fcmResult) {
            // Enregistrer une trace dans la base pour l'historique
            if ($systemUser = \App\Models\User::find(1)) {
                $notificationData = [
            'title' => $request->title,
            'message' => $request->message,
                    'send_push' => true,
            'send_email' => $request->has('send_email'),
            'large_icon' => $request->large_icon,
            'big_image' => $request->big_image,
            'action_url' => $request->action_url,
                    'is_broadcast' => $request->recipient_type === 'all'
                ];
                
                \Illuminate\Support\Facades\Notification::send(
                    [$systemUser], 
                    new \App\Notifications\CustomMessageNotification($notificationData)
                );
            }
            
            $message = "Notification envoyée avec succès vers {$recipientText}";
            return redirect()->route('backend.custom-notifications.create')
                            ->with('success', $message);
        } else {
        return redirect()->route('backend.custom-notifications.create')
                            ->with('error', 'Erreur lors de l\'envoi de la notification');
        }
    }

    /**
     * Historique des messages envoyés
     */
    public function index()
    {
        $module_action = 'Historique des messages';
        
        return view('backend.custom-notifications.index', compact('module_action'));
    }

    /**
     * Données pour DataTable de l'historique
     */
    public function index_data(Request $request)
    {
        $notifications = \DB::table('notifications')
            ->where('type', 'App\\Notifications\\CustomMessageNotification')
            ->orderBy('created_at', 'desc')
            ->get();

        return DataTables::of($notifications)
            ->addColumn('title', function ($notification) {
                $data = json_decode($notification->data, true);
                return $data['title'] ?? 'Sans titre';
            })
            ->addColumn('message', function ($notification) {
                $data = json_decode($notification->data, true);
                return \Str::limit($data['message'] ?? '', 100);
            })
            ->addColumn('recipient', function ($notification) {
                $user = User::find($notification->notifiable_id);
                return $user ? $user->first_name . ' ' . $user->last_name : 'Utilisateur supprimé';
            })
            ->addColumn('channels', function ($notification) {
                $data = json_decode($notification->data, true);
                $channels = [];
                if ($data['send_push'] ?? false) $channels[] = 'Push';
                if ($data['send_email'] ?? false) $channels[] = 'Email';
                return implode(', ', $channels);
            })
            ->addColumn('extras', function ($notification) {
                $data = json_decode($notification->data, true);
                $extras = [];
                if (!empty($data['large_icon'])) $extras[] = 'Icône';
                if (!empty($data['big_image'])) $extras[] = 'Image';
                if (!empty($data['action_url'])) $extras[] = 'Lien';
                return implode(', ', $extras) ?: '-';
            })
            ->editColumn('created_at', function ($notification) {
                return \Carbon\Carbon::parse($notification->created_at)->format('d/m/Y H:i');
            })
            ->make(true);
    }

    /**
     * Envoyer une notification broadcast
     */
    private function sendBroadcastNotification($data, $type = 'topic', $token = null)
    {
        // Structure FCM simplifiée demandée
        $fcmData = [
            'message' => [
                'notification' => [
                    'title' => $data['title'],
                    'body' => $data['message']
                ],
                'data' => []
            ]
        ];

        // Ajouter l'URL d'action si présente
        if (!empty($data['action_url'])) {
            $fcmData['message']['data']['url'] = $data['action_url'];
        }

        // Définir le destinataire (topic ou token)
        if ($type === 'token' && $token) {
            $fcmData['message']['token'] = $token;
        } else {
            // Utiliser le topic voir_films_hd pour les notifications broadcast
            $fcmData['message']['topic'] = 'voir_films_hd';
        }

        // Ajouter les sections Android et APNS pour les images et options avancées
        $fcmData['message']['android'] = [
            'notification' => [
                'icon' => 'ic_notification',
                'color' => '#FF6B35',
                'sound' => 'default',
                'channel_id' => 'voir_films_hd_channel' // Ajout d'un canal de notification spécifique
            ]
        ];

        $fcmData['message']['apns'] = [
            'payload' => [
                'aps' => [
                    'sound' => 'default',
                    'badge' => 1,
                    'category' => 'voir_films_hd' // Ajout d'une catégorie pour iOS
                ]
            ]
        ];

        // Ajouter les images si présentes
        if (!empty($data['large_icon'])) {
            $fcmData['message']['android']['notification']['icon'] = $data['large_icon'];
        }

        if (!empty($data['big_image'])) {
            $fcmData['message']['android']['notification']['image'] = $data['big_image'];
        }

        // Log pour debug
        \Log::info('FCM Custom Notification Structure:', $fcmData);

        // Envoyer via FCM
        return fcm($fcmData);
    }
} 