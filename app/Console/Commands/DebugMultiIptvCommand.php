<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Entertainment\Services\MultiIptvService;
use Illuminate\Support\Facades\Log;

class DebugMultiIptvCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'multi-iptv:debug 
                           {tmdb_id : ID TMDB de la série à débuguer}
                           {--action=debug : Action à effectuer (debug|refresh|nocache|compare|clear-api)}
                           {--clear-cache : Vider le cache avant le test}
                           {--entertainment-id= : ID Entertainment spécifique pour vidage cache API}';

    /**
     * The console command description.
     */
    protected $description = 'Debug le service MultiIptv pour une série donnée';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $tmdbId = $this->argument('tmdb_id');
        $action = $this->option('action');
        $clearCache = $this->option('clear-cache');

        $this->info("🔍 Debug MultiIptvService pour TMDB ID: {$tmdbId}");
        $this->info("📅 " . now()->toDateTimeString());

        try {
            $service = new MultiIptvService();

            if (!$service->isEnabled()) {
                $this->error('❌ Service MultiIptv désactivé');
                $this->table(['Config', 'Valeur'], [
                    ['iptv_integration', config('entertainment.iptv_integration', false) ? 'true' : 'false'],
                    ['multi_iptv_enabled', config('entertainment.multi_iptv_enabled', false) ? 'true' : 'false'],
                    ['iptv_series_enabled', config('entertainment.iptv_series_enabled', true) ? 'true' : 'false']
                ]);
                return 1;
            }

            if ($clearCache) {
                $this->info('🗑️  Vidage du cache...');
                $service->clearCache($tmdbId, 'series');
            }

            switch ($action) {
                case 'debug':
                    $this->handleDebugAction($service, $tmdbId);
                    break;

                case 'refresh':
                    $this->handleRefreshAction($service, $tmdbId);
                    break;

                case 'nocache':
                    $this->handleNoCacheAction($service, $tmdbId);
                    break;

                case 'compare':
                    $this->handleCompareAction($service, $tmdbId);
                    break;

                case 'clear-api':
                    $this->handleClearApiAction($service, $tmdbId);
                    break;

                default:
                    $this->error("❌ Action '{$action}' non reconnue");
                    return 1;
            }

        } catch (\Exception $e) {
            $this->error('❌ Erreur: ' . $e->getMessage());
            if ($this->output->isVerbose()) {
                $this->line($e->getTraceAsString());
            }
            return 1;
        }

        return 0;
    }

    private function handleDebugAction($service, $tmdbId)
    {
        $this->info('🔍 Debug des sources brutes...');
        
        // Debug des sources
        $debugSources = $service->debugTvShowSources($tmdbId);
        
        $this->table(['Source', 'Connecté', 'Série trouvée', 'Saisons', 'Numéros saisons'], 
            collect($debugSources['sources'])->map(function($source, $name) {
                return [
                    $name,
                    $source['connected'] ? '✅' : '❌',
                    isset($source['series_found']) ? ($source['series_found'] ? '✅' : '❌') : 
                        (isset($source['poster_found']) ? ($source['poster_found'] ? '✅' : '❌') : '?'),
                    $source['seasons_count'] ?? '?',
                    isset($source['seasons']) ? implode(', ', $source['seasons']) : '?'
                ];
            })->toArray()
        );

        // Données avec cache
        $this->info('📦 Récupération des données avec cache...');
        $cachedData = $service->getTvShowData($tmdbId);
        
        if ($cachedData) {
            $this->info("✅ {" . count($cachedData) . "} saisons trouvées");
            
            // Chercher la saison 3
            $season3 = null;
            foreach ($cachedData as $season) {
                if ($season['season_number'] == 3) {
                    $season3 = $season;
                    break;
                }
            }
            
            if ($season3) {
                $this->info("✅ Saison 3 trouvée !");
                $this->table(['Propriété', 'Valeur'], [
                    ['Source IPTV', $season3['iptv_source']],
                    ['Épisodes', count($season3['episodes'])],
                    ['ID source', $season3['iptv_source_id']]
                ]);
            } else {
                $this->warn("⚠️  Saison 3 NOT FOUND dans les données finales");
                $this->info("Saisons disponibles: " . collect($cachedData)->pluck('season_number')->implode(', '));
            }
        } else {
            $this->error("❌ Aucune donnée trouvée pour cette série");
        }
    }

    private function handleRefreshAction($service, $tmdbId)
    {
        $this->info('🔄 Rafraîchissement des données (vidage cache + rechargement)...');
        
        $newData = $service->refreshTvShowData($tmdbId);
        
        if ($newData) {
            $this->info("✅ Données rafraîchies: " . count($newData) . " saisons");
            $seasons = collect($newData)->pluck('season_number')->sort()->implode(', ');
            $this->info("Saisons disponibles: {$seasons}");
            
            // Vérifier saison 3
            $season3Found = collect($newData)->firstWhere('season_number', 3);
            if ($season3Found) {
                $this->info("✅ Saison 3 maintenant disponible ! (Source: {$season3Found['iptv_source']})");
            } else {
                $this->warn("⚠️  Saison 3 toujours manquante après rafraîchissement");
            }
        } else {
            $this->error("❌ Aucune donnée après rafraîchissement");
        }
    }

    private function handleNoCacheAction($service, $tmdbId)
    {
        $this->info('🚫 Récupération des données sans cache...');
        
        $freshData = $service->getTvShowDataNoCache($tmdbId);
        
        if ($freshData) {
            $this->info("✅ Données fraîches: " . count($freshData) . " saisons");
            $seasons = collect($freshData)->pluck('season_number')->sort()->implode(', ');
            $this->info("Saisons disponibles: {$seasons}");
            
            // Vérifier saison 3
            $season3Found = collect($freshData)->firstWhere('season_number', 3);
            if ($season3Found) {
                $this->info("✅ Saison 3 trouvée dans les données fraîches ! (Source: {$season3Found['iptv_source']})");
            } else {
                $this->warn("⚠️  Saison 3 manquante même dans les données fraîches");
            }
        } else {
            $this->error("❌ Aucune donnée fraîche trouvée");
        }
    }

    private function handleCompareAction($service, $tmdbId)
    {
        $this->info('🔄 Comparaison cache vs données fraîches...');
        
        $cachedData = $service->getTvShowData($tmdbId);
        $freshData = $service->getTvShowDataNoCache($tmdbId);
        
        $cachedCount = $cachedData ? count($cachedData) : 0;
        $freshCount = $freshData ? count($freshData) : 0;
        $difference = $freshCount - $cachedCount;
        
        $this->table(['Type', 'Saisons'], [
            ['Cache', $cachedCount],
            ['Fraîches', $freshCount],
            ['Différence', $difference]
        ]);
        
        if ($difference > 0) {
            $this->warn("⚠️  Les données fraîches ont {$difference} saison(s) de plus - problème de cache détecté !");
            $this->info("💡 Recommendation: Utilisez --clear-cache ou action=refresh");
            
            // Trouver les saisons manquantes
            $cachedSeasons = $cachedData ? collect($cachedData)->pluck('season_number')->toArray() : [];
            $freshSeasons = $freshData ? collect($freshData)->pluck('season_number')->toArray() : [];
            $missingSeasons = array_diff($freshSeasons, $cachedSeasons);
            
            if ($missingSeasons) {
                $this->info("Saisons manquantes dans le cache: " . implode(', ', $missingSeasons));
            }
            
        } elseif ($difference < 0) {
            $this->error("❌ Les données fraîches ont moins de saisons - problème de base de données");
        } else {
            $this->info("✅ Aucune différence détectée");
        }
    }

    private function handleClearApiAction($service, $tmdbId)
    {
        $this->info('🗑️  Vidage du cache API...');
        
        $entertainmentId = $this->option('entertainment-id');
        
        if ($entertainmentId) {
            // Vidage direct par Entertainment ID
            $this->info("Vidage cache API pour Entertainment ID: {$entertainmentId}");
            $cleared = $service->clearApiCacheByEntertainmentId($entertainmentId);
            $this->info("✅ {$cleared} clés de cache API vidées");
        } else {
            // Récupérer l'Entertainment ID depuis TMDB ID
            $entertainment = \Modules\Entertainment\Models\Entertainment::where('tmdb_id', $tmdbId)
                ->where('type', 'tvshow')
                ->first(['id', 'name']);
                
            if ($entertainment) {
                $this->info("Entertainment trouvé: {$entertainment->name} (ID: {$entertainment->id})");
                $cleared = $service->clearApiCacheByEntertainmentId($entertainment->id);
                $this->info("✅ {$cleared} clés de cache API vidées");
            } else {
                $this->error("❌ Aucun Entertainment trouvé pour TMDB ID: {$tmdbId}");
                return;
            }
        }
        
        $this->info("💡 Le cache API a été vidé. Testez maintenant votre endpoint api/tvshow-details");
    }
} 