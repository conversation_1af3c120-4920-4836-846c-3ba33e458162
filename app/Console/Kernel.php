<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Illuminate\Http\Middleware\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api' => [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array<string, class-string|string>
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'auth.session' => \Illuminate\Session\Middleware\AuthenticateSession::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \App\Http\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'check.api.key' => \App\Http\Middleware\CheckApiKey::class,
    ];

    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\CleanupExpiredTokens::class,
        Commands\GenerateStreamToken::class,
        Commands\UpdateIptvFlagsCommand::class,
        Commands\DebugMultiIptvCommand::class,
        'Modules\Subscriptions\Console\Commands\CheckSubscription',
        \App\Console\Commands\ConvertImdbToTmdb::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('queue:work --tries=3 --stop-when-empty')->withoutOverlapping();

        $schedule->command('migrate:fresh --seed')->hourlyAt(2);

        // Nettoyer les tokens expirés selon TOKEN_TIMESPAN
        $schedule->command('tokens:cleanup')->cron('*/' . env('TOKEN_TIMESPAN', 5) . ' * * * *');
        
        // Récupérer la durée en minutes depuis .env
        $tokenTimespan = env('TOKEN_TIMESPAN', 5);
        
        // Programmer la génération du token à un intervalle légèrement inférieur à la durée d'expiration
        // pour garantir qu'un nouveau token est toujours disponible
        $interval = max(1, $tokenTimespan - 1); // Au moins 1 minute avant expiration
        
        $schedule->command('token:generate')
                ->everyMinute()
                ->when(function () {
                    // Vérifier si un token existe et s'il expire bientôt (dans la prochaine minute)
                    $tokenService = app(\App\Services\LocalTokenService::class);
                    $currentToken = $tokenService->getCurrentGlobalToken();
                    
                    if (!$currentToken) {
                        // Aucun token, il faut en générer un
                        return true;
                    }
                    
                    // Générer un nouveau token si l'actuel expire dans moins d'une minute
                    return ($currentToken['expires'] - time()) < 60;
                })
                ->withoutOverlapping();
        
        // Mettre à jour les drapeaux IPTV toutes les 12 heures
        $schedule->command('iptv:update-flags --limit=200')->twiceDaily(2, 14)->withoutOverlapping();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
