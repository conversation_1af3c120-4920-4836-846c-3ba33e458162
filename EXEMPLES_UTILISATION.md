# 🎬 Cas d'utilisation concrets des APIs Films et Séries

## 📊 **1. Films par Genre**

### Action (Genre ID: 28)
```bash
GET /api/movie-list?content_type=movie&genre_id=28&per_page=20
```
**Résultat :** Liste des films d'action

### Comédie (Genre ID: 35)
```bash
GET /api/movie-list?content_type=movie&genre_id=35&per_page=15
```
**Résultat :** Films de comédie

### Science-Fiction (Genre ID: 878)
```bash
GET /api/movie-list?content_type=movie&genre_id=878&min_imdb_rating=7.0&per_page=10
```
**Résultat :** Films de science-fiction avec bonne note

---

## 📅 **2. Films par Année**

### Films de 2023
```bash
GET /api/movie-list?content_type=movie&release_year=2023&per_page=25
```
**Résultat :** Tous les films sortis en 2023

### Films récents de qualité (2022-2023)
```bash
# Films 2023
GET /api/movie-list?content_type=movie&release_year=2023&min_imdb_rating=7.5&sort_by_views=desc

# Films 2022
GET /api/movie-list?content_type=movie&release_year=2022&min_imdb_rating=7.5&sort_by_views=desc
```

### Films classiques (2010)
```bash
GET /api/movie-list?content_type=movie&release_year=2010&min_imdb_rating=8.0&sort_by_views=desc
```
**Résultat :** Films classiques de 2010 avec excellente note

---

## 👀 **3. Films par Popularité (Vues)**

### Films les plus regardés (toutes catégories)
```bash
GET /api/movie-list?content_type=movie&sort_by_views=desc&per_page=20
```
**Résultat :** Top 20 des films les plus vus

### Films populaires de qualité
```bash
GET /api/movie-list?content_type=movie&min_imdb_rating=7.0&sort_by_views=desc&per_page=15
```
**Résultat :** Films populaires avec bonne note

### Action les plus regardés
```bash
GET /api/movie-list?content_type=movie&genre_id=28&sort_by_views=desc&per_page=10
```
**Résultat :** Top 10 des films d'action les plus vus

---

## ⭐ **4. Films par Rating IMDB**

### Films excellents (8.0+)
```bash
GET /api/movie-list?content_type=movie&min_imdb_rating=8.0&sort_by_views=desc&per_page=20
```
**Résultat :** Films avec note exceptionnelle

### Films très bons (7.5+)
```bash
GET /api/movie-list?content_type=movie&min_imdb_rating=7.5&per_page=30
```
**Résultat :** Films avec très bonne note

### Films récents de qualité
```bash
GET /api/movie-list?content_type=movie&release_year=2023&min_imdb_rating=7.5&sort_by_views=desc
```
**Résultat :** Films 2023 avec excellente note triés par popularité

---

## 🎯 **5. Combinaisons Avancées**

### Films d'action 2023 populaires et bien notés
```bash
GET /api/movie-list?content_type=movie&genre_id=28&release_year=2023&min_imdb_rating=7.0&sort_by_views=desc&per_page=10
```
**Usage :** Page d'accueil "Films d'action récents et populaires"

### Comédies classiques très bien notées
```bash
GET /api/movie-list?content_type=movie&genre_id=35&release_year=2015&min_imdb_rating=8.0&sort_by_views=desc
```
**Usage :** Section "Comédies classiques recommandées"

### Blockbusters récents
```bash
GET /api/movie-list?content_type=movie&release_year=2023&min_imdb_rating=7.5&sort_by_views=desc&per_page=5
```
**Usage :** Slider "Blockbusters du moment"

---

## 📺 **6. Séries TV - Cas d'utilisation**

### Séries populaires actuelles
```bash
GET /api/tvshow-list?content_type=tvshow&min_imdb_rating=7.5&sort_by_views=desc&per_page=15
```

### Séries d'action bien notées
```bash
GET /api/tvshow-list?content_type=tvshow&genre_id=28&min_imdb_rating=7.0&sort_by_views=desc
```

### Nouvelles séries 2023
```bash
GET /api/tvshow-list?content_type=tvshow&release_year=2023&sort_by_views=desc&per_page=20
```

---

## 🏆 **7. Sections recommandées pour votre site**

### Page d'accueil
```bash
# Hero Section - Top 5 films populaires
GET /api/movie-list?content_type=movie&min_imdb_rating=8.0&sort_by_views=desc&per_page=5

# Nouveautés 2023
GET /api/movie-list?content_type=movie&release_year=2023&sort_by_views=desc&per_page=10

# Films les mieux notés
GET /api/movie-list?content_type=movie&min_imdb_rating=8.5&per_page=8
```

### Page Genre Action
```bash
# Action populaires
GET /api/movie-list?content_type=movie&genre_id=28&sort_by_views=desc&per_page=20

# Action récents et bien notés
GET /api/movie-list?content_type=movie&genre_id=28&release_year=2023&min_imdb_rating=7.0&sort_by_views=desc
```

### Page "Tendances"
```bash
# Films tendance
GET /api/movie-list?content_type=movie&sort_by_views=desc&per_page=30

# Séries tendance
GET /api/tvshow-list?content_type=tvshow&sort_by_views=desc&per_page=20
```

---

## 🔧 **8. Paramètres pour différents contextes**

### Mobile (moins d'éléments)
```bash
GET /api/movie-list?content_type=movie&sort_by_views=desc&per_page=10
```

### Desktop (plus d'éléments)
```bash
GET /api/movie-list?content_type=movie&sort_by_views=desc&per_page=24
```

### Widget "Recommandations"
```bash
GET /api/movie-list?content_type=movie&min_imdb_rating=7.5&sort_by_views=desc&per_page=6
```

---

## 📈 **9. Analytics et Insights**

### Films les plus populaires par genre
```bash
# Action
GET /api/movie-list?content_type=movie&genre_id=28&sort_by_views=desc&per_page=5

# Comédie  
GET /api/movie-list?content_type=movie&genre_id=35&sort_by_views=desc&per_page=5

# Drame
GET /api/movie-list?content_type=movie&genre_id=18&sort_by_views=desc&per_page=5
```

### Évolution par année
```bash
# Comparer 2022 vs 2023
GET /api/movie-list?content_type=movie&release_year=2023&sort_by_views=desc&per_page=10
GET /api/movie-list?content_type=movie&release_year=2022&sort_by_views=desc&per_page=10
```

---

## 💡 **10. Tips d'implémentation**

### Mise en cache côté frontend
```javascript
// Cache pour 30 minutes
const cacheKey = `movies_${genre}_${year}_${rating}_${sort}`;
localStorage.setItem(cacheKey, JSON.stringify(data));
```

### Pagination infinie
```javascript
// Page suivante
GET /api/movie-list?content_type=movie&sort_by_views=desc&per_page=10&page=2
```

### Recherche combinée
```javascript
// Recherche + filtres
GET /api/movie-list?content_type=movie&search=marvel&genre_id=28&min_imdb_rating=7.0&sort_by_views=desc
``` 