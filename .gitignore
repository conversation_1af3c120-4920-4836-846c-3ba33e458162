node_modules
/public/hot
/public/storage
/storage/*.key
/vendor
.env
stre_streamit_db.sql
.env.backup
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/.idea
/.vscode
*.cache
.DS_Store


# Below Comments For Design Style & Scripts
# /public/css/*
# !/public/css/.gitkeep
# /public/js/core/*
# !/public/js/core/.gitkeep

# /public/js/*.js*
# !/public/js/.gitkeep

# /public/js/iqonic-script/*.min.js*
# !/public/js/iqonic-script/.gitkeep
# !/public/js/setting-init.js

# /public/modules/**/*
# !/public/modules/.gitkeep
# /public/mix-manifest.json
node_modules
node_modules
/storage/media-library/**/*
_ide_helper.php





copy_iptv_data.php
copy_iptv_data_cli.php
create_iptv_db2_structure.php
create_iptv_db2_structure_cli.php
modify_iptv_data_for_test.php
modify_iptv_data_for_test_cli.php
test_multi_iptv_cli.php




public/debug_assembledata.php
public/debug_closure_context.php
public/debug_environment.php
public/debug_gettvshowdata.php
public/debug_iptv_db2.php
public/debug_simple_getseries.php
public/debug_step_by_step.php
public/debug_test_connection.php
public/debug_test_connection_simple.php

public/vod/data_download/
public/vod/temp/
*.m3u