<?php

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Test MultiIptvService avec fusion Standard ===\n\n";

try {
    // Test du MultiIptvService
    $service = app(Modules\Entertainment\Services\MultiIptvService::class);

    // Test avec un ID TMDB d'exemple
    $tmdbId = '117581'; // Game of Thrones

    // Test des statistiques
    echo "1. Statistiques des bases de données :\n";
    $stats = $service->getStats();
    foreach ($stats as $dbName => $stat) {
        echo "  - {$dbName}: ";
        if ($stat['connected']) {
            echo "✅ Connecté - {$stat['movies']} films, {$stat['series']} séries ({$stat['type']})\n";
        } else {
            echo "❌ Erreur: {$stat['error']}\n";
        }
    }

    echo "\n2. Test de récupération de série (TMDB ID: {$tmdbId}) :\n";

    // Vérifier d'abord si le service est activé
    if (!$service->isEnabled()) {
        echo "⚠️  Service MultiIptv désactivé dans la configuration\n";
        echo "   Vérifiez config('entertainment.iptv_integration') et config('entertainment.multi_iptv_enabled')\n";
    }

    // Test de récupération des données
    $seriesData = $service->getTvShowData($tmdbId);

    if ($seriesData) {
        echo "✅ Série trouvée avec " . count($seriesData) . " saisons :\n";
        
        foreach ($seriesData as $season) {
            $iptvSource = $season['iptv_source'] ?? 'unknown';
            $iptvFlag = $season['is_iptv'] ? '📺' : '🎬';
            
            echo "  {$iptvFlag} Saison {$season['season_number']} ({$iptvSource}) - {$season['total_episodes']} épisodes\n";
            
            // Afficher quelques épisodes
            $episodeCount = min(3, count($season['episodes']));
            for ($i = 0; $i < $episodeCount; $i++) {
                $episode = $season['episodes'][$i];
                $sourceCount = count($episode['sources']);
                echo "    - Episode {$episode['episode_number']}: {$sourceCount} sources\n";
            }
            
            if (count($season['episodes']) > 3) {
                echo "    - ... et " . (count($season['episodes']) - 3) . " autres épisodes\n";
            }
        }
    } else {
        echo "❌ Aucune série trouvée\n";
    }

    echo "\n3. Test du cache :\n";
    echo "  - Vider le cache...\n";
    $service->clearCache($tmdbId, 'series');
    echo "  - ✅ Cache vidé\n";

    echo "\n4. Test de re-récupération (depuis cache) :\n";
    $start = microtime(true);
    $seriesData2 = $service->getTvShowData($tmdbId);
    $end = microtime(true);
    $duration = round(($end - $start) * 1000, 2);

    if ($seriesData2) {
        echo "✅ Série re-récupérée en {$duration}ms avec " . count($seriesData2) . " saisons\n";
    } else {
        echo "❌ Échec de re-récupération\n";
    }

} catch (Exception $e) {
    echo "❌ Erreur lors du test: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Test terminé ===\n"; 